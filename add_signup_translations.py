#!/usr/bin/env python3
"""
Script to add signup form translations to all language ARB files
"""

import json
import os

# Translation data for signup form
signup_translations = {
    'as': {  # Assamese
        'form_first_name_required': 'প্ৰথম নাম *',
        'form_first_name_hint': 'আপোনাৰ প্ৰথম নাম লিখক',
        'form_middle_name_optional': 'মধ্য নাম (ঐচ্ছিক)',
        'form_middle_name_hint': 'আপোনাৰ মধ্য নাম লিখক',
        'form_last_name_required': 'শেষ নাম *',
        'form_last_name_hint': 'আপোনাৰ শেষ নাম লিখক',
        'form_whatsapp_number': 'হোৱাটছএপ নম্বৰ',
        'form_whatsapp_hint': '১০ সংখ্যাৰ হোৱাটছএপ নম্বৰ লিখক',
        'form_secondary_phone_optional': 'দ্বিতীয় ফোন নম্বৰ (ঐচ্ছিক)',
        'form_secondary_phone_hint': '১০ সংখ্যাৰ দ্বিতীয় ফোন নম্বৰ লিখক',
        'btn_no_email': 'মোৰ কোনো ইমেইল নাই',
        'btn_request_signup': 'চাইন আপৰ বাবে অনুৰোধ',
        'btn_already_have_account': 'ইতিমধ্যে একাউণ্ট আছে? লগইন কৰক',
        'text_whatsapp_same_as_phone': 'হোৱাটছএপ নম্বৰ ফোন নম্বৰৰ দৰেই',
        'text_use_same_whatsapp': 'হোৱাটছএপৰ বাবে একেটা নম্বৰ ব্যৱহাৰ কৰিবনে?',
        'text_verified': 'সত্যাপিত',
        'msg_submitting_data': 'ডেটা জমা দিয়া হৈছে। অনুগ্ৰহ কৰি অপেক্ষা কৰক!',
        'msg_success': 'সফল',
        'msg_error': 'ত্ৰুটি',
        'msg_form_incomplete': 'ফৰ্ম অসম্পূৰ্ণ',
        'msg_complete_required_fields': 'অনুগ্ৰহ কৰি সকলো প্ৰয়োজনীয় ক্ষেত্ৰ সঠিকভাৱে পূৰণ কৰক',
        'msg_information': 'তথ্য',
        'msg_complete_fields_order': 'অনুগ্ৰহ কৰি সকলো ক্ষেত্ৰ ক্ৰমানুসাৰে পূৰণ কৰক। প্ৰতিটো ক্ষেত্ৰ কেৱল তেতিয়াহে সক্ৰিয় হ\'ব যেতিয়া পূৰ্ববৰ্তী ক্ষেত্ৰটো সঠিকভাৱে পূৰণ আৰু সত্যাপিত কৰা হ\'ব।',
        'error_enter_first_name': 'অনুগ্ৰহ কৰি আপোনাৰ প্ৰথম নাম লিখক',
        'error_enter_last_name': 'অনুগ্ৰহ কৰি আপোনাৰ শেষ নাম লিখক',
        'error_enter_whatsapp': 'অনুগ্ৰহ কৰি হোৱাটছএপ নম্বৰ লিখক',
        'error_whatsapp_10_digits': 'হোৱাটছএপ নম্বৰ অৱশ্যেই ঠিক ১০ সংখ্যাৰ হ\'ব লাগিব',
        'error_enter_only_numbers': 'অনুগ্ৰহ কৰি কেৱল সংখ্যা লিখক',
        'error_secondary_phone_10_digits': 'দ্বিতীয় ফোন নম্বৰ অৱশ্যেই ঠিক ১০ সংখ্যাৰ হ\'ব লাগিব',
        'error_phone_numbers_different': 'ফোন নম্বৰ আৰু দ্বিতীয় ফোন নম্বৰ অৱশ্যেই বেলেগ হ\'ব লাগিব',
        'error_request_error': 'অনুৰোধ ত্ৰুটি',
        'error_phone_number': 'ফোন নম্বৰ',
        'error_whatsapp_number': 'হোৱাটছএপ নম্বৰ',
        'error_email_id': 'ইমেইল আইডি',
        'error_emp_number': 'কৰ্মচাৰী নম্বৰ',
        'error_update_marked_info': 'অনুগ্ৰহ কৰি ৰঙা ক্ৰছ দিয়ে চিহ্নিত তথ্য আপডেট কৰক আৰু আকৌ চেষ্টা কৰক।',
        'status_new': 'নতুন',
        'status_already_taken': 'ইতিমধ্যে লোৱা হৈছে',
        'status_already_requested': 'ইতিমধ্যে অনুৰোধ কৰা হৈছে',
        'status_unknown': 'অজ্ঞাত'
    },
    'pa': {  # Punjabi
        'form_first_name_required': 'ਪਹਿਲਾ ਨਾਮ *',
        'form_first_name_hint': 'ਆਪਣਾ ਪਹਿਲਾ ਨਾਮ ਦਰਜ ਕਰੋ',
        'form_middle_name_optional': 'ਮੱਧ ਨਾਮ (ਵਿਕਲਪਿਕ)',
        'form_middle_name_hint': 'ਆਪਣਾ ਮੱਧ ਨਾਮ ਦਰਜ ਕਰੋ',
        'form_last_name_required': 'ਅੰਤਿਮ ਨਾਮ *',
        'form_last_name_hint': 'ਆਪਣਾ ਅੰਤਿਮ ਨਾਮ ਦਰਜ ਕਰੋ',
        'form_whatsapp_number': 'ਵਟਸਐਪ ਨੰਬਰ',
        'form_whatsapp_hint': '10 ਅੰਕਾਂ ਦਾ ਵਟਸਐਪ ਨੰਬਰ ਦਰਜ ਕਰੋ',
        'form_secondary_phone_optional': 'ਦੂਜਾ ਫੋਨ ਨੰਬਰ (ਵਿਕਲਪਿਕ)',
        'form_secondary_phone_hint': '10 ਅੰਕਾਂ ਦਾ ਦੂਜਾ ਫੋਨ ਨੰਬਰ ਦਰਜ ਕਰੋ',
        'btn_no_email': 'ਮੇਰੇ ਕੋਲ ਈਮੇਲ ਨਹੀਂ ਹੈ',
        'btn_request_signup': 'ਸਾਈਨ ਅੱਪ ਲਈ ਬੇਨਤੀ',
        'btn_already_have_account': 'ਪਹਿਲਾਂ ਤੋਂ ਖਾਤਾ ਹੈ? ਲਾਗਇਨ ਕਰੋ',
        'text_whatsapp_same_as_phone': 'ਵਟਸਐਪ ਨੰਬਰ ਫੋਨ ਨੰਬਰ ਦੇ ਸਮਾਨ ਹੈ',
        'text_use_same_whatsapp': 'ਵਟਸਐਪ ਲਈ ਉਹੀ ਨੰਬਰ ਵਰਤੋ?',
        'text_verified': 'ਤਸਦੀਕ ਸ਼ੁਦਾ',
        'msg_submitting_data': 'ਡੇਟਾ ਜਮ੍ਹਾਂ ਕੀਤਾ ਜਾ ਰਿਹਾ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਇੰਤਜ਼ਾਰ ਕਰੋ!',
        'msg_success': 'ਸਫਲਤਾ',
        'msg_error': 'ਗਲਤੀ',
        'msg_form_incomplete': 'ਫਾਰਮ ਅਧੂਰਾ',
        'msg_complete_required_fields': 'ਕਿਰਪਾ ਕਰਕੇ ਸਾਰੇ ਲੋੜੀਂਦੇ ਖੇਤਰ ਸਹੀ ਤਰੀਕੇ ਨਾਲ ਭਰੋ',
        'msg_information': 'ਜਾਣਕਾਰੀ',
        'msg_complete_fields_order': 'ਕਿਰਪਾ ਕਰਕੇ ਸਾਰੇ ਖੇਤਰ ਕ੍ਰਮ ਵਿੱਚ ਭਰੋ। ਹਰ ਖੇਤਰ ਤਾਂ ਹੀ ਸਮਰੱਥ ਹੋਵੇਗਾ ਜਦੋਂ ਪਿਛਲਾ ਖੇਤਰ ਸਹੀ ਤਰੀਕੇ ਨਾਲ ਭਰਿਆ ਅਤੇ ਤਸਦੀਕ ਕੀਤਾ ਜਾਵੇ।',
        'error_enter_first_name': 'ਕਿਰਪਾ ਕਰਕੇ ਆਪਣਾ ਪਹਿਲਾ ਨਾਮ ਦਰਜ ਕਰੋ',
        'error_enter_last_name': 'ਕਿਰਪਾ ਕਰਕੇ ਆਪਣਾ ਅੰਤਿਮ ਨਾਮ ਦਰਜ ਕਰੋ',
        'error_enter_whatsapp': 'ਕਿਰਪਾ ਕਰਕੇ ਵਟਸਐਪ ਨੰਬਰ ਦਰਜ ਕਰੋ',
        'error_whatsapp_10_digits': 'ਵਟਸਐਪ ਨੰਬਰ ਬਿਲਕੁਲ 10 ਅੰਕਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ',
        'error_enter_only_numbers': 'ਕਿਰਪਾ ਕਰਕੇ ਸਿਰਫ ਨੰਬਰ ਦਰਜ ਕਰੋ',
        'error_secondary_phone_10_digits': 'ਦੂਜਾ ਫੋਨ ਨੰਬਰ ਬਿਲਕੁਲ 10 ਅੰਕਾਂ ਦਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ',
        'error_phone_numbers_different': 'ਫੋਨ ਨੰਬਰ ਅਤੇ ਦੂਜਾ ਫੋਨ ਨੰਬਰ ਵੱਖਰੇ ਹੋਣੇ ਚਾਹੀਦੇ ਹਨ',
        'error_request_error': 'ਬੇਨਤੀ ਗਲਤੀ',
        'error_phone_number': 'ਫੋਨ ਨੰਬਰ',
        'error_whatsapp_number': 'ਵਟਸਐਪ ਨੰਬਰ',
        'error_email_id': 'ਈਮੇਲ ਆਈਡੀ',
        'error_emp_number': 'ਕਰਮਚਾਰੀ ਨੰਬਰ',
        'error_update_marked_info': 'ਕਿਰਪਾ ਕਰਕੇ ਲਾਲ ਕਰਾਸ ਨਾਲ ਚਿਹਨਿਤ ਜਾਣਕਾਰੀ ਨੂੰ ਅਪਡੇਟ ਕਰੋ ਅਤੇ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ।',
        'status_new': 'ਨਵਾਂ',
        'status_already_taken': 'ਪਹਿਲਾਂ ਤੋਂ ਲਿਆ ਗਿਆ',
        'status_already_requested': 'ਪਹਿਲਾਂ ਤੋਂ ਬੇਨਤੀ ਕੀਤੀ ਗਈ',
        'status_unknown': 'ਅਣਜਾਣ'
    },
    'mr': {  # Marathi
        'form_first_name_required': 'पहिले नाव *',
        'form_first_name_hint': 'तुमचे पहिले नाव टाका',
        'form_middle_name_optional': 'मधले नाव (पर्यायी)',
        'form_middle_name_hint': 'तुमचे मधले नाव टाका',
        'form_last_name_required': 'आडनाव *',
        'form_last_name_hint': 'तुमचे आडनाव टाका',
        'form_whatsapp_number': 'व्हाट्सअप नंबर',
        'form_whatsapp_hint': '10 अंकी व्हाट्सअप नंबर टाका',
        'form_secondary_phone_optional': 'दुसरा फोन नंबर (पर्यायी)',
        'form_secondary_phone_hint': '10 अंकी दुसरा फोन नंबर टाका',
        'btn_no_email': 'माझ्याकडे ईमेल नाही',
        'btn_request_signup': 'साइन अप साठी विनंती',
        'btn_already_have_account': 'आधीच खाते आहे? लॉगिन करा',
        'text_whatsapp_same_as_phone': 'व्हाट्सअप नंबर फोन नंबर सारखाच आहे',
        'text_use_same_whatsapp': 'व्हाट्सअप साठी तोच नंबर वापरायचा?',
        'text_verified': 'सत्यापित',
        'msg_submitting_data': 'डेटा सबमिट करत आहे. कृपया प्रतीक्षा करा!',
        'msg_success': 'यशस्वी',
        'msg_error': 'त्रुटी',
        'msg_form_incomplete': 'फॉर्म अपूर्ण',
        'msg_complete_required_fields': 'कृपया सर्व आवश्यक फील्ड योग्यरित्या भरा',
        'msg_information': 'माहिती',
        'msg_complete_fields_order': 'कृपया सर्व फील्ड क्रमाने भरा. प्रत्येक फील्ड तेव्हाच सक्षम होईल जेव्हा मागील फील्ड योग्यरित्या भरले आणि सत्यापित केले जाईल.',
        'error_enter_first_name': 'कृपया तुमचे पहिले नाव टाका',
        'error_enter_last_name': 'कृपया तुमचे आडनाव टाका',
        'error_enter_whatsapp': 'कृपया व्हाट्सअप नंबर टाका',
        'error_whatsapp_10_digits': 'व्हाट्सअप नंबर नक्की 10 अंकांचा असावा',
        'error_enter_only_numbers': 'कृपया फक्त संख्या टाका',
        'error_secondary_phone_10_digits': 'दुसरा फोन नंबर नक्की 10 अंकांचा असावा',
        'error_phone_numbers_different': 'फोन नंबर आणि दुसरा फोन नंबर वेगळे असावेत',
        'error_request_error': 'विनंती त्रुटी',
        'error_phone_number': 'फोन नंबर',
        'error_whatsapp_number': 'व्हाट्सअप नंबर',
        'error_email_id': 'ईमेल आयडी',
        'error_emp_number': 'कर्मचारी नंबर',
        'error_update_marked_info': 'कृपया लाल क्रॉसने चिन्हांकित माहिती अपडेट करा आणि पुन्हा प्रयत्न करा.',
        'status_new': 'नवीन',
        'status_already_taken': 'आधीच घेतले',
        'status_already_requested': 'आधीच विनंती केली',
        'status_unknown': 'अज्ञात'
    },
    'kn': {  # Kannada
        'form_first_name_required': 'ಮೊದಲ ಹೆಸರು *',
        'form_first_name_hint': 'ನಿಮ್ಮ ಮೊದಲ ಹೆಸರನ್ನು ನಮೂದಿಸಿ',
        'form_middle_name_optional': 'ಮಧ್ಯ ಹೆಸರು (ಐಚ್ಛಿಕ)',
        'form_middle_name_hint': 'ನಿಮ್ಮ ಮಧ್ಯ ಹೆಸರನ್ನು ನಮೂದಿಸಿ',
        'form_last_name_required': 'ಕೊನೆಯ ಹೆಸರು *',
        'form_last_name_hint': 'ನಿಮ್ಮ ಕೊನೆಯ ಹೆಸರನ್ನು ನಮೂದಿಸಿ',
        'form_whatsapp_number': 'ವಾಟ್ಸ್‌ಆಪ್ ಸಂಖ್ಯೆ',
        'form_whatsapp_hint': '10 ಅಂಕಿಗಳ ವಾಟ್ಸ್‌ಆಪ್ ಸಂಖ್ಯೆಯನ್ನು ನಮೂದಿಸಿ',
        'form_secondary_phone_optional': 'ದ್ವಿತೀಯ ಫೋನ್ ಸಂಖ್ಯೆ (ಐಚ್ಛಿಕ)',
        'form_secondary_phone_hint': '10 ಅಂಕಿಗಳ ದ್ವಿತೀಯ ಫೋನ್ ಸಂಖ್ಯೆಯನ್ನು ನಮೂದಿಸಿ',
        'btn_no_email': 'ನನ್ನ ಬಳಿ ಇಮೇಲ್ ಇಲ್ಲ',
        'btn_request_signup': 'ಸೈನ್ ಅಪ್‌ಗಾಗಿ ವಿನಂತಿ',
        'btn_already_have_account': 'ಈಗಾಗಲೇ ಖಾತೆ ಇದೆಯೇ? ಲಾಗಿನ್ ಮಾಡಿ',
        'text_whatsapp_same_as_phone': 'ವಾಟ್ಸ್‌ಆಪ್ ಸಂಖ್ಯೆ ಫೋನ್ ಸಂಖ್ಯೆಯಂತೆಯೇ ಇದೆ',
        'text_use_same_whatsapp': 'ವಾಟ್ಸ್‌ಆಪ್‌ಗಾಗಿ ಅದೇ ಸಂಖ್ಯೆಯನ್ನು ಬಳಸುವುದೇ?',
        'text_verified': 'ಪರಿಶೀಲಿಸಲಾಗಿದೆ',
        'msg_submitting_data': 'ಡೇಟಾವನ್ನು ಸಲ್ಲಿಸಲಾಗುತ್ತಿದೆ. ದಯವಿಟ್ಟು ಕಾಯಿರಿ!',
        'msg_success': 'ಯಶಸ್ವಿ',
        'msg_error': 'ದೋಷ',
        'msg_form_incomplete': 'ಫಾರ್ಮ್ ಅಪೂರ್ಣ',
        'msg_complete_required_fields': 'ದಯವಿಟ್ಟು ಎಲ್ಲಾ ಅಗತ್ಯ ಕ್ಷೇತ್ರಗಳನ್ನು ಸರಿಯಾಗಿ ಭರ್ತಿ ಮಾಡಿ',
        'msg_information': 'ಮಾಹಿತಿ',
        'msg_complete_fields_order': 'ದಯವಿಟ್ಟು ಎಲ್ಲಾ ಕ್ಷೇತ್ರಗಳನ್ನು ಕ್ರಮದಲ್ಲಿ ಭರ್ತಿ ಮಾಡಿ. ಪ್ರತಿ ಕ್ಷೇತ್ರವು ಹಿಂದಿನ ಕ್ಷೇತ್ರವನ್ನು ಸರಿಯಾಗಿ ಭರ್ತಿ ಮಾಡಿ ಮತ್ತು ಪರಿಶೀಲಿಸಿದ ನಂತರ ಮಾತ್ರ ಸಕ್ರಿಯಗೊಳ್ಳುತ್ತದೆ.',
        'error_enter_first_name': 'ದಯವಿಟ್ಟು ನಿಮ್ಮ ಮೊದಲ ಹೆಸರನ್ನು ನಮೂದಿಸಿ',
        'error_enter_last_name': 'ದಯವಿಟ್ಟು ನಿಮ್ಮ ಕೊನೆಯ ಹೆಸರನ್ನು ನಮೂದಿಸಿ',
        'error_enter_whatsapp': 'ದಯವಿಟ್ಟು ವಾಟ್ಸ್‌ಆಪ್ ಸಂಖ್ಯೆಯನ್ನು ನಮೂದಿಸಿ',
        'error_whatsapp_10_digits': 'ವಾಟ್ಸ್‌ಆಪ್ ಸಂಖ್ಯೆ ನಿಖರವಾಗಿ 10 ಅಂಕಿಗಳಿರಬೇಕು',
        'error_enter_only_numbers': 'ದಯವಿಟ್ಟು ಕೇವಲ ಸಂಖ್ಯೆಗಳನ್ನು ನಮೂದಿಸಿ',
        'error_secondary_phone_10_digits': 'ದ್ವಿತೀಯ ಫೋನ್ ಸಂಖ್ಯೆ ನಿಖರವಾಗಿ 10 ಅಂಕಿಗಳಿರಬೇಕು',
        'error_phone_numbers_different': 'ಫೋನ್ ಸಂಖ್ಯೆ ಮತ್ತು ದ್ವಿತೀಯ ಫೋನ್ ಸಂಖ್ಯೆ ವಿಭಿನ್ನವಾಗಿರಬೇಕು',
        'error_request_error': 'ವಿನಂತಿ ದೋಷ',
        'error_phone_number': 'ಫೋನ್ ಸಂಖ್ಯೆ',
        'error_whatsapp_number': 'ವಾಟ್ಸ್‌ಆಪ್ ಸಂಖ್ಯೆ',
        'error_email_id': 'ಇಮೇಲ್ ಐಡಿ',
        'error_emp_number': 'ಉದ್ಯೋಗಿ ಸಂಖ್ಯೆ',
        'error_update_marked_info': 'ದಯವಿಟ್ಟು ಕೆಂಪು ಶಿಲುಬೆಯಿಂದ ಗುರುತಿಸಲಾದ ಮಾಹಿತಿಯನ್ನು ನವೀಕರಿಸಿ ಮತ್ತು ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ.',
        'status_new': 'ಹೊಸ',
        'status_already_taken': 'ಈಗಾಗಲೇ ತೆಗೆದುಕೊಳ್ಳಲಾಗಿದೆ',
        'status_already_requested': 'ಈಗಾಗಲೇ ವಿನಂತಿಸಲಾಗಿದೆ',
        'status_unknown': 'ಅಜ್ಞಾತ'
    },
    'ta': {  # Tamil
        'form_first_name_required': 'முதல் பெயர் *',
        'form_first_name_hint': 'உங்கள் முதல் பெயரை உள்ளிடவும்',
        'form_middle_name_optional': 'நடு பெயர் (விருப்பமானது)',
        'form_middle_name_hint': 'உங்கள் நடு பெயரை உள்ளிடவும்',
        'form_last_name_required': 'கடைசி பெயர் *',
        'form_last_name_hint': 'உங்கள் கடைசி பெயரை உள்ளிடவும்',
        'form_whatsapp_number': 'வாட்ஸ்அப் எண்',
        'form_whatsapp_hint': '10 இலக்க வாட்ஸ்அப் எண்ணை உள்ளிடவும்',
        'form_secondary_phone_optional': 'இரண்டாம் தொலைபேசி எண் (விருப்பமானது)',
        'form_secondary_phone_hint': '10 இலக்க இரண்டாம் தொலைபேசி எண்ணை உள்ளிடவும்',
        'btn_no_email': 'என்னிடம் மின்னஞ்சல் இல்லை',
        'btn_request_signup': 'பதிவுக்கான கோரிக்கை',
        'btn_already_have_account': 'ஏற்கனவே கணக்கு உள்ளதா? உள்நுழையவும்',
        'text_whatsapp_same_as_phone': 'வாட்ஸ்அப் எண் தொலைபேசி எண்ணைப் போலவே உள்ளது',
        'text_use_same_whatsapp': 'வாட்ஸ்அப்பிற்கு அதே எண்ணைப் பயன்படுத்தவா?',
        'text_verified': 'சரிபார்க்கப்பட்டது',
        'msg_submitting_data': 'தரவு சமர்ப்பிக்கப்படுகிறது. தயவுசெய்து காத்திருக்கவும்!',
        'msg_success': 'வெற்றி',
        'msg_error': 'பிழை',
        'msg_form_incomplete': 'படிவம் முழுமையடையவில்லை',
        'msg_complete_required_fields': 'தயவுசெய்து அனைத்து தேவையான புலங்களையும் சரியாக நிரப்பவும்',
        'msg_information': 'தகவல்',
        'msg_complete_fields_order': 'தயவுசெய்து அனைத்து புலங்களையும் வரிசையில் நிரப்பவும். ஒவ்வொரு புலமும் முந்தைய புலம் சரியாக நிரப்பப்பட்டு சரிபார்க்கப்பட்ட பின்னரே செயல்படும்.',
        'error_enter_first_name': 'தயவுசெய்து உங்கள் முதல் பெயரை உள்ளிடவும்',
        'error_enter_last_name': 'தயவுசெய்து உங்கள் கடைசி பெயரை உள்ளிடவும்',
        'error_enter_whatsapp': 'தயவுசெய்து வாட்ஸ்அப் எண்ணை உள்ளிடவும்',
        'error_whatsapp_10_digits': 'வாட்ஸ்அப் எண் சரியாக 10 இலக்கங்களாக இருக்க வேண்டும்',
        'error_enter_only_numbers': 'தயவுசெய்து எண்களை மட்டும் உள்ளிடவும்',
        'error_secondary_phone_10_digits': 'இரண்டாம் தொலைபேசி எண் சரியாக 10 இலக்கங்களாக இருக்க வேண்டும்',
        'error_phone_numbers_different': 'தொலைபேசி எண் மற்றும் இரண்டாம் தொலைபேசி எண் வேறுபட்டதாக இருக்க வேண்டும்',
        'error_request_error': 'கோரிக்கை பிழை',
        'error_phone_number': 'தொலைபேசி எண்',
        'error_whatsapp_number': 'வாட்ஸ்அப் எண்',
        'error_email_id': 'மின்னஞ்சல் ஐடி',
        'error_emp_number': 'பணியாளர் எண்',
        'error_update_marked_info': 'தயவுசெய்து சிவப்பு குறியால் குறிக்கப்பட்ட தகவலை புதுப்பித்து மீண்டும் முயற்சிக்கவும்.',
        'status_new': 'புதிய',
        'status_already_taken': 'ஏற்கனவே எடுக்கப்பட்டது',
        'status_already_requested': 'ஏற்கனவே கோரப்பட்டது',
        'status_unknown': 'தெரியாத'
    },
    'te': {  # Telugu
        'form_first_name_required': 'మొదటి పేరు *',
        'form_first_name_hint': 'మీ మొదటి పేరును నమోదు చేయండి',
        'form_middle_name_optional': 'మధ్య పేరు (ఐచ్ఛికం)',
        'form_middle_name_hint': 'మీ మధ్య పేరును నమోదు చేయండి',
        'form_last_name_required': 'చివరి పేరు *',
        'form_last_name_hint': 'మీ చివరి పేరును నమోదు చేయండి',
        'form_whatsapp_number': 'వాట్సాప్ నంబర్',
        'form_whatsapp_hint': '10 అంకెల వాట్సాప్ నంబర్‌ను నమోదు చేయండి',
        'form_secondary_phone_optional': 'రెండవ ఫోన్ నంబర్ (ఐచ్ఛికం)',
        'form_secondary_phone_hint': '10 అంకెల రెండవ ఫోన్ నంబర్‌ను నమోదు చేయండి',
        'btn_no_email': 'నా దగ్గర ఇమెయిల్ లేదు',
        'btn_request_signup': 'సైన్ అప్ కోసం అభ్యర్థన',
        'btn_already_have_account': 'ఇప్పటికే ఖాతా ఉందా? లాగిన్ చేయండి',
        'text_whatsapp_same_as_phone': 'వాట్సాప్ నంబర్ ఫోన్ నంబర్ మాదిరిగానే ఉంది',
        'text_use_same_whatsapp': 'వాట్సాప్ కోసం అదే నంబర్‌ను ఉపయోగించాలా?',
        'text_verified': 'ధృవీకరించబడింది',
        'msg_submitting_data': 'డేటా సమర్పించబడుతోంది. దయచేసి వేచి ఉండండి!',
        'msg_success': 'విజయం',
        'msg_error': 'లోపం',
        'msg_form_incomplete': 'ఫారం అసంపూర్ణం',
        'msg_complete_required_fields': 'దయచేసి అన్ని అవసరమైన ఫీల్డ్‌లను సరిగ్గా పూరించండి',
        'msg_information': 'సమాచారం',
        'msg_complete_fields_order': 'దయచేసి అన్ని ఫీల్డ్‌లను క్రమంలో పూరించండి. ప్రతి ఫీల్డ్ మునుపటి ఫీల్డ్ సరిగ్గా పూరించబడి మరియు ధృవీకరించబడిన తర్వాత మాత్రమే ప్రారంభించబడుతుంది.',
        'error_enter_first_name': 'దయచేసి మీ మొదటి పేరును నమోదు చేయండి',
        'error_enter_last_name': 'దయచేసి మీ చివరి పేరును నమోదు చేయండి',
        'error_enter_whatsapp': 'దయచేసి వాట్సాప్ నంబర్‌ను నమోదు చేయండి',
        'error_whatsapp_10_digits': 'వాట్సాప్ నంబర్ ఖచ్చితంగా 10 అంకెలు ఉండాలి',
        'error_enter_only_numbers': 'దయచేసి సంఖ్యలను మాత్రమే నమోదు చేయండి',
        'error_secondary_phone_10_digits': 'రెండవ ఫోన్ నంబర్ ఖచ్చితంగా 10 అంకెలు ఉండాలి',
        'error_phone_numbers_different': 'ఫోన్ నంబర్ మరియు రెండవ ఫోన్ నంబర్ వేరుగా ఉండాలి',
        'error_request_error': 'అభ్యర్థన లోపం',
        'error_phone_number': 'ఫోన్ నంబర్',
        'error_whatsapp_number': 'వాట్సాప్ నంబర్',
        'error_email_id': 'ఇమెయిల్ ఐడి',
        'error_emp_number': 'ఉద్యోగి నంబర్',
        'error_update_marked_info': 'దయచేసి ఎరుపు క్రాస్‌తో గుర్తించబడిన సమాచారాన్ని అప్‌డేట్ చేసి మళ్లీ ప్రయత్నించండి.',
        'status_new': 'కొత్త',
        'status_already_taken': 'ఇప్పటికే తీసుకోబడింది',
        'status_already_requested': 'ఇప్పటికే అభ్యర్థించబడింది',
        'status_unknown': 'తెలియని'
    },
    'ml': {  # Malayalam
        'form_first_name_required': 'ആദ്യ പേര് *',
        'form_first_name_hint': 'നിങ്ങളുടെ ആദ്യ പേര് നൽകുക',
        'form_middle_name_optional': 'മധ്യ പേര് (ഓപ്ഷണൽ)',
        'form_middle_name_hint': 'നിങ്ങളുടെ മധ്യ പേര് നൽകുക',
        'form_last_name_required': 'അവസാന പേര് *',
        'form_last_name_hint': 'നിങ്ങളുടെ അവസാന പേര് നൽകുക',
        'form_whatsapp_number': 'വാട്സാപ്പ് നമ്പർ',
        'form_whatsapp_hint': '10 അക്ക വാട്സാപ്പ് നമ്പർ നൽകുക',
        'form_secondary_phone_optional': 'രണ്ടാമത്തെ ഫോൺ നമ്പർ (ഓപ്ഷണൽ)',
        'form_secondary_phone_hint': '10 അക്ക രണ്ടാമത്തെ ഫോൺ നമ്പർ നൽകുക',
        'btn_no_email': 'എനിക്ക് ഇമെയിൽ ഇല്ല',
        'btn_request_signup': 'സൈൻ അപ്പിനുള്ള അഭ്യർത്ഥന',
        'btn_already_have_account': 'ഇതിനകം അക്കൗണ്ട് ഉണ്ടോ? ലോഗിൻ ചെയ്യുക',
        'text_whatsapp_same_as_phone': 'വാട്സാപ്പ് നമ്പർ ഫോൺ നമ്പറിന് സമാനമാണ്',
        'text_use_same_whatsapp': 'വാട്സാപ്പിനായി അതേ നമ്പർ ഉപയോഗിക്കണോ?',
        'text_verified': 'പരിശോധിച്ചു',
        'msg_submitting_data': 'ഡാറ്റ സമർപ്പിക്കുന്നു. ദയവായി കാത്തിരിക്കുക!',
        'msg_success': 'വിജയം',
        'msg_error': 'പിശക്',
        'msg_form_incomplete': 'ഫോം അപൂർണ്ണം',
        'msg_complete_required_fields': 'ദയവായി എല്ലാ ആവശ്യമായ ഫീൽഡുകളും ശരിയായി പൂരിപ്പിക്കുക',
        'msg_information': 'വിവരങ്ങൾ',
        'msg_complete_fields_order': 'ദയവായി എല്ലാ ഫീൽഡുകളും ക്രമത്തിൽ പൂരിപ്പിക്കുക. ഓരോ ഫീൽഡും മുമ്പത്തെ ഫീൽഡ് ശരിയായി പൂരിപ്പിച്ച് പരിശോധിച്ചതിന് ശേഷം മാത്രമേ പ്രവർത്തനക്ഷമമാകൂ.',
        'error_enter_first_name': 'ദയവായി നിങ്ങളുടെ ആദ്യ പേര് നൽകുക',
        'error_enter_last_name': 'ദയവായി നിങ്ങളുടെ അവസാന പേര് നൽകുക',
        'error_enter_whatsapp': 'ദയവായി വാട്സാപ്പ് നമ്പർ നൽകുക',
        'error_whatsapp_10_digits': 'വാട്സാപ്പ് നമ്പർ കൃത്യമായി 10 അക്കങ്ങൾ ആയിരിക്കണം',
        'error_enter_only_numbers': 'ദയവായി സംഖ്യകൾ മാത്രം നൽകുക',
        'error_secondary_phone_10_digits': 'രണ്ടാമത്തെ ഫോൺ നമ്പർ കൃത്യമായി 10 അക്കങ്ങൾ ആയിരിക്കണം',
        'error_phone_numbers_different': 'ഫോൺ നമ്പറും രണ്ടാമത്തെ ഫോൺ നമ്പറും വ്യത്യസ്തമായിരിക്കണം',
        'error_request_error': 'അഭ്യർത്ഥന പിശക്',
        'error_phone_number': 'ഫോൺ നമ്പർ',
        'error_whatsapp_number': 'വാട്സാപ്പ് നമ്പർ',
        'error_email_id': 'ഇമെയിൽ ഐഡി',
        'error_emp_number': 'ജീവനക്കാരൻ നമ്പർ',
        'error_update_marked_info': 'ദയവായി ചുവന്ന ക്രോസ് കൊണ്ട് അടയാളപ്പെടുത്തിയ വിവരങ്ങൾ അപ്ഡേറ്റ് ചെയ്ത് വീണ്ടും ശ്രമിക്കുക.',
        'status_new': 'പുതിയ',
        'status_already_taken': 'ഇതിനകം എടുത്തു',
        'status_already_requested': 'ഇതിനകം അഭ്യർത്ഥിച്ചു',
        'status_unknown': 'അജ്ഞാതം'
    }
}

def add_translations_to_arb(language_code, translations):
    """Add translations to a specific ARB file"""
    arb_file = f'lib/l10n/app_{language_code}.arb'
    
    if not os.path.exists(arb_file):
        print(f"Warning: {arb_file} does not exist")
        return
    
    try:
        with open(arb_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the last entry before the closing brace
        last_brace_pos = content.rfind('}')
        if last_brace_pos == -1:
            print(f"Error: Invalid JSON structure in {arb_file}")
            return
        
        # Insert new translations before the closing brace
        new_entries = []
        for key, value in translations.items():
            new_entries.append(f'  "{key}": "{value}",')
            new_entries.append(f'  "@{key}": {{')
            if 'form_' in key:
                new_entries.append(f'    "description": "Signup form field: {key}",')
                new_entries.append(f'    "context": "signup_form"')
            elif 'btn_' in key:
                new_entries.append(f'    "description": "Signup button: {key}",')
                new_entries.append(f'    "context": "signup_form"')
            elif 'msg_' in key:
                new_entries.append(f'    "description": "Signup message: {key}",')
                new_entries.append(f'    "context": "signup_form"')
            elif 'error_' in key:
                new_entries.append(f'    "description": "Signup error: {key}",')
                new_entries.append(f'    "context": "signup_error"')
            elif 'status_' in key:
                new_entries.append(f'    "description": "Signup status: {key}",')
                new_entries.append(f'    "context": "signup_error"')
            else:
                new_entries.append(f'    "description": "Signup text: {key}",')
                new_entries.append(f'    "context": "signup_form"')
            new_entries.append(f'  }},')
        
        # Remove the last comma
        if new_entries:
            new_entries[-1] = new_entries[-1].rstrip(',')
        
        # Insert before the last closing brace
        insertion_text = ',\n' + '\n'.join(new_entries)
        new_content = content[:last_brace_pos] + insertion_text + '\n' + content[last_brace_pos:]
        
        with open(arb_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"Successfully added {len(translations)} translations to {arb_file}")
        
    except Exception as e:
        print(f"Error processing {arb_file}: {e}")

def main():
    """Main function to add translations to all language files"""
    for lang_code, translations in signup_translations.items():
        add_translations_to_arb(lang_code, translations)

if __name__ == '__main__':
    main()
