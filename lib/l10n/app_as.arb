{"@@locale": "as", "appTitle": "রেলঅপ্স", "@appTitle": {"description": "The title of the application"}, "cancel": "", "confirm": "", "delete": "", "edit": "", "error": "", "home": "", "loading": "", "login": "লগিন", "@login": {"description": "Login button text"}, "logout": "", "next": "", "no": "", "ok": "", "password": "", "previous": "", "refresh": "", "retry": "", "save": "", "search": "", "settings": "", "submit": "", "username": "", "welcome": "রেলঅপ্সলৈ আপনাক স্বাগতম", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "", "test_welcome": "স্বাগতম", "@test_welcome": {"description": "Welcome message for testing", "context": "test_context"}, "test_login": "লগিন", "@test_login": {"description": "Login button text", "context": "button_labels"}, "text_menu": "মেনু", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "text_train_tracker": "ট্ৰেইন ট্ৰেকাৰ", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "CA নিযুক্ত কৰক", "@text_assign_ca": {"description": "Text from text_widgets: Assign CA", "context": "text_widgets"}, "text_assign_cs": "CS নিযুক্ত কৰক", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR বিৱৰণ", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_passenger_chart": "যাত্ৰী চাৰ্ট", "@text_passenger_chart": {"description": "Text from text_widgets: Passenger Chart", "context": "text_widgets"}, "text_map_screen": "মেপ স্ক্ৰীন", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "কনফিগাৰেচন", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "ৰিপৰ্ট", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "যাত্ৰী ফিডবেক", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "ৰেক অভাৱ ৰিপৰ্ট", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS ৰ পৰা MCC হস্তান্তৰ", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC ৰ পৰা OBHS হস্তান্তৰ", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "ডেটা আপলোড কৰক", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "ব্যৱহাৰকাৰী ব্যৱস্থাপনা", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "সমস্যা ব্যৱস্থাপনা", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "ৰেল সাথী QR", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "গ্ৰাহক সেৱা", "@text_customer_care": {"description": "Text from text_widgets: Customer Care", "context": "text_widgets", "text_loading": "লোড হৈ আছে", "@text_loading": {"description": "Critical translation for: text_loading", "context": "critical_translations"}, "text_error": "ত্ৰুটি", "@text_error": {"description": "Critical translation for: text_error", "context": "critical_translations"}, "text_success": "সফল", "@text_success": {"description": "Critical translation for: text_success", "context": "critical_translations"}, "text_warning": "সতৰ্কতা", "@text_warning": {"description": "Critical translation for: text_warning", "context": "critical_translations"}, "text_info": "তথ্য", "@text_info": {"description": "Critical translation for: text_info", "context": "critical_translations"}, "text_no_data": "কোনো ডেটা নাই", "@text_no_data": {"description": "Critical translation for: text_no_data", "context": "critical_translations"}, "text_please_wait": "অনুগ্ৰহ কৰি অপেক্ষা কৰক", "@text_please_wait": {"description": "Critical translation for: text_please_wait", "context": "critical_translations"}, "text_try_again": "আকৌ চেষ্টা কৰক", "@text_try_again": {"description": "Critical translation for: text_try_again", "context": "critical_translations"}, "text_something_went_wrong": "কিবা ভুল হৈছে", "@text_something_went_wrong": {"description": "Critical translation for: text_something_went_wrong", "context": "critical_translations"}, "text_network_error": "নেটৱৰ্ক ত্ৰুটি", "@text_network_error": {"description": "Critical translation for: text_network_error", "context": "critical_translations"}, "text_connection_failed": "সংযোগ ব্যৰ্থ", "@text_connection_failed": {"description": "Critical translation for: text_connection_failed", "context": "critical_translations"}, "text_timeout": "সময় শেষ", "@text_timeout": {"description": "Critical translation for: text_timeout", "context": "critical_translations"}, "text_train_details": "ৰেলৰ বিৱৰণ", "@text_train_details": {"description": "Critical translation for: text_train_details", "context": "critical_translations"}, "text_train_status": "ৰেলৰ অৱস্থা", "@text_train_status": {"description": "Critical translation for: text_train_status", "context": "critical_translations"}, "text_train_schedule": "ৰেলৰ সময়সূচী", "@text_train_schedule": {"description": "Critical translation for: text_train_schedule", "context": "critical_translations"}, "text_departure_time": "প্ৰস্থানৰ সময়", "@text_departure_time": {"description": "Critical translation for: text_departure_time", "context": "critical_translations"}, "text_arrival_time": "আগমনৰ সময়", "@text_arrival_time": {"description": "Critical translation for: text_arrival_time", "context": "critical_translations"}, "text_platform_number": "প্লেটফৰ্ম নম্বৰ", "@text_platform_number": {"description": "Critical translation for: text_platform_number", "context": "critical_translations"}, "text_coach_position": "কোচৰ অৱস্থান", "@text_coach_position": {"description": "Critical translation for: text_coach_position", "context": "critical_translations"}, "text_seat_availability": "আসন উপলব্ধতা", "@text_seat_availability": {"description": "Critical translation for: text_seat_availability", "context": "critical_translations"}, "text_passenger_list": "যাত্ৰী তালিকা", "@text_passenger_list": {"description": "Critical translation for: text_passenger_list", "context": "critical_translations"}, "text_reservation_chart": "সংৰক্ষণ চাৰ্ট", "@text_reservation_chart": {"description": "Critical translation for: text_reservation_chart", "context": "critical_translations"}}, "text_change_mobile": "মোবাইল সলনি কৰক", "@text_change_mobile": {"description": "Profile screen translation for: text_change_mobile", "context": "profile_screen"}, "text_change_whatsapp": "হোৱাটছএপ নম্বৰ সলনি কৰক", "@text_change_whatsapp": {"description": "Profile screen translation for: text_change_whatsapp", "context": "profile_screen"}, "text_alert": "সতৰ্কতা", "@text_alert": {"description": "Profile screen translation for: text_alert", "context": "profile_screen"}, "text_close": "বন্ধ কৰক", "@text_close": {"description": "Profile screen translation for: text_close", "context": "profile_screen"}, "text_change_your_email": "আপোনাৰ ইমেইল সলনি কৰক", "@text_change_your_email": {"description": "Profile screen translation for: text_change_your_email", "context": "profile_screen"}, "text_current_email": "বৰ্তমানৰ ইমেইল", "@text_current_email": {"description": "Profile screen translation for: text_current_email", "context": "profile_screen"}, "text_new_email": "নতুন ইমেইল", "@text_new_email": {"description": "Profile screen translation for: text_new_email", "context": "profile_screen"}, "text_please_enter_new_email": "অনুগ্ৰহ কৰি নতুন ইমেইল দিয়ক", "@text_please_enter_new_email": {"description": "Profile screen translation for: text_please_enter_new_email", "context": "profile_screen"}, "text_otp": "অ'টিপি", "@text_otp": {"description": "Profile screen translation for: text_otp", "context": "profile_screen"}, "text_resend_otp": "অ'টিপি পুনৰ পঠিয়াওক", "@text_resend_otp": {"description": "Profile screen translation for: text_resend_otp", "context": "profile_screen"}, "text_resend_in_seconds": "{seconds} ছেকেণ্ডত পুনৰ পঠিয়াওক", "@text_resend_in_seconds": {"description": "Profile screen translation for: text_resend_in_seconds", "context": "profile_screen"}, "text_verify_otp": "অ'টিপি সত্যাপন কৰক", "@text_verify_otp": {"description": "Profile screen translation for: text_verify_otp", "context": "profile_screen"}, "text_generate_otp": "অ'টিপি সৃষ্টি কৰক", "@text_generate_otp": {"description": "Profile screen translation for: text_generate_otp", "context": "profile_screen"}, "text_change_your_password": "আপোনাৰ পাছৱৰ্ড সলনি কৰক", "@text_change_your_password": {"description": "Profile screen translation for: text_change_your_password", "context": "profile_screen"}, "text_old_password": "পুৰণি পাছৱৰ্ড", "@text_old_password": {"description": "Profile screen translation for: text_old_password", "context": "profile_screen"}, "text_new_password": "নতুন পাছৱৰ্ড", "@text_new_password": {"description": "Profile screen translation for: text_new_password", "context": "profile_screen"}, "text_confirm_new_password": "নতুন পাছৱৰ্ড নিশ্চিত কৰক", "@text_confirm_new_password": {"description": "Profile screen translation for: text_confirm_new_password", "context": "profile_screen"}, "text_please_enter_otp": "অনুগ্ৰহ কৰি অ'টিপি দিয়ক", "@text_please_enter_otp": {"description": "Profile screen translation for: text_please_enter_otp", "context": "profile_screen"}, "text_send_mobile_otp": "মোবাইল অ'টিপি পঠিয়াওক", "@text_send_mobile_otp": {"description": "Profile screen translation for: text_send_mobile_otp", "context": "profile_screen"}, "text_send_email_otp": "ইমেইল অ'টিপি পঠিয়াওক", "@text_send_email_otp": {"description": "Profile screen translation for: text_send_email_otp", "context": "profile_screen"}, "text_please_enter_value": "অনুগ্ৰহ কৰি এটা মান দিয়ক", "@text_please_enter_value": {"description": "Profile screen translation for: text_please_enter_value", "context": "profile_screen"}, "text_please_enter_valid_mobile": "অনুগ্ৰহ কৰি এটা বৈধ মোবাইল নম্বৰ দিয়ক", "@text_please_enter_valid_mobile": {"description": "Profile screen translation for: text_please_enter_valid_mobile", "context": "profile_screen"}, "text_success": "সফলতা", "@text_success": {"description": "Profile screen translation for: text_success", "context": "profile_screen"}, "text_ok": "ঠিক আছে", "@text_ok": {"description": "Profile screen translation for: text_ok", "context": "profile_screen"}, "text_change_your_mobile_number": "আপোনাৰ মোবাইল নম্বৰ সলনি কৰক", "@text_change_your_mobile_number": {"description": "Profile screen translation for: text_change_your_mobile_number", "context": "profile_screen"}, "text_current_mobile_number": "বৰ্তমানৰ মোবাইল নম্বৰ", "@text_current_mobile_number": {"description": "Profile screen translation for: text_current_mobile_number", "context": "profile_screen"}, "text_new_mobile_number": "নতুন মোবাইল নম্বৰ", "@text_new_mobile_number": {"description": "Profile screen translation for: text_new_mobile_number", "context": "profile_screen"}, "text_please_enter_new_mobile": "অনুগ্ৰহ কৰি নতুন মোবাইল নম্বৰ দিয়ক", "@text_please_enter_new_mobile": {"description": "Profile screen translation for: text_please_enter_new_mobile", "context": "profile_screen"}, "text_change_your_whatsapp_number": "আপোনাৰ হোৱাটছএপ নম্বৰ সলনি কৰক", "@text_change_your_whatsapp_number": {"description": "Profile screen translation for: text_change_your_whatsapp_number", "context": "profile_screen"}, "text_current_whatsapp_number": "বৰ্তমানৰ হোৱাটছএপ নম্বৰ", "@text_current_whatsapp_number": {"description": "Profile screen translation for: text_current_whatsapp_number", "context": "profile_screen"}, "text_new_whatsapp_number": "নতুন হোৱাটছএপ নম্বৰ", "@text_new_whatsapp_number": {"description": "Profile screen translation for: text_new_whatsapp_number", "context": "profile_screen"}, "text_please_enter_new_whatsapp": "অনুগ্ৰহ কৰি নতুন হোৱাটছএপ মোবাইল নম্বৰ দিয়ক", "@text_please_enter_new_whatsapp": {"description": "Profile screen translation for: text_please_enter_new_whatsapp", "context": "profile_screen"}, "text_train": "ৰেল", "@text_train": {"description": "Table column header for train", "context": "add_train_screen"}, "text_coaches": "<PERSON><PERSON>চ", "@text_coaches": {"description": "Table column header for coaches", "context": "add_train_screen"}, "text_origin_date": "মূল তাৰিখ", "@text_origin_date": {"description": "Table column header for origin date", "context": "add_train_screen"}, "text_na": "উপলব্ধ নহয়", "@text_na": {"description": "Not available text", "context": "add_train_screen"}, "text_send_otp": "অ'টিপি পঠিয়াওক", "@text_send_otp": {"description": "Send OTP button text", "context": "change_email_modal"}, "text_failed_to_send_otp": "অ'টিপি পঠিয়াবলৈ ব্যৰ্থ: {error}", "@text_failed_to_send_otp": {"description": "Error message when OTP sending fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_email_saved_successfully": "ইমেইল সফলভাৱে সংৰক্ষিত হৈছে!", "@text_email_saved_successfully": {"description": "Success message when email is saved", "context": "change_email_modal"}, "text_failed_to_verify_otp": "অ'টিপি সত্যাপন কৰিবলৈ ব্যৰ্থ: {error}", "@text_failed_to_verify_otp": {"description": "Error message when OTP verification fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_cancel": "বাতিল", "@text_cancel": {"description": "Cancel button text", "context": "general"}, "rm_feedback_app_bar_title": "ৰেলমদদ যাত্ৰী ফিডবেক", "rm_feedback_main_title": "ৰেলমদদ ফিডবেক", "form_pnr_number": "PNR নম্বৰ *", "form_crn_number": "CRN নম্বৰ*", "form_train_no": "ৰেল নম্বৰ *", "form_train_name": "ৰেলৰ নাম *", "form_passenger_name": "যাত্ৰীৰ নাম *", "form_coach_no": "কোচ নম্বৰ *", "form_berth_no": "বাৰ্থ নম্বৰ *", "form_mobile_number": "মোবাইল নম্বৰ", "form_email_id": "ইমেইল আইডি", "form_issue_type": "সমস্যাৰ প্ৰকাৰ", "form_sub_issue_type": "উপ সমস্যাৰ প্ৰকাৰ", "form_resolved_status": "সমাধান হৈছে (হয়/নহয়) *", "form_marks": "নম্বৰ (১ ৰ পৰা ১০) *", "form_task_status": "কামৰ অৱস্থা *", "form_remarks": "যাত্ৰীৰ মন্তব্য", "btn_validate": "সত্যাপন কৰক", "btn_verified": "সত্যাপিত", "btn_verify_email": "ইমেইল সত্যাপন কৰক", "btn_verify_otp": "OTP সত্যাপন কৰক", "btn_submit_feedback": "ফিডবেক জমা দিয়ক", "btn_upload_pnr_image": "PNR ছবি আপলোড কৰক", "btn_pick_media": "ফিডবেকৰ বাবে ছবি/ভিডিঅ' বাছনি কৰক", "btn_camera": "কেমেৰা", "btn_gallery": "গেলাৰী", "btn_image": "ছবি", "btn_video": "ভিডিঅ'", "btn_i_understand": "মই বুজিলোঁ", "btn_ok": "ঠিক আছে", "status_verified": "সত্যাপিত", "status_pending": "অপেক্ষমাণ", "status_completed": "সম্পূৰ্ণ", "status_yes": "হয়", "status_no": "নহয়", "status_select": "বাছনি কৰক", "section_email_verification": "ইমেইল সত্যাপন (ঐচ্ছিক)", "section_selected_images": "বাছনি কৰা ছবি:", "section_selected_videos": "বাছনি কৰা ভিডিঅ':", "dialog_email_verification_info": "ইমেইল সত্যাপন তথ্য", "dialog_select_media_type": "মিডিয়া প্ৰকাৰ বাছনি কৰক", "validation_fill_all_fields": "অনুগ্ৰহ কৰি সকলো ক্ষেত্ৰ বৈধ তথ্যৰে পূৰণ কৰক।", "validation_pnr_digits": "PNR নম্বৰ ৮ বা ১০ সংখ্যাৰ হ'ব লাগিব", "validation_berth_number": "বাৰ্থ নম্বৰ এটা বৈধ সংখ্যা হ'ব লাগিব", "validation_feedback_length": "ফিডবেক ১০০ আখৰতকৈ বেছি হ'ব নোৱাৰে", "validation_email_required": "অনুগ্ৰহ কৰি এটা বৈধ ইমেইল আইডি দিয়ক।", "validation_otp_required": "অনুগ্ৰহ কৰি OTP দিয়ক।", "validation_train_no_required": "ৰেল নম্বৰ প্ৰয়োজন", "validation_train_name_required": "ৰেলৰ নাম প্ৰয়োজন", "validation_passenger_name_required": "যাত্ৰীৰ নাম প্ৰয়োজন", "validation_mobile_required": "মোবাইল নম্বৰ প্ৰয়োজন", "validation_mobile_digits": "মোবাইল নম্বৰ ১০ সংখ্যাৰ হ'ব লাগিব", "validation_issue_type_required": "অনুগ্ৰহ কৰি এটা সমস্যাৰ প্ৰকাৰ বাছনি কৰক", "validation_sub_issue_required": "অনুগ্ৰহ কৰি এটা উপ-সমস্যাৰ প্ৰকাৰ বাছনি কৰক", "validation_resolved_required": "অনুগ্ৰহ কৰি সমাধানৰ অৱস্থা বাছনি কৰক", "validation_marks_required": "অনুগ্ৰহ কৰি নম্বৰ বাছনি কৰক", "msg_pnr_images_limit": "আপুনি সৰ্বোচ্চ ৩টা PNR ছবি বাছনি কৰিব পাৰে", "msg_feedback_images_limit": "সৰ্বোচ্চ ৩টা ফিডবেক ছবিৰ অনুমতি আছে", "msg_images_added_limit": "কেৱল {count}টা ছবি যোগ কৰা হৈছে। সৰ্বোচ্চ ৩টাৰ সীমা পাইছে।", "msg_error_picking_media": "মিডিয়া বাছনিত ত্ৰুটি: {error}", "msg_failed_fetch_train_name": "ৰেলৰ নাম আনিবলৈ ব্যৰ্থ", "msg_invalid_pnr": "অবৈধ PNR নম্বৰ।", "msg_pnr_success": "PNR বিৱৰণ সফলভাৱে আনা হৈছে।", "msg_pnr_validation_failed": "PNR বিৱৰণ সত্যাপন কৰিবলৈ ব্যৰ্থ। অবৈধ PNR নম্বৰ।", "msg_email_verification_sent": "ইমেইল সত্যাপন আৰম্ভ হৈছে। অনুগ্ৰহ কৰি আপোনাৰ ইনবক্স আৰু স্পেম ফোল্ডাৰ দুয়োটা পৰীক্ষা কৰক।।", "msg_otp_verified": "OTP সফলভাৱে সত্যাপন কৰা হৈছে।", "msg_feedback_submitted": "ফিডবেক সফলভাৱে জমা দিয়া হৈছে!", "msg_feedback_failed": "ফিডবেক জমা দিবলৈ ব্যৰ্থ", "msg_unexpected_error": "এটা অপ্ৰত্যাশিত ত্ৰুটি ঘটিছে। অনুগ্ৰহ কৰি আকৌ চেষ্টা কৰক।", "info_spam_folder_note": "অনুগ্ৰহ কৰি মনত ৰাখিব যে সত্যাপন ইমেইলবোৰ কেতিয়াবা আপোনাৰ স্পেম/জাংক ফোল্ডাৰত পৌঁছিব পাৰে।", "info_after_requesting_otp": "OTP অনুৰোধ কৰাৰ পিছত:", "info_check_inbox": "প্ৰথমে আপোনাৰ ইনবক্স পৰীক্ষা কৰক", "info_check_spam": "যদি পোৱা নগ'ল, স্পেম/জাংক ফোল্ডাৰ পৰীক্ষা কৰক", "info_add_safe_sender": "আমাৰ ডমেইনক আপোনাৰ নিৰাপদ প্ৰেৰক তালিকাত যোগ কৰক", "text_no_feedback_images": "কোনো ফিডবেক ছবি বাছনি কৰা হোৱা নাই", "text_no_pnr_images": "কোনো PNR ছবি বাছনি কৰা হোৱা নাই", "text_character_count": "{count}/১০০ আখৰ", "loading_sending_otp": "OTP পঠিওৱা হৈ আছে", "loading_verifying_otp": "OTP সত্যাপন কৰা হৈ আছে", "loading_submitting_feedback": "ফিডবেক জমা দিয়া হৈ আছে", "attendance_api_summary": "API সাৰাংশ বিৱৰণ", "@attendance_api_summary": {"description": "Attendance: API Summary Details", "context": "attendance"}, "attendance_assigned_coaches": "🚆 বৰাদ্দ Coaches:", "@attendance_assigned_coaches": {"description": "Attendance: 🚆 Assigned Coaches:", "context": "attendance"}, "attendance_available_label": "উপলব্ধ: {count}", "@attendance_available_label": {"description": "Attendance: Available: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_available_section": "উপলব্ধ", "@attendance_available_section": {"description": "Attendance: Available", "context": "attendance"}, "attendance_berths_count": "{count} berths", "@attendance_berths_count": {"description": "Attendance: {count} berths", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_buffer_time_restriction": "The সময় between the current সময় and the ট্ৰেইন's arrival সময় is not within the 2-hour buffer.", "@attendance_buffer_time_restriction": {"description": "Attendance: The time between the current time and the train's arrival time is not within the 2-hour buffer.", "context": "attendance"}, "attendance_cancel": "বাতিল কৰক", "@attendance_cancel": {"description": "Attendance: Cancel", "context": "attendance"}, "attendance_cancelled": "Cancelled", "@attendance_cancelled": {"description": "Attendance: Cancelled", "context": "attendance"}, "attendance_chart_not_prepared": "চার্ট has not been prepared for this ষ্টেচন", "@attendance_chart_not_prepared": {"description": "Attendance: Chart has not been prepared for this station", "context": "attendance"}, "attendance_charting_refreshed": "চার্টিং refreshed at: {time}", "@attendance_charting_refreshed": {"description": "Attendance: Charting refreshed at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_started": "চার্টিং started at: {time}", "@attendance_charting_started": {"description": "Attendance: Charting started at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_time": "চার্টিং সময়: {time}", "@attendance_charting_time": {"description": "Attendance: Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_click_more": "click for more...", "@attendance_click_more": {"description": "Attendance: click for more...", "context": "attendance"}, "attendance_coach_label": "🚃 কোচ: {coach}", "@attendance_coach_label": {"description": "Attendance: 🚃 Coach: {coach}", "context": "attendance", "placeholders": {"coach": {"type": "String"}}}, "attendance_coach_occupancy": "কোচ Occupancy বিৱৰণ", "@attendance_coach_occupancy": {"description": "Attendance: Coach Occupancy <PERSON>", "context": "attendance"}, "attendance_coach_type": "কোচ Type:", "@attendance_coach_type": {"description": "Attendance: Coach Type:", "context": "attendance"}, "attendance_coaches": "Coaches: {coaches}", "@attendance_coaches": {"description": "Attendance: Coaches: {coaches}", "context": "attendance", "placeholders": {"coaches": {"type": "String"}}}, "attendance_concise_view": "Concise দৃশ্য", "@attendance_concise_view": {"description": "Attendance: Concise View", "context": "attendance"}, "attendance_count_label": "A: {count}", "@attendance_count_label": {"description": "Attendance: A: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_daily": "Daily", "@attendance_daily": {"description": "Attendance: Daily", "context": "attendance"}, "attendance_data_refreshed": "ডেটা refreshed সফলভাবে", "@attendance_data_refreshed": {"description": "Attendance: Data refreshed successfully", "context": "attendance"}, "attendance_deboarding": "🔴 নমা:", "@attendance_deboarding": {"description": "Attendance: 🔴 Deboarding:", "context": "attendance"}, "attendance_deboarding_none": "🔴 নমা: None", "@attendance_deboarding_none": {"description": "Attendance: 🔴 Deboarding: None", "context": "attendance"}, "attendance_detailed_view": "Detailed দৃশ্য", "@attendance_detailed_view": {"description": "Attendance: Detailed View", "context": "attendance"}, "attendance_ehk_assigned": "EHK বৰাদ্দ for ট্ৰেইন:{ehkName}", "@attendance_ehk_assigned": {"description": "Attendance: EHK Assigned for train:{ehkName}", "context": "attendance", "placeholders": {"ehkName": {"type": "String"}}}, "attendance_expected_charting": "Expected চার্টিং সময়: {time}", "@attendance_expected_charting": {"description": "Attendance: Expected Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_failed_load_data": "বিফল to load detailed ডেটা: {error}", "@attendance_failed_load_data": {"description": "Attendance: Failed to load detailed data: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_failed_update_status": "বিফল to আপডেট কৰক ট্ৰেইন অৱস্থা", "@attendance_failed_update_status": {"description": "Attendance: Failed to update train status", "context": "attendance"}, "attendance_go": "যাওক", "@attendance_go": {"description": "Attendance: Go", "context": "attendance"}, "attendance_in_route": "In-route", "@attendance_in_route": {"description": "Attendance: In-route", "context": "attendance"}, "attendance_inside": "ভিতৰত", "@attendance_inside": {"description": "Attendance: Inside", "context": "attendance"}, "attendance_inside_train": "You are now চিহ্নিত as ভিতৰত the ট্ৰেইন", "@attendance_inside_train": {"description": "Attendance: You are now marked as inside the train", "context": "attendance"}, "attendance_journey_status_updated": "যাত্ৰা অৱস্থা updated to {status}", "@attendance_journey_status_updated": {"description": "Attendance: Journey status updated to {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_last_fetched": "Last fetched: {time}", "@attendance_last_fetched": {"description": "Attendance: Last fetched: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_loading": "লোড হৈ আছে...", "@attendance_loading": {"description": "Attendance: Loading...", "context": "attendance"}, "attendance_location_not_fetched": "ট্ৰেইন অৱস্থান is not fetched yet, অনুগ্ৰহ কৰি try again later", "@attendance_location_not_fetched": {"description": "Attendance: Train Location is not fetched yet, please try again later", "context": "attendance"}, "attendance_na": "N/A", "@attendance_na": {"description": "Attendance: N/A", "context": "attendance"}, "attendance_near_stations": "You're near the following ষ্টেচন(s):", "@attendance_near_stations": {"description": "Attendance: You're near the following station(s):", "context": "attendance"}, "attendance_nearby_station_alert": "🛤️ ওচৰৰ ষ্টেচন সতৰ্কবাণী", "@attendance_nearby_station_alert": {"description": "Attendance: 🛤️ Nearby Station Alert", "context": "attendance"}, "attendance_non_sleeper": "Non-Sleeper", "@attendance_non_sleeper": {"description": "Attendance: Non-Sleeper", "context": "attendance"}, "attendance_not_attendance_station": "উপস্থিতি cannot be চিহ্নিত for ষ্টেচন {stationCode} as it is not an উপস্থিতি ষ্টেচন.", "@attendance_not_attendance_station": {"description": "Attendance: Attendance cannot be marked for station {stationCode} as it is not an attendance station.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_not_inside_train": "You are not ভিতৰত the ট্ৰেইন. অনুগ্ৰহ কৰি যাওক ভিতৰত the ট্ৰেইন first.", "@attendance_not_inside_train": {"description": "Attendance: You are not inside the train. Please go inside the train first.", "context": "attendance"}, "attendance_off_label": "Off: {count}", "@attendance_off_label": {"description": "Attendance: Off: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_offboarding_section": "Offboarding", "@attendance_offboarding_section": {"description": "Attendance: Offboarding", "context": "attendance"}, "attendance_ok": "ঠিক আছে", "@attendance_ok": {"description": "Attendance: OK", "context": "attendance"}, "attendance_on_label": "On: {count}", "@attendance_on_label": {"description": "Attendance: On: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_onboarding": "🟢 উঠা:", "@attendance_onboarding": {"description": "Attendance: 🟢 Onboarding:", "context": "attendance"}, "attendance_onboarding_none": "🟢 উঠা: None", "@attendance_onboarding_none": {"description": "Attendance: 🟢 Onboarding: None", "context": "attendance"}, "attendance_onboarding_section": "উঠা", "@attendance_onboarding_section": {"description": "Attendance: Onboarding", "context": "attendance"}, "attendance_other_ca": "Other CA", "@attendance_other_ca": {"description": "Attendance: Other CA", "context": "attendance"}, "attendance_other_ehk": "Other EHK/OBHS", "@attendance_other_ehk": {"description": "Attendance: Other EHK/OBHS", "context": "attendance"}, "attendance_outside_train": "You are now চিহ্নিত as বাহিৰত the ট্ৰেইন", "@attendance_outside_train": {"description": "Attendance: You are now marked as outside the train", "context": "attendance"}, "attendance_passenger_chart": "যাত্ৰী চার্ট   Atten..", "@attendance_passenger_chart": {"description": "Attendance: Passenger Chart   Atten..", "context": "attendance"}, "attendance_refresh_failed": "ৰিফ্ৰেছ বিফল: {error}", "@attendance_refresh_failed": {"description": "Attendance: Refresh failed: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_screen_title": "উপস্থিতি", "@attendance_screen_title": {"description": "Attendance: Attendance", "context": "attendance"}, "attendance_select_date": "নিৰ্বাচন কৰক তাৰিখ", "@attendance_select_date": {"description": "Attendance: Select date", "context": "attendance"}, "attendance_select_train_date": "অনুগ্ৰহ কৰি নিৰ্বাচন কৰক a ট্ৰেইন নম্বৰ and তাৰিখ.", "@attendance_select_train_date": {"description": "Attendance: Please select a train number and date.", "context": "attendance"}, "attendance_select_train_first": "অনুগ্ৰহ কৰি নিৰ্বাচন কৰক a ট্ৰেইন first", "@attendance_select_train_first": {"description": "Attendance: Please select a train first", "context": "attendance"}, "attendance_self": "Self", "@attendance_self": {"description": "Attendance: Self", "context": "attendance"}, "attendance_sleeper": "Sleeper", "@attendance_sleeper": {"description": "Attendance: Sleeper", "context": "attendance"}, "attendance_station_details": "ষ্টেচন বিৱৰণ - {stationCode}", "@attendance_station_details": {"description": "Attendance: Station Details - {stationCode}", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_stoppages": "Stoppages", "@attendance_stoppages": {"description": "Attendance: Stoppages", "context": "attendance"}, "attendance_timings": "Tim<PERSON>", "@attendance_timings": {"description": "Attendance: Timings", "context": "attendance"}, "attendance_today": "Today", "@attendance_today": {"description": "Attendance: Today", "context": "attendance"}, "attendance_too_far_from_station": "You're over 50 KM away from the selected ষ্টেচন {stationCode}. উপস্থিতি can only be চিহ্নিত when you're within the allowed range.", "@attendance_too_far_from_station": {"description": "Attendance: You're over 50 KM away from the selected station {stationCode}. Attendance can only be marked when you're within the allowed range.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_train": "ট্ৰেইন", "@attendance_train": {"description": "Attendance: Train", "context": "attendance"}, "attendance_train_date": "ট্ৰেইন {trainNo} - {date}", "@attendance_train_date": {"description": "Attendance: Train {trainNo} - {date}", "context": "attendance", "placeholders": {"trainNo": {"type": "String"}, "date": {"type": "String"}}}, "attendance_train_depot": "ট্ৰেইন ডিপো:{depot}", "@attendance_train_depot": {"description": "Attendance: Train Depot:{depot}", "context": "attendance", "placeholders": {"depot": {"type": "String"}}}, "attendance_train_not_running": "ট্ৰেইন Not Running", "@attendance_train_not_running": {"description": "Attendance: Train Not Running", "context": "attendance"}, "attendance_train_not_running_message": "ট্ৰেইন {trainNumber} is NOT running on {dayOfWeek}", "@attendance_train_not_running_message": {"description": "Attendance: Train {trainNumber} is NOT running on {dayOfWeek}", "context": "attendance", "placeholders": {"trainNumber": {"type": "String"}, "dayOfWeek": {"type": "String"}}}, "attendance_update": "আপডেট কৰক", "@attendance_update": {"description": "Attendance: Update", "context": "attendance"}, "attendance_update_journey_status": "আপডেট কৰক যাত্ৰা অৱস্থা", "@attendance_update_journey_status": {"description": "Attendance: Update Journey Status", "context": "attendance"}, "attendance_update_message": "A new সংস্কৰণ of the app is উপলব্ধ. You must আপডেট কৰক to continue using the app.", "@attendance_update_message": {"description": "Attendance: A new version of the app is available. You must update to continue using the app.", "context": "attendance"}, "attendance_update_now": "আপডেট কৰক Now", "@attendance_update_now": {"description": "Attendance: Update Now", "context": "attendance"}, "attendance_update_required": "আপডেট কৰক প্ৰয়োজনীয়", "@attendance_update_required": {"description": "Attendance: Update Required", "context": "attendance"}, "attendance_user_location": "ব্যৱহাৰকাৰী অৱস্থান:{locationStatus}", "@attendance_user_location": {"description": "Attendance: User Location:{locationStatus}", "context": "attendance", "placeholders": {"locationStatus": {"type": "String"}}}, "attendance_details_distance": "দূৰত্ব: {distance} km", "@attendance_details_distance": {"description": "Attendance: Distance: {distance} km", "context": "attendance", "placeholders": {"distance": {"type": "String"}}}, "attendance_details_error": "ভুল: {error}", "@attendance_details_error": {"description": "Attendance: Error: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_details_journey_date": "যাত্ৰা তাৰিখ:", "@attendance_details_journey_date": {"description": "Attendance: Journey Date:", "context": "attendance"}, "attendance_details_latitude": "latitude: {latitude}", "@attendance_details_latitude": {"description": "Attendance: latitude: {latitude}", "context": "attendance", "placeholders": {"latitude": {"type": "String"}}}, "attendance_details_longitude": "longitude: {longitude}", "@attendance_details_longitude": {"description": "Attendance: longitude: {longitude}", "context": "attendance", "placeholders": {"longitude": {"type": "String"}}}, "attendance_details_match_percentage": "Match শতাংশ: {percentage}", "@attendance_details_match_percentage": {"description": "Attendance: Match Percentage: {percentage}", "context": "attendance", "placeholders": {"percentage": {"type": "String"}}}, "attendance_details_nearest_station": "nearest ষ্টেচন: {station}", "@attendance_details_nearest_station": {"description": "Attendance: nearest Station: {station}", "context": "attendance", "placeholders": {"station": {"type": "String"}}}, "attendance_details_no_attendance": "No উপস্থিতি found.", "@attendance_details_no_attendance": {"description": "Attendance: No attendance found.", "context": "attendance"}, "attendance_details_no_data": "No ডেটা উপলব্ধ.", "@attendance_details_no_data": {"description": "Attendance: No data available.", "context": "attendance"}, "attendance_details_no_human_detected": "No human detected", "@attendance_details_no_human_detected": {"description": "Attendance: No human detected", "context": "attendance"}, "attendance_details_station_code": "ষ্টেচন Code:", "@attendance_details_station_code": {"description": "Attendance: Station Code:", "context": "attendance"}, "attendance_details_status": "অৱস্থা: {status}", "@attendance_details_status": {"description": "Attendance: Status: {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_details_status_marked": "চিহ্নিত", "@attendance_details_status_marked": {"description": "Attendance: Marked", "context": "attendance"}, "attendance_details_status_pending": "Pending", "@attendance_details_status_pending": {"description": "Attendance: Pending", "context": "attendance"}, "attendance_details_title": "উপস্থিতি বিৱৰণ", "@attendance_details_title": {"description": "Attendance: Attendance Details", "context": "attendance"}, "attendance_details_train_number": "ট্ৰেইন নম্বৰ:", "@attendance_details_train_number": {"description": "Attendance: Train Number:", "context": "attendance"}, "attendance_details_updated": "All বিৱৰণ updated সফলভাবে.", "@attendance_details_updated": {"description": "Attendance: All details updated successfully.", "context": "attendance"}, "attendance_details_updated_at": "Updated At: {updatedAt}", "@attendance_details_updated_at": {"description": "Attendance: Updated At: {updatedAt}", "context": "attendance", "placeholders": {"updatedAt": {"type": "String"}}}, "attendance_details_updated_by": "Updated By: {updatedBy}", "@attendance_details_updated_by": {"description": "Attendance: Updated By: {updatedBy}", "context": "attendance", "placeholders": {"updatedBy": {"type": "String"}}}, "attendance_details_username": "Username: {username}", "@attendance_details_username": {"description": "Attendance: Username: {username}", "context": "attendance", "placeholders": {"username": {"type": "String"}}}, "btn_no_attendance_found": "No উপস্থিতি found.", "@btn_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_attendance_already_submitted": "উপস্থিতি Already Submitted", "@text_attendance_already_submitted": {"description": "Attendance: Attendance Already Submitted", "context": "attendance"}, "text_attendance_marked_successfully": "উপস্থিতি চিহ্নিত সফলভাবে!", "@text_attendance_marked_successfully": {"description": "Attendance: Attendance marked successfully!", "context": "attendance"}, "text_no_attendance_found": "No উপস্থিতি found.", "@text_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_welcome_to_railops": "ৰেলঅপ্সলৈ আপনাক স্বাগতম", "@text_welcome_to_railops": {"description": "Welcome message on login screen", "context": "login_screen"}, "text_sign_up_to_railops": "ৰেলঅপ্সত চাইন আপ কৰক", "@text_sign_up_to_railops": {"description": "Sign up screen title", "context": "sign_up_screen"}, "text_login_using_fingerprint": "ফিঙ্গাৰপ্ৰিণ্ট ব্যৱহাৰ কৰি লগিন কৰক", "@text_login_using_fingerprint": {"description": "Biometric authentication prompt", "context": "authentication"}, "text_logging_in_please_wait": "লগিন হৈ আছে... অনুগ্ৰহ কৰি অপেক্ষা কৰক।", "@text_logging_in_please_wait": {"description": "Loading message during login", "context": "authentication"}, "text_log_in": "<PERSON><PERSON>", "@text_log_in": {"description": "Login button text", "context": "authentication"}, "text_home": "হোম", "@text_home": {"description": "Home navigation title", "context": "navigation"}, "text_home_screen": "হোম স্ক্ৰীন", "@text_home_screen": {"description": "Home screen title text", "context": "home_screen"}, "@form_mobile_number": {"description": "Mobile number field label", "context": "login_form"}, "form_password": "পাছৱৰ্ড *", "@form_password": {"description": "Password field label", "context": "login_form"}, "error_enter_mobile_number": "অনুগ্ৰহ কৰি আপোনাৰ মোবাইল নম্বৰ দিয়ক", "@error_enter_mobile_number": {"description": "Mobile number validation error", "context": "login_form"}, "error_mobile_number_digits": "মোবাইল নম্বৰ ১০ সংখ্যাৰ হ'ব লাগিব", "@error_mobile_number_digits": {"description": "Mobile number length validation error", "context": "login_form"}, "error_enter_password": "অনুগ্ৰহ কৰি আপোনাৰ পাছৱৰ্ড দিয়ক", "@error_enter_password": {"description": "Password validation error", "context": "login_form"}, "btn_log_in_with_mobile": "মোবাইল নম্বৰেৰে লগ ইন কৰক", "@btn_log_in_with_mobile": {"description": "Mobile login button text", "context": "login_form"}, "btn_new_user_sign_up": "নতুন ব্যৱহাৰকাৰী? ইয়াত চাইন আপ কৰক", "@btn_new_user_sign_up": {"description": "Sign up button text", "context": "login_form"}, "text_privacy_policy": "গোপনীয়তা নীতি", "@text_privacy_policy": {"description": "Privacy policy link text", "context": "login_form"}, "text_terms_conditions": "নিয়ম আৰু চৰ্তাৱলী", "@text_terms_conditions": {"description": "Terms and conditions link text", "context": "login_form"}, "text_know_about_app": "এই এপৰ বিষয়ে জানক", "@text_know_about_app": {"description": "App information link text", "context": "login_form"}, "msg_login_successful": "লগিন সফল", "@msg_login_successful": {"description": "Login success message", "context": "authentication"}, "msg_invalid_pin": "অবৈধ পিন", "@msg_invalid_pin": {"description": "Invalid PIN error message", "context": "authentication"}, "form_first_name_required": "প্ৰথম নাম *", "@form_first_name_required": {"description": "Signup form field: form_first_name_required", "context": "signup_form"}, "form_first_name_hint": "আপোনাৰ প্ৰথম নাম লিখক", "@form_first_name_hint": {"description": "Signup form field: form_first_name_hint", "context": "signup_form"}, "form_middle_name_optional": "মধ্য নাম (ঐচ্ছিক)", "@form_middle_name_optional": {"description": "Signup form field: form_middle_name_optional", "context": "signup_form"}, "form_middle_name_hint": "আপোনাৰ মধ্য নাম লিখক", "@form_middle_name_hint": {"description": "Signup form field: form_middle_name_hint", "context": "signup_form"}, "form_last_name_required": "শেষ নাম *", "@form_last_name_required": {"description": "Signup form field: form_last_name_required", "context": "signup_form"}, "form_last_name_hint": "আপোনাৰ শেষ নাম লিখক", "@form_last_name_hint": {"description": "Signup form field: form_last_name_hint", "context": "signup_form"}, "form_whatsapp_number": "হোৱাটছএপ নম্বৰ", "@form_whatsapp_number": {"description": "Signup form field: form_whatsapp_number", "context": "signup_form"}, "form_whatsapp_hint": "১০ সংখ্যাৰ হোৱাটছএপ নম্বৰ লিখক", "@form_whatsapp_hint": {"description": "Signup form field: form_whatsapp_hint", "context": "signup_form"}, "form_secondary_phone_optional": "দ্বিতীয় ফোন নম্বৰ (ঐচ্ছিক)", "@form_secondary_phone_optional": {"description": "Signup form field: form_secondary_phone_optional", "context": "signup_form"}, "form_secondary_phone_hint": "১০ সংখ্যাৰ দ্বিতীয় ফোন নম্বৰ লিখক", "@form_secondary_phone_hint": {"description": "Signup form field: form_secondary_phone_hint", "context": "signup_form"}, "btn_no_email": "মোৰ কোনো ইমেইল নাই", "@btn_no_email": {"description": "Signup button: btn_no_email", "context": "signup_form"}, "btn_request_signup": "চাইন আপৰ বাবে অনুৰোধ", "@btn_request_signup": {"description": "Signup button: btn_request_signup", "context": "signup_form"}, "btn_already_have_account": "ইতিমধ্যে একাউণ্ট আছে? লগইন কৰক", "@btn_already_have_account": {"description": "Signup button: btn_already_have_account", "context": "signup_form"}, "text_whatsapp_same_as_phone": "হোৱাটছএপ নম্বৰ ফোন নম্বৰৰ দৰেই", "@text_whatsapp_same_as_phone": {"description": "Signup text: text_whatsapp_same_as_phone", "context": "signup_form"}, "text_use_same_whatsapp": "হোৱাটছএপৰ বাবে একেটা নম্বৰ ব্যৱহাৰ কৰিবনে?", "@text_use_same_whatsapp": {"description": "Signup text: text_use_same_whatsapp", "context": "signup_form"}, "text_verified": "সত্যাপিত", "@text_verified": {"description": "Signup text: text_verified", "context": "signup_form"}, "msg_submitting_data": "ডেটা জমা দিয়া হৈছে। অনুগ্ৰহ কৰি অপেক্ষা কৰক!", "@msg_submitting_data": {"description": "Signup message: msg_submitting_data", "context": "signup_form"}, "msg_success": "সফল", "@msg_success": {"description": "Signup message: msg_success", "context": "signup_form"}, "msg_error": "ত্ৰুটি", "@msg_error": {"description": "Signup message: msg_error", "context": "signup_form"}, "msg_form_incomplete": "ফৰ্ম অসম্পূৰ্ণ", "@msg_form_incomplete": {"description": "Signup form field: msg_form_incomplete", "context": "signup_form"}, "msg_complete_required_fields": "অনুগ্ৰহ কৰি সকলো প্ৰয়োজনীয় ক্ষেত্ৰ সঠিকভাৱে পূৰণ কৰক", "@msg_complete_required_fields": {"description": "Signup message: msg_complete_required_fields", "context": "signup_form"}, "msg_information": "তথ্য", "@msg_information": {"description": "Signup message: msg_information", "context": "signup_form"}, "msg_complete_fields_order": "অনুগ্ৰহ কৰি সকলো ক্ষেত্ৰ ক্ৰমানুসাৰে পূৰণ কৰক। প্ৰতিটো ক্ষেত্ৰ কেৱল তেতিয়াহে সক্ৰিয় হ'ব যেতিয়া পূৰ্ববৰ্তী ক্ষেত্ৰটো সঠিকভাৱে পূৰণ আৰু সত্যাপিত কৰা হ'ব।", "@msg_complete_fields_order": {"description": "Signup message: msg_complete_fields_order", "context": "signup_form"}, "error_enter_first_name": "অনুগ্ৰহ কৰি আপোনাৰ প্ৰথম নাম লিখক", "@error_enter_first_name": {"description": "Signup error: error_enter_first_name", "context": "signup_error"}, "error_enter_last_name": "অনুগ্ৰহ কৰি আপোনাৰ শেষ নাম লিখক", "@error_enter_last_name": {"description": "Signup error: error_enter_last_name", "context": "signup_error"}, "error_enter_whatsapp": "অনুগ্ৰহ কৰি হোৱাটছএপ নম্বৰ লিখক", "@error_enter_whatsapp": {"description": "Signup error: error_enter_whatsapp", "context": "signup_error"}, "error_whatsapp_10_digits": "হোৱাটছএপ নম্বৰ অৱশ্যেই ঠিক ১০ সংখ্যাৰ হ'ব লাগিব", "@error_whatsapp_10_digits": {"description": "Signup error: error_whatsapp_10_digits", "context": "signup_error"}, "error_enter_only_numbers": "অনুগ্ৰহ কৰি কেৱল সংখ্যা লিখক", "@error_enter_only_numbers": {"description": "Signup error: error_enter_only_numbers", "context": "signup_error"}, "error_secondary_phone_10_digits": "দ্বিতীয় ফোন নম্বৰ অৱশ্যেই ঠিক ১০ সংখ্যাৰ হ'ব লাগিব", "@error_secondary_phone_10_digits": {"description": "Signup error: error_secondary_phone_10_digits", "context": "signup_error"}, "error_phone_numbers_different": "ফোন নম্বৰ আৰু দ্বিতীয় ফোন নম্বৰ অৱশ্যেই বেলেগ হ'ব লাগিব", "@error_phone_numbers_different": {"description": "Signup error: error_phone_numbers_different", "context": "signup_error"}, "error_request_error": "অনুৰোধ ত্ৰুটি", "@error_request_error": {"description": "Signup error: error_request_error", "context": "signup_error"}, "error_phone_number": "ফোন নম্বৰ", "@error_phone_number": {"description": "Signup error: error_phone_number", "context": "signup_error"}, "error_whatsapp_number": "হোৱাটছএপ নম্বৰ", "@error_whatsapp_number": {"description": "Signup error: error_whatsapp_number", "context": "signup_error"}, "error_email_id": "ইমেইল আইডি", "@error_email_id": {"description": "Signup error: error_email_id", "context": "signup_error"}, "error_emp_number": "কৰ্মচাৰী নম্বৰ", "@error_emp_number": {"description": "Signup error: error_emp_number", "context": "signup_error"}, "error_update_marked_info": "অনুগ্ৰহ কৰি ৰঙা ক্ৰছ দিয়ে চিহ্নিত তথ্য আপডেট কৰক আৰু আকৌ চেষ্টা কৰক।", "@error_update_marked_info": {"description": "Signup error: error_update_marked_info", "context": "signup_error"}, "status_new": "নতুন", "@status_new": {"description": "Signup status: status_new", "context": "signup_error"}, "status_already_taken": "ইতিমধ্যে লোৱা হৈছে", "@status_already_taken": {"description": "Signup status: status_already_taken", "context": "signup_error"}, "status_already_requested": "ইতিমধ্যে অনুৰোধ কৰা হৈছে", "@status_already_requested": {"description": "Signup status: status_already_requested", "context": "signup_error"}, "status_unknown": "অজ্ঞাত", "@status_unknown": {"description": "Signup status: status_unknown", "context": "signup_error"}}