{"@@locale": "hi", "appTitle": "रेलऑप्स", "@appTitle": {"description": "The title of the application"}, "cancel": "", "confirm": "", "delete": "", "edit": "", "error": "", "home": "", "loading": "", "login": "लॉगिन", "@login": {"description": "Login button text"}, "logout": "", "next": "", "no": "", "ok": "", "password": "", "previous": "", "refresh": "", "retry": "", "save": "", "search": "", "settings": "", "submit": "", "username": "", "welcome": "रेलऑप्स में आपका स्वागत है", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "", "test_welcome": "नमस्ते ऐप में", "@test_welcome": {"description": "Welcome message for testing", "context": "test_context"}, "test_login": "लॉगिन", "@test_login": {"description": "Login button text", "context": "button_labels"}, "text_menu": "मेनू", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "text_train_tracker": "ट्रेन ट्रैकर", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "CA असाइन करें", "@text_assign_ca": {"description": "Text from text_widgets: Assign CA", "context": "text_widgets"}, "text_assign_cs": "CS असाइन करें", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR विवरण", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_passenger_chart": "यात्री चार्ट", "@text_passenger_chart": {"description": "Text from text_widgets: Passenger Chart", "context": "text_widgets"}, "text_map_screen": "मैप स्क्रीन", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "कॉन्फ़िगरेशन", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "रिपोर्ट", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "यात्री फीडबैक", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "रेक कमी रिपोर्ट", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS से MCC हैंडओवर", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC से OBHS हैंडओवर", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "डेटा अपलोड करें", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "उपयोगकर्ता प्रबंधन", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "समस्या प्रबंधन", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "रेल साथी QR", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "ग्राहक सेवा", "@text_customer_care": {"description": "Text from text_widgets: Customer Care", "context": "text_widgets"}, "text_reenter_otp": "OTP पुनः दर्ज करें", "@text_reenter_otp": {"description": "Text from text_widgets: Re-enter OTP", "context": "text_widgets"}, "text_deny": "अस्वीकार करें", "@text_deny": {"description": "Text from text_widgets: <PERSON>y", "context": "text_widgets"}, "text_enable": "सक्षम करें", "@text_enable": {"description": "Text from text_widgets: Enable", "context": "text_widgets"}, "text_location_access_required": "स्थान पहुंच आवश्यक", "@text_location_access_required": {"description": "Text from text_widgets: Location Access Required", "context": "text_widgets"}, "text_decline": "अस्वीकार करें", "@text_decline": {"description": "Text from text_widgets: Decline", "context": "text_widgets"}, "text_accept": "स्वीकार करें", "@text_accept": {"description": "Text from text_widgets: Accept", "context": "text_widgets"}, "text_confirm_delete": "हटाने की पुष्टि करें", "@text_confirm_delete": {"description": "Text from text_widgets: Confirm Delete", "context": "text_widgets"}, "text_cancel": "रद्<PERSON> करें", "@text_cancel": {"description": "Cancel button text", "context": "general"}, "text_delete": "हटाएं", "@text_delete": {"description": "Text from text_widgets: Delete", "context": "text_widgets"}, "text_storage_permission_is": "फाइलें डाउनलोड करने के लिए स्टोरेज अनुमति आवश्यक है", "@text_storage_permission_is": {"description": "Text from text_widgets: Storage permission is required to download files", "context": "text_widgets"}, "text_please_select_a": "कृपया पहले एक ट्रेन चुनें", "@text_please_select_a": {"description": "Text from text_widgets: Please select a train first", "context": "text_widgets"}, "text_update_required": "अपडेट आवश्यक", "@text_update_required": {"description": "Text from text_widgets: Update Required", "context": "text_widgets"}, "text_update_now": "अभी अपडेट करें", "@text_update_now": {"description": "Text from text_widgets: Update Now", "context": "text_widgets"}, "text_please_select_a_1": "कृपया ट्रेन नंबर और तारीख चुनें।", "@text_please_select_a_1": {"description": "Text from text_widgets: Please select a train number and date.", "context": "text_widgets"}, "text_all_details_updated": "सभी विवरण सफलतापूर्वक अपडेट किए गए।", "@text_all_details_updated": {"description": "Text from text_widgets: All details updated successfully.", "context": "text_widgets"}, "text_failed_to_update": "विवरण अपडेट करने में विफल: $e", "@text_failed_to_update": {"description": "Text from text_widgets: Failed to update details: $e", "context": "text_widgets"}, "text_data_refreshed_successfully": "डेटा सफलतापूर्वक रीफ्रेश किया गया", "@text_data_refreshed_successfully": {"description": "Text from text_widgets: Data refreshed successfully", "context": "text_widgets"}, "text_train_location_saved": "ट्रेन स्थान सफलतापूर्वक सहेजा गया", "@text_train_location_saved": {"description": "Text from text_widgets: Train Location Saved Successfully", "context": "text_widgets"}, "text_self": "स्वयं", "@text_self": {"description": "Text from text_widgets: Self", "context": "text_widgets"}, "text_other_ca": "अन्य CA", "@text_other_ca": {"description": "Text from text_widgets: Other CA", "context": "text_widgets"}, "btn_accept": "स्वीकार करें", "@btn_accept": {"description": "Button text for accept action", "context": "button_labels"}, "btn_add_configuration": "कॉन्फ़िगरेशन जोड़ें", "@btn_add_configuration": {"description": "Button text for add configuration", "context": "button_labels"}, "btn_cancel": "रद्<PERSON> करें", "@btn_cancel": {"description": "Button text for cancel action", "context": "button_labels"}, "btn_close": "ब<PERSON><PERSON> करें", "@btn_close": {"description": "<PERSON><PERSON> text for close action", "context": "button_labels"}, "btn_coach_handover_report": "कोच हैंडओवर रिपोर्ट", "@btn_coach_handover_report": {"description": "But<PERSON> text for coach handover report", "context": "button_labels"}, "btn_coach_issue_status": "कोच समस्या स्थिति", "@btn_coach_issue_status": {"description": "<PERSON><PERSON> text for coach issue status", "context": "button_labels"}, "btn_completed": "पूर्ण", "@btn_completed": {"description": "Button text for completed status", "context": "button_labels"}, "btn_decline": "अस्वीकार करें", "@btn_decline": {"description": "Button text for decline action", "context": "button_labels"}, "btn_delete": "हटाएं", "@btn_delete": {"description": "Button text for delete action", "context": "button_labels"}, "btn_deny": "अस्वीकार करें", "@btn_deny": {"description": "Button text for deny action", "context": "button_labels"}, "btn_edit_configuration": "कॉन्फ़िगरेशन संपादित करें", "@btn_edit_configuration": {"description": "Button text for edit configuration", "context": "button_labels"}, "btn_enable": "सक्षम करें", "@btn_enable": {"description": "Button text for enable action", "context": "button_labels"}, "btn_error_snapshoterror": "त्रुटि हुई", "@btn_error_snapshoterror": {"description": "Button text for error occurred", "context": "button_labels"}, "btn_no_coaches_available": "कोई कोच उपलब्ध नहीं", "@btn_no_coaches_available": {"description": "Button text for no coaches available", "context": "button_labels"}, "btn_no_complaints_found": "कोई शिकायत नहीं मिली", "@btn_no_complaints_found": {"description": "Button text for no complaints found", "context": "button_labels"}, "btn_no_data_available": "कोई डेटा उपलब्ध नहीं", "@btn_no_data_available": {"description": "Button text for no data available", "context": "button_labels"}, "btn_no_data_available_1": "कोई डेटा उपलब्ध नहीं", "@btn_no_data_available_1": {"description": "Button text for no data available (variant)", "context": "button_labels"}, "btn_no_feedback_available": "कोई फीडबैक उपलब्ध नहीं", "@btn_no_feedback_available": {"description": "Button text for no feedback available", "context": "button_labels"}, "btn_no_location_data": "कोई स्थान डेटा नहीं", "@btn_no_location_data": {"description": "Button text for no location data", "context": "button_labels"}, "btn_no_notifications_found": "कोई सूचना नहीं मिली", "@btn_no_notifications_found": {"description": "Button text for no notifications found", "context": "button_labels"}, "btn_no_passengers_found": "कोई यात्री नहीं मिला", "@btn_no_passengers_found": {"description": "Button text for no passengers found", "context": "button_labels"}, "btn_no_reports_found": "कोई रिपोर्ट नहीं मिली", "@btn_no_reports_found": {"description": "Button text for no reports found", "context": "button_labels"}, "btn_no_trains_found": "कोई ट्रेन नहीं मिली", "@btn_no_trains_found": {"description": "Button text for no trains found", "context": "button_labels"}, "btn_ok": "ठीक है", "@btn_ok": {"description": "Button text for OK action", "context": "button_labels"}, "btn_refresh": "रीफ्रेश करें", "@btn_refresh": {"description": "Button text for refresh action", "context": "button_labels"}, "btn_retry": "पुनः प्रयास करें", "@btn_retry": {"description": "Button text for retry action", "context": "button_labels"}, "btn_save": "सहेजें", "@btn_save": {"description": "Button text for save action", "context": "button_labels"}, "btn_submit": "<PERSON><PERSON><PERSON> करें", "@btn_submit": {"description": "Button text for submit action", "context": "button_labels"}, "btn_update": "अपडेट करें", "@btn_update": {"description": "Button text for update action", "context": "button_labels"}, "form_train_number": "ट्रेन संख्या", "@form_train_number": {"description": "Form label for train number", "context": "form_labels"}, "form_date": "तारीख", "@form_date": {"description": "Form label for date", "context": "form_labels"}, "form_time": "समय", "@form_time": {"description": "Form label for time", "context": "form_labels"}, "form_station": "स्टेशन", "@form_station": {"description": "Form label for station", "context": "form_labels"}, "form_coach": "कोच", "@form_coach": {"description": "Form label for coach", "context": "form_labels"}, "form_seat": "सीट", "@form_seat": {"description": "Form label for seat", "context": "form_labels"}, "form_passenger_name": "यात्री का नाम *", "@form_passenger_name": {"description": "Form label for passenger name", "context": "form_labels"}, "form_mobile_number": "मोबाइल नंबर", "@form_mobile_number": {"description": "Mobile number field label", "context": "login_form"}, "form_email_address": "ईमेल पता", "@form_email_address": {"description": "Form label for email address", "context": "form_labels"}, "form_remarks": "यात्री की टिप्पणी", "@form_remarks": {"description": "Form label for remarks", "context": "form_labels"}, "text_loading": "लोड हो रहा है", "@text_loading": {"description": "Critical translation for: text_loading", "context": "critical_translations"}, "text_error": "त्रुटि", "@text_error": {"description": "Critical translation for: text_error", "context": "critical_translations"}, "text_success": "सफलता", "@text_success": {"description": "Success dialog title", "context": "profile_forms"}, "text_warning": "चेतावनी", "@text_warning": {"description": "Critical translation for: text_warning", "context": "critical_translations"}, "text_info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ी", "@text_info": {"description": "Critical translation for: text_info", "context": "critical_translations"}, "text_no_data": "कोई डेटा नहीं", "@text_no_data": {"description": "Critical translation for: text_no_data", "context": "critical_translations"}, "text_please_wait": "कृपया प्रतीक्षा करें", "@text_please_wait": {"description": "Critical translation for: text_please_wait", "context": "critical_translations"}, "text_try_again": "पुनः प्रयास करें", "@text_try_again": {"description": "Critical translation for: text_try_again", "context": "critical_translations"}, "text_something_went_wrong": "कुछ गलत हुआ", "@text_something_went_wrong": {"description": "Critical translation for: text_something_went_wrong", "context": "critical_translations"}, "text_network_error": "नेटवर्क त्रुटि", "@text_network_error": {"description": "Critical translation for: text_network_error", "context": "critical_translations"}, "text_connection_failed": "कनेक्शन विफल", "@text_connection_failed": {"description": "Critical translation for: text_connection_failed", "context": "critical_translations"}, "text_timeout": "समय समाप्त", "@text_timeout": {"description": "Critical translation for: text_timeout", "context": "critical_translations"}, "text_sign_in": "साइन इन करें", "@text_sign_in": {"description": "Critical translation for: text_sign_in", "context": "critical_translations"}, "text_sign_out": "साइन आउट करें", "@text_sign_out": {"description": "Critical translation for: text_sign_out", "context": "critical_translations"}, "text_sign_up": "साइन अप करें", "@text_sign_up": {"description": "Critical translation for: text_sign_up", "context": "critical_translations"}, "text_forgot_password": "पासवर्ड भूल गए", "@text_forgot_password": {"description": "Critical translation for: text_forgot_password", "context": "critical_translations"}, "text_reset_password": "पासवर्ड रीसेट करें", "@text_reset_password": {"description": "Critical translation for: text_reset_password", "context": "critical_translations"}, "text_change_password": "पासवर्ड बदलें", "@text_change_password": {"description": "Critical translation for: text_change_password", "context": "critical_translations"}, "text_current_password": "वर्तमान पासवर्ड", "@text_current_password": {"description": "Critical translation for: text_current_password", "context": "critical_translations"}, "text_new_password": "नया पासवर्ड", "@text_new_password": {"description": "New password field label", "context": "change_password_form"}, "text_confirm_password": "पासवर्ड की पुष्टि करें", "@text_confirm_password": {"description": "Critical translation for: text_confirm_password", "context": "critical_translations"}, "text_invalid_credentials": "अमान्य क्रेडेंशियल", "@text_invalid_credentials": {"description": "Critical translation for: text_invalid_credentials", "context": "critical_translations"}, "text_account_locked": "खाता लॉक है", "@text_account_locked": {"description": "Critical translation for: text_account_locked", "context": "critical_translations"}, "text_session_expired": "सत्र समाप्त हो गया", "@text_session_expired": {"description": "Critical translation for: text_session_expired", "context": "critical_translations"}, "text_train_details": "ट्रेन विवरण", "@text_train_details": {"description": "Critical translation for: text_train_details", "context": "critical_translations"}, "text_train_status": "ट्रेन स्थिति", "@text_train_status": {"description": "Critical translation for: text_train_status", "context": "critical_translations"}, "text_train_schedule": "ट्रेन समय सारणी", "@text_train_schedule": {"description": "Critical translation for: text_train_schedule", "context": "critical_translations"}, "text_departure_time": "प्रस्थान समय", "@text_departure_time": {"description": "Critical translation for: text_departure_time", "context": "critical_translations"}, "text_arrival_time": "आगमन समय", "@text_arrival_time": {"description": "Critical translation for: text_arrival_time", "context": "critical_translations"}, "text_platform_number": "प्लेटफॉर्म संख्या", "@text_platform_number": {"description": "Critical translation for: text_platform_number", "context": "critical_translations"}, "text_coach_position": "कोच स्थिति", "@text_coach_position": {"description": "Critical translation for: text_coach_position", "context": "critical_translations"}, "text_seat_availability": "सीट उपलब्धता", "@text_seat_availability": {"description": "Critical translation for: text_seat_availability", "context": "critical_translations"}, "text_passenger_list": "यात्री सूची", "@text_passenger_list": {"description": "Critical translation for: text_passenger_list", "context": "critical_translations"}, "text_reservation_chart": "आरक्षण चार्ट", "@text_reservation_chart": {"description": "Critical translation for: text_reservation_chart", "context": "critical_translations"}, "text_waiting_list": "प्रतीक्षा सूची", "@text_waiting_list": {"description": "Critical translation for: text_waiting_list", "context": "critical_translations"}, "text_confirmed": "पुष्ट", "@text_confirmed": {"description": "Critical translation for: text_confirmed", "context": "critical_translations"}, "text_waitlisted": "प्रतीक्षा सूची में", "@text_waitlisted": {"description": "Critical translation for: text_waitlisted", "context": "critical_translations"}, "text_rac": "RAC", "@text_rac": {"description": "Critical translation for: text_rac", "context": "critical_translations"}, "text_cancelled": "रद्द", "@text_cancelled": {"description": "Critical translation for: text_cancelled", "context": "critical_translations"}, "text_current_location": "वर्तमान स्थान", "@text_current_location": {"description": "Critical translation for: text_current_location", "context": "critical_translations"}, "text_location_permission": "स्थान अनुमति", "@text_location_permission": {"description": "Critical translation for: text_location_permission", "context": "critical_translations"}, "text_enable_location": "स्थान सक्षम करें", "@text_enable_location": {"description": "Critical translation for: text_enable_location", "context": "critical_translations"}, "text_location_disabled": "स्थान अक्षम", "@text_location_disabled": {"description": "Critical translation for: text_location_disabled", "context": "critical_translations"}, "text_gps_not_available": "GPS उपलब्ध नहीं", "@text_gps_not_available": {"description": "Critical translation for: text_gps_not_available", "context": "critical_translations"}, "text_getting_location": "स्थान प्राप्त कर रहे हैं", "@text_getting_location": {"description": "Critical translation for: text_getting_location", "context": "critical_translations"}, "text_location_accuracy": "स्थान सटीकता", "@text_location_accuracy": {"description": "Critical translation for: text_location_accuracy", "context": "critical_translations"}, "text_distance": "दूरी", "@text_distance": {"description": "Critical translation for: text_distance", "context": "critical_translations"}, "text_latitude": "अक्षांश", "@text_latitude": {"description": "Critical translation for: text_latitude", "context": "critical_translations"}, "text_longitude": "देशांतर", "@text_longitude": {"description": "Critical translation for: text_longitude", "context": "critical_translations"}, "text_notifications": "सूचनाएं", "@text_notifications": {"description": "Critical translation for: text_notifications", "context": "critical_translations"}, "text_notification_settings": "सूचना सेटिंग्स", "@text_notification_settings": {"description": "Critical translation for: text_notification_settings", "context": "critical_translations"}, "text_push_notifications": "पुश सूचनाएं", "@text_push_notifications": {"description": "Critical translation for: text_push_notifications", "context": "critical_translations"}, "text_notification_permission": "सूचना अनुमति", "@text_notification_permission": {"description": "Critical translation for: text_notification_permission", "context": "critical_translations"}, "text_mark_as_read": "पढ़ा हुआ चिह्नित करें", "@text_mark_as_read": {"description": "Critical translation for: text_mark_as_read", "context": "critical_translations"}, "text_mark_all_read": "सभी को पढ़ा हुआ चिह्नित करें", "@text_mark_all_read": {"description": "Critical translation for: text_mark_all_read", "context": "critical_translations"}, "text_clear_notifications": "सूचनाएं साफ़ करें", "@text_clear_notifications": {"description": "Critical translation for: text_clear_notifications", "context": "critical_translations"}, "text_no_notifications": "कोई सूचना नहीं", "@text_no_notifications": {"description": "Critical translation for: text_no_notifications", "context": "critical_translations"}, "text_download_file": "फाइल डाउनलोड करें", "@text_download_file": {"description": "Critical translation for: text_download_file", "context": "critical_translations"}, "text_upload_file": "फाइल अपलोड करें", "@text_upload_file": {"description": "Critical translation for: text_upload_file", "context": "critical_translations"}, "text_file_size": "फाइल आकार", "@text_file_size": {"description": "Critical translation for: text_file_size", "context": "critical_translations"}, "text_file_type": "फाइल प्रकार", "@text_file_type": {"description": "Critical translation for: text_file_type", "context": "critical_translations"}, "text_download_complete": "डाउनलोड पूर्ण", "@text_download_complete": {"description": "Critical translation for: text_download_complete", "context": "critical_translations"}, "text_upload_complete": "अपलोड पूर्ण", "@text_upload_complete": {"description": "Critical translation for: text_upload_complete", "context": "critical_translations"}, "text_download_failed": "डाउनलोड विफल", "@text_download_failed": {"description": "Critical translation for: text_download_failed", "context": "critical_translations"}, "text_upload_failed": "अपलोड विफल", "@text_upload_failed": {"description": "Critical translation for: text_upload_failed", "context": "critical_translations"}, "text_file_not_found": "फाइल नहीं मिली", "@text_file_not_found": {"description": "Critical translation for: text_file_not_found", "context": "critical_translations"}, "text_invalid_file": "अमान्य फाइल", "@text_invalid_file": {"description": "Critical translation for: text_invalid_file", "context": "critical_translations"}, "text_take_photo": "फोटो लें", "@text_take_photo": {"description": "Critical translation for: text_take_photo", "context": "critical_translations"}, "text_choose_from_gallery": "गैलरी से चुनें", "@text_choose_from_gallery": {"description": "Critical translation for: text_choose_from_gallery", "context": "critical_translations"}, "text_camera_permission": "कैमरा अनुमति", "@text_camera_permission": {"description": "Critical translation for: text_camera_permission", "context": "critical_translations"}, "text_storage_permission": "स्टोरेज अनुमति", "@text_storage_permission": {"description": "Critical translation for: text_storage_permission", "context": "critical_translations"}, "text_photo_captured": "फोटो कैप्चर किया गया", "@text_photo_captured": {"description": "Critical translation for: text_photo_captured", "context": "critical_translations"}, "text_image_selected": "छवि चुनी गई", "@text_image_selected": {"description": "Critical translation for: text_image_selected", "context": "critical_translations"}, "text_compress_image": "छवि संपीड़ित करें", "@text_compress_image": {"description": "Critical translation for: text_compress_image", "context": "critical_translations"}, "text_image_quality": "छवि गुणवत्ता", "@text_image_quality": {"description": "Critical translation for: text_image_quality", "context": "critical_translations"}, "text_select_date": "तारीख चुनें", "@text_select_date": {"description": "Critical translation for: text_select_date", "context": "critical_translations"}, "text_select_time": "समय चुनें", "@text_select_time": {"description": "Critical translation for: text_select_time", "context": "critical_translations"}, "text_start_date": "प्रारंभ तारीख", "@text_start_date": {"description": "Critical translation for: text_start_date", "context": "critical_translations"}, "text_end_date": "समाप्ति तारीख", "@text_end_date": {"description": "Critical translation for: text_end_date", "context": "critical_translations"}, "text_start_time": "प्रार<PERSON>भ समय", "@text_start_time": {"description": "Critical translation for: text_start_time", "context": "critical_translations"}, "text_end_time": "समाप्ति समय", "@text_end_time": {"description": "Critical translation for: text_end_time", "context": "critical_translations"}, "text_duration": "अवधि", "@text_duration": {"description": "Critical translation for: text_duration", "context": "critical_translations"}, "text_hours": "घंटे", "@text_hours": {"description": "Critical translation for: text_hours", "context": "critical_translations"}, "text_minutes": "मिनट", "@text_minutes": {"description": "Critical translation for: text_minutes", "context": "critical_translations"}, "text_seconds": "सेकंड", "@text_seconds": {"description": "Critical translation for: text_seconds", "context": "critical_translations"}, "text_search_placeholder": "खोजें...", "@text_search_placeholder": {"description": "Critical translation for: text_search_placeholder", "context": "critical_translations"}, "text_search_results": "खोज परिणाम", "@text_search_results": {"description": "Critical translation for: text_search_results", "context": "critical_translations"}, "text_no_results": "कोई परिणाम नहीं", "@text_no_results": {"description": "Critical translation for: text_no_results", "context": "critical_translations"}, "text_filter_by": "द्वारा फिल्टर करें", "@text_filter_by": {"description": "Critical translation for: text_filter_by", "context": "critical_translations"}, "text_sort_by": "द्वारा क्रमबद्ध करें", "@text_sort_by": {"description": "Critical translation for: text_sort_by", "context": "critical_translations"}, "text_ascending": "आरोही", "@text_ascending": {"description": "Critical translation for: text_ascending", "context": "critical_translations"}, "text_descending": "अवरोही", "@text_descending": {"description": "Critical translation for: text_descending", "context": "critical_translations"}, "text_clear_filter": "फिल्टर साफ़ करें", "@text_clear_filter": {"description": "Critical translation for: text_clear_filter", "context": "critical_translations"}, "text_apply_filter": "फिल्टर लागू करें", "@text_apply_filter": {"description": "Critical translation for: text_apply_filter", "context": "critical_translations"}, "text_required_field": "आवश्यक फील्ड", "@text_required_field": {"description": "Critical translation for: text_required_field", "context": "critical_translations"}, "text_invalid_email": "अमान्य ईमेल", "@text_invalid_email": {"description": "Critical translation for: text_invalid_email", "context": "critical_translations"}, "text_invalid_phone": "अमान्य फोन नंबर", "@text_invalid_phone": {"description": "Critical translation for: text_invalid_phone", "context": "critical_translations"}, "text_password_too_short": "पासवर्ड बहुत छोटा", "@text_password_too_short": {"description": "Critical translation for: text_password_too_short", "context": "critical_translations"}, "text_passwords_dont_match": "पासवर्ड मेल नहीं खाते", "@text_passwords_dont_match": {"description": "Critical translation for: text_passwords_dont_match", "context": "critical_translations"}, "text_invalid_format": "अमान्य प्रारूप", "@text_invalid_format": {"description": "Critical translation for: text_invalid_format", "context": "critical_translations"}, "text_field_cannot_be_empty": "फील्ड खाली नहीं हो सकता", "@text_field_cannot_be_empty": {"description": "Critical translation for: text_field_cannot_be_empty", "context": "critical_translations"}, "text_permission_required": "अनुमति आवश्यक", "@text_permission_required": {"description": "Critical translation for: text_permission_required", "context": "critical_translations"}, "text_grant_permission": "अनुमति दें", "@text_grant_permission": {"description": "Critical translation for: text_grant_permission", "context": "critical_translations"}, "text_permission_denied": "अनुमति अस्वीकृत", "@text_permission_denied": {"description": "Critical translation for: text_permission_denied", "context": "critical_translations"}, "text_go_to_settings": "सेटिंग्स में जाएं", "@text_go_to_settings": {"description": "Critical translation for: text_go_to_settings", "context": "critical_translations"}, "text_enable_in_settings": "सेटिंग्स में सक्षम करें", "@text_enable_in_settings": {"description": "Critical translation for: text_enable_in_settings", "context": "critical_translations"}, "text_no_internet": "इंटरनेट नहीं", "@text_no_internet": {"description": "Critical translation for: text_no_internet", "context": "critical_translations"}, "text_poor_connection": "खराब कनेक्शन", "@text_poor_connection": {"description": "Critical translation for: text_poor_connection", "context": "critical_translations"}, "text_connecting": "कनेक्ट हो रहा है", "@text_connecting": {"description": "Critical translation for: text_connecting", "context": "critical_translations"}, "text_connected": "जुड़ा हुआ", "@text_connected": {"description": "Critical translation for: text_connected", "context": "critical_translations"}, "text_disconnected": "डिस्कनेक्ट", "@text_disconnected": {"description": "Critical translation for: text_disconnected", "context": "critical_translations"}, "text_reconnecting": "पुनः कनेक्ट हो रहा है", "@text_reconnecting": {"description": "Critical translation for: text_reconnecting", "context": "critical_translations"}, "text_coach_details": "कोच विवरण", "@text_coach_details": {"description": "Critical translation for: text_coach_details", "context": "critical_translations"}, "text_passenger_details": "यात्री विवरण", "@text_passenger_details": {"description": "Critical translation for: text_passenger_details", "context": "critical_translations"}, "text_seat_number": "सीट संख्या", "@text_seat_number": {"description": "Critical translation for: text_seat_number", "context": "critical_translations"}, "text_berth_type": "बर्थ प्रकार", "@text_berth_type": {"description": "Critical translation for: text_berth_type", "context": "critical_translations"}, "text_upper_berth": "ऊपरी बर्थ", "@text_upper_berth": {"description": "Critical translation for: text_upper_berth", "context": "critical_translations"}, "text_middle_berth": "मध्य बर्थ", "@text_middle_berth": {"description": "Critical translation for: text_middle_berth", "context": "critical_translations"}, "text_lower_berth": "निचली बर्थ", "@text_lower_berth": {"description": "Critical translation for: text_lower_berth", "context": "critical_translations"}, "text_side_upper": "साइड ऊपरी", "@text_side_upper": {"description": "Critical translation for: text_side_upper", "context": "critical_translations"}, "text_side_lower": "साइड निचली", "@text_side_lower": {"description": "Critical translation for: text_side_lower", "context": "critical_translations"}, "text_occupied": "कब्जे में", "@text_occupied": {"description": "Critical translation for: text_occupied", "context": "critical_translations"}, "text_vacant": "खाली", "@text_vacant": {"description": "Critical translation for: text_vacant", "context": "critical_translations"}, "text_reserved": "आरक्षित", "@text_reserved": {"description": "Critical translation for: text_reserved", "context": "critical_translations"}, "text_generate_report": "रिपोर्ट जेनरेट करें", "@text_generate_report": {"description": "Critical translation for: text_generate_report", "context": "critical_translations"}, "text_export_data": "डेटा निर्यात करें", "@text_export_data": {"description": "Critical translation for: text_export_data", "context": "critical_translations"}, "text_print_report": "रिपोर्ट प्रिंट करें", "@text_print_report": {"description": "Critical translation for: text_print_report", "context": "critical_translations"}, "text_share_report": "रिपोर्ट साझा करें", "@text_share_report": {"description": "Critical translation for: text_share_report", "context": "critical_translations"}, "text_report_generated": "रिपोर्ट जेनरेट हुई", "@text_report_generated": {"description": "Critical translation for: text_report_generated", "context": "critical_translations"}, "text_no_data_to_export": "निर्यात के लिए कोई डेटा नहीं", "@text_no_data_to_export": {"description": "Critical translation for: text_no_data_to_export", "context": "critical_translations"}, "text_app_settings": "ऐप सेटिंग्स", "@text_app_settings": {"description": "Critical translation for: text_app_settings", "context": "critical_translations"}, "text_language_settings": "भाषा सेटिंग्स", "@text_language_settings": {"description": "Critical translation for: text_language_settings", "context": "critical_translations"}, "text_theme_settings": "थीम सेटिंग्स", "@text_theme_settings": {"description": "Critical translation for: text_theme_settings", "context": "critical_translations"}, "text_privacy_settings": "गोपनीयता सेटिंग्स", "@text_privacy_settings": {"description": "Critical translation for: text_privacy_settings", "context": "critical_translations"}, "text_security_settings": "सुरक्षा सेटिंग्स", "@text_security_settings": {"description": "Critical translation for: text_security_settings", "context": "critical_translations"}, "text_backup_settings": "बैकअप सेटिंग्स", "@text_backup_settings": {"description": "Critical translation for: text_backup_settings", "context": "critical_translations"}, "text_restore_settings": "सेटिंग्स पुनर्स्थापित करें", "@text_restore_settings": {"description": "Critical translation for: text_restore_settings", "context": "critical_translations"}, "text_reset_settings": "सेटिंग्स रीसेट करें", "@text_reset_settings": {"description": "Critical translation for: text_reset_settings", "context": "critical_translations"}, "text_next": "अगला", "@text_next": {"description": "Critical translation for: text_next", "context": "critical_translations"}, "text_previous": "पिछला", "@text_previous": {"description": "Critical translation for: text_previous", "context": "critical_translations"}, "text_finish": "समाप्त", "@text_finish": {"description": "Critical translation for: text_finish", "context": "critical_translations"}, "text_skip": "छोड़ें", "@text_skip": {"description": "Critical translation for: text_skip", "context": "critical_translations"}, "text_continue": "जारी रखें", "@text_continue": {"description": "Critical translation for: text_continue", "context": "critical_translations"}, "text_back": "वापस", "@text_back": {"description": "Critical translation for: text_back", "context": "critical_translations"}, "text_forward": "आगे", "@text_forward": {"description": "Critical translation for: text_forward", "context": "critical_translations"}, "text_up": "ऊपर", "@text_up": {"description": "Critical translation for: text_up", "context": "critical_translations"}, "text_down": "नीचे", "@text_down": {"description": "Critical translation for: text_down", "context": "critical_translations"}, "text_left": "बाएं", "@text_left": {"description": "Critical translation for: text_left", "context": "critical_translations"}, "text_right": "दाएं", "@text_right": {"description": "Critical translation for: text_right", "context": "critical_translations"}, "text_expand": "विस्तार करें", "@text_expand": {"description": "Critical translation for: text_expand", "context": "critical_translations"}, "text_collapse": "संक्षिप्त करें", "@text_collapse": {"description": "Critical translation for: text_collapse", "context": "critical_translations"}, "text_maximize": "अधिकतम करें", "@text_maximize": {"description": "Critical translation for: text_maximize", "context": "critical_translations"}, "text_minimize": "न्यूनतम करें", "@text_minimize": {"description": "Critical translation for: text_minimize", "context": "critical_translations"}, "btn_other": "अन्य", "@btn_other": {"description": "Comprehensive translation for: btn_other", "context": "comprehensive_phase1_phase2"}, "btn_other_ca": "अन्य CA", "@btn_other_ca": {"description": "Comprehensive translation for: btn_other_ca", "context": "comprehensive_phase1_phase2"}, "btn_other_ehkobhs": "अन्य EHK/OBHS", "@btn_other_ehkobhs": {"description": "Comprehensive translation for: btn_other_ehkobhs", "context": "comprehensive_phase1_phase2"}, "btn_pending": "लंबित", "@btn_pending": {"description": "Comprehensive translation for: btn_pending", "context": "comprehensive_phase1_phase2"}, "btn_rake_deficiency_report": "रेक कमी रिपोर्ट", "@btn_rake_deficiency_report": {"description": "Comprehensive translation for: btn_rake_deficiency_report", "context": "comprehensive_phase1_phase2"}, "btn_save_selection": "चयन सहेजें", "@btn_save_selection": {"description": "Comprehensive translation for: btn_save_selection", "context": "comprehensive_phase1_phase2"}, "btn_select_charting_day": "चार्टिंग दिन चुनें", "@btn_select_charting_day": {"description": "Comprehensive translation for: btn_select_charting_day", "context": "comprehensive_phase1_phase2"}, "btn_self": "स्वयं", "@btn_self": {"description": "Comprehensive translation for: btn_self", "context": "comprehensive_phase1_phase2"}, "btn_upload_json_data": "JSON डेटा अपलोड करें", "@btn_upload_json_data": {"description": "Comprehensive translation for: btn_upload_json_data", "context": "comprehensive_phase1_phase2"}, "btn_your_current_location": "आपका वर्तमान स्थान", "@btn_your_current_location": {"description": "Comprehensive translation for: btn_your_current_location", "context": "comprehensive_phase1_phase2"}, "form_add_stoppage": "स्टॉपेज जोड़ें", "@form_add_stoppage": {"description": "Comprehensive translation for: form_add_stoppage", "context": "comprehensive_phase1_phase2"}, "form_add_your_comments": "अपनी टिप्पणी जोड़ें", "@form_add_your_comments": {"description": "Comprehensive translation for: form_add_your_comments", "context": "comprehensive_phase1_phase2"}, "form_add_your_feedback": "अपना फीडबैक जोड़ें", "@form_add_your_feedback": {"description": "Comprehensive translation for: form_add_your_feedback", "context": "comprehensive_phase1_phase2"}, "form_coach_number": "कोच संख्या", "@form_coach_number": {"description": "Comprehensive translation for: form_coach_number", "context": "comprehensive_phase1_phase2"}, "form_departure_station": "प्रस्थान स्टेशन", "@form_departure_station": {"description": "Comprehensive translation for: form_departure_station", "context": "comprehensive_phase1_phase2"}, "form_destination_station": "गंतव्य स्टेशन", "@form_destination_station": {"description": "Comprehensive translation for: form_destination_station", "context": "comprehensive_phase1_phase2"}, "form_enter_coach_number": "कोच संख्या दर्ज करें", "@form_enter_coach_number": {"description": "Comprehensive translation for: form_enter_coach_number", "context": "comprehensive_phase1_phase2"}, "form_enter_train_number": "ट्रेन संख्या दर्ज करें", "@form_enter_train_number": {"description": "Comprehensive translation for: form_enter_train_number", "context": "comprehensive_phase1_phase2"}, "form_journey_date": "यात्रा तारीख", "@form_journey_date": {"description": "Comprehensive translation for: form_journey_date", "context": "comprehensive_phase1_phase2"}, "form_select_date": "तारीख चुनें", "@form_select_date": {"description": "Comprehensive translation for: form_select_date", "context": "comprehensive_phase1_phase2"}, "form_select_station": "स्टेशन चुनें", "@form_select_station": {"description": "Comprehensive translation for: form_select_station", "context": "comprehensive_phase1_phase2"}, "form_select_train": "ट्रेन चुनें", "@form_select_train": {"description": "Comprehensive translation for: form_select_train", "context": "comprehensive_phase1_phase2"}, "text_attendance": "उपस्थिति", "@text_attendance": {"description": "Comprehensive translation for: text_attendance", "context": "comprehensive_phase1_phase2"}, "text_chart_prepared": "चार्ट तैयार", "@text_chart_prepared": {"description": "Comprehensive translation for: text_chart_prepared", "context": "comprehensive_phase1_phase2"}, "text_coach_assignment": "कोच असाइनमेंट", "@text_coach_assignment": {"description": "Comprehensive translation for: text_coach_assignment", "context": "comprehensive_phase1_phase2"}, "text_current_status": "वर्तमान स्थिति", "@text_current_status": {"description": "Comprehensive translation for: text_current_status", "context": "comprehensive_phase1_phase2"}, "text_data_sync": "डेटा सिंक", "@text_data_sync": {"description": "Comprehensive translation for: text_data_sync", "context": "comprehensive_phase1_phase2"}, "text_download_chart": "चार्ट डाउनलोड करें", "@text_download_chart": {"description": "Comprehensive translation for: text_download_chart", "context": "comprehensive_phase1_phase2"}, "text_journey_details": "यात्रा विवरण", "@text_journey_details": {"description": "Comprehensive translation for: text_journey_details", "context": "comprehensive_phase1_phase2"}, "text_location_update": "स्थान अपडेट", "@text_location_update": {"description": "Comprehensive translation for: text_location_update", "context": "comprehensive_phase1_phase2"}, "text_passenger_count": "यात्री संख्या", "@text_passenger_count": {"description": "Comprehensive translation for: text_passenger_count", "context": "comprehensive_phase1_phase2"}, "text_platform_info": "प्लेटफॉर्म जानकारी", "@text_platform_info": {"description": "Comprehensive translation for: text_platform_info", "context": "comprehensive_phase1_phase2"}, "text_rake_composition": "रेक संरचना", "@text_rake_composition": {"description": "Comprehensive translation for: text_rake_composition", "context": "comprehensive_phase1_phase2"}, "text_station_code": "स्टेशन कोड", "@text_station_code": {"description": "Comprehensive translation for: text_station_code", "context": "comprehensive_phase1_phase2"}, "text_train_composition": "ट्रेन संरचना", "@text_train_composition": {"description": "Comprehensive translation for: text_train_composition", "context": "comprehensive_phase1_phase2"}, "text_upload_status": "अपलोड स्थिति", "@text_upload_status": {"description": "Comprehensive translation for: text_upload_status", "context": "comprehensive_phase1_phase2"}, "error_connection_timeout": "कनेक्शन समय समाप्त", "@error_connection_timeout": {"description": "Comprehensive translation for: error_connection_timeout", "context": "comprehensive_phase1_phase2"}, "error_data_not_found": "डेटा नहीं मिला", "@error_data_not_found": {"description": "Comprehensive translation for: error_data_not_found", "context": "comprehensive_phase1_phase2"}, "error_invalid_input": "अमान्य इनपुट", "@error_invalid_input": {"description": "Comprehensive translation for: error_invalid_input", "context": "comprehensive_phase1_phase2"}, "error_network_unavailable": "नेटवर्क अनुपलब्ध", "@error_network_unavailable": {"description": "Comprehensive translation for: error_network_unavailable", "context": "comprehensive_phase1_phase2"}, "error_permission_denied": "अनुमति अस्वीकृत", "@error_permission_denied": {"description": "Comprehensive translation for: error_permission_denied", "context": "comprehensive_phase1_phase2"}, "error_server_error": "सर्वर त्रुटि", "@error_server_error": {"description": "Comprehensive translation for: error_server_error", "context": "comprehensive_phase1_phase2"}, "error_upload_failed": "अपलोड विफल", "@error_upload_failed": {"description": "Comprehensive translation for: error_upload_failed", "context": "comprehensive_phase1_phase2"}, "success_data_saved": "डेटा सहेजा गया", "@success_data_saved": {"description": "Comprehensive translation for: success_data_saved", "context": "comprehensive_phase1_phase2"}, "success_upload_complete": "अपलोड पूर्ण", "@success_upload_complete": {"description": "Comprehensive translation for: success_upload_complete", "context": "comprehensive_phase1_phase2"}, "success_sync_complete": "सिंक पूर्ण", "@success_sync_complete": {"description": "Comprehensive translation for: success_sync_complete", "context": "comprehensive_phase1_phase2"}, "msg_loading_data": "डेटा लोड हो रहा है", "@msg_loading_data": {"description": "Comprehensive translation for: msg_loading_data", "context": "comprehensive_phase1_phase2"}, "msg_processing": "प्रसंस्करण", "@msg_processing": {"description": "Comprehensive translation for: msg_processing", "context": "comprehensive_phase1_phase2"}, "msg_please_wait": "कृपया प्रतीक्षा करें", "@msg_please_wait": {"description": "Comprehensive translation for: msg_please_wait", "context": "comprehensive_phase1_phase2"}, "msg_no_data_available": "कोई डेटा उपलब्ध नहीं", "@msg_no_data_available": {"description": "Comprehensive translation for: msg_no_data_available", "context": "comprehensive_phase1_phase2"}, "msg_select_option": "विकल्प चुनें", "@msg_select_option": {"description": "Comprehensive translation for: msg_select_option", "context": "comprehensive_phase1_phase2"}, "nav_dashboard": "डैशबोर्ड", "@nav_dashboard": {"description": "Comprehensive translation for: nav_dashboard", "context": "comprehensive_phase1_phase2"}, "nav_reports": "रिपोर्ट्स", "@nav_reports": {"description": "Comprehensive translation for: nav_reports", "context": "comprehensive_phase1_phase2"}, "nav_notifications": "सूचनाएं", "@nav_notifications": {"description": "Comprehensive translation for: nav_notifications", "context": "comprehensive_phase1_phase2"}, "nav_profile": "प्रोफाइल", "@nav_profile": {"description": "Comprehensive translation for: nav_profile", "context": "comprehensive_phase1_phase2"}, "nav_help": "सहायता", "@nav_help": {"description": "Comprehensive translation for: nav_help", "context": "comprehensive_phase1_phase2"}, "nav_about": "के बारे में", "@nav_about": {"description": "Comprehensive translation for: nav_about", "context": "comprehensive_phase1_phase2"}, "time_now": "अभी", "@time_now": {"description": "Comprehensive translation for: time_now", "context": "comprehensive_phase1_phase2"}, "time_today": "आज", "@time_today": {"description": "Comprehensive translation for: time_today", "context": "comprehensive_phase1_phase2"}, "time_yesterday": "कल", "@time_yesterday": {"description": "Comprehensive translation for: time_yesterday", "context": "comprehensive_phase1_phase2"}, "time_tomorrow": "कल", "@time_tomorrow": {"description": "Comprehensive translation for: time_tomorrow", "context": "comprehensive_phase1_phase2"}, "date_format": "दिनांक प्रारूप", "@date_format": {"description": "Comprehensive translation for: date_format", "context": "comprehensive_phase1_phase2"}, "time_format": "समय प्रारूप", "@time_format": {"description": "Comprehensive translation for: time_format", "context": "comprehensive_phase1_phase2"}, "text_change_mobile": "मोबाइल बदलें", "@text_change_mobile": {"description": "Title for change mobile screen", "context": "change_mobile_screen"}, "text_change_whatsapp": "व्हाट्सऐप नंबर बदलें", "@text_change_whatsapp": {"description": "Title for change whatsapp screen", "context": "change_whatsapp_screen"}, "text_alert": "चेतावनी", "@text_alert": {"description": "Alert dialog title", "context": "profile_forms"}, "text_close": "ब<PERSON><PERSON> करें", "@text_close": {"description": "Close button text", "context": "profile_forms"}, "text_change_your_email": "अपना ईमेल बदलें", "@text_change_your_email": {"description": "Change email form title", "context": "change_email_form"}, "text_current_email": "वर्तमान ईमेल", "@text_current_email": {"description": "Current email field label", "context": "change_email_form"}, "text_new_email": "नया ईमेल", "@text_new_email": {"description": "New email field label", "context": "change_email_form"}, "text_please_enter_new_email": "कृपया नया ईमेल दर्ज करें", "@text_please_enter_new_email": {"description": "Validation message for new email field", "context": "change_email_form"}, "text_otp": "ओटीपी", "@text_otp": {"description": "OTP field label", "context": "profile_forms"}, "text_resend_otp": "ओटीपी पुनः भेजें", "@text_resend_otp": {"description": "Resend OTP button text", "context": "profile_forms"}, "text_resend_in_seconds": "{seconds} सेकंड में पुनः भेजें", "@text_resend_in_seconds": {"description": "Resend countdown text", "context": "profile_forms", "placeholders": {"seconds": {"type": "int", "description": "Number of seconds remaining"}}}, "text_verify_otp": "ओटीपी सत्यापित करें", "@text_verify_otp": {"description": "Verify OTP button text", "context": "profile_forms"}, "text_generate_otp": "ओटीपी जेनरेट करें", "@text_generate_otp": {"description": "Generate OTP button text", "context": "profile_forms"}, "text_send_otp": "ओटीपी भेजें", "@text_send_otp": {"description": "Send OTP button text", "context": "change_email_modal"}, "text_failed_to_send_otp": "ओटीपी भेजने में विफल: {error}", "@text_failed_to_send_otp": {"description": "Error message when OTP sending fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_email_saved_successfully": "ईमेल सफलतापूर्वक सेव हो गया!", "@text_email_saved_successfully": {"description": "Success message when email is saved", "context": "change_email_modal"}, "text_failed_to_verify_otp": "ओटीपी सत्यापन में विफल: {error}", "@text_failed_to_verify_otp": {"description": "Error message when OTP verification fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_change_your_password": "अपना पासवर्ड बदलें", "@text_change_your_password": {"description": "Change password form title", "context": "change_password_form"}, "text_old_password": "पुराना पासवर्ड", "@text_old_password": {"description": "Old password field label", "context": "change_password_form"}, "text_confirm_new_password": "नया पासवर्ड पुष्टि करें", "@text_confirm_new_password": {"description": "Confirm new password field label", "context": "change_password_form"}, "text_please_enter_otp": "कृपया ओटीपी दर्ज करें", "@text_please_enter_otp": {"description": "Validation message for OTP field", "context": "profile_forms"}, "text_send_mobile_otp": "मोबाइल ओटीपी भेजें", "@text_send_mobile_otp": {"description": "Send mobile OTP button text", "context": "change_password_form"}, "text_send_email_otp": "ईमेल ओटीपी भेजें", "@text_send_email_otp": {"description": "Send email OTP button text", "context": "change_password_form"}, "text_please_enter_value": "कृपया एक मान दर्ज करें", "@text_please_enter_value": {"description": "Generic validation message for empty fields", "context": "profile_forms"}, "text_please_enter_valid_mobile": "कृपया एक वैध मोबाइल नंबर दर्ज करें", "@text_please_enter_valid_mobile": {"description": "Validation message for mobile number field", "context": "profile_forms"}, "text_ok": "ठीक है", "@text_ok": {"description": "OK button text", "context": "profile_forms"}, "text_change_your_mobile_number": "अपना मोबाइल नंबर बदलें", "@text_change_your_mobile_number": {"description": "Change mobile form title", "context": "change_mobile_form"}, "text_current_mobile_number": "वर्तमान मोबाइल नंबर", "@text_current_mobile_number": {"description": "Current mobile number field label", "context": "change_mobile_form"}, "text_new_mobile_number": "नया मोबाइल नंबर", "@text_new_mobile_number": {"description": "New mobile number field label", "context": "change_mobile_form"}, "text_please_enter_new_mobile": "कृपया नया मोबाइल नंबर दर्ज करें", "@text_please_enter_new_mobile": {"description": "Validation message for new mobile number field", "context": "change_mobile_form"}, "text_change_your_whatsapp_number": "अपना व्हाट्सऐप नंबर बदलें", "@text_change_your_whatsapp_number": {"description": "Change whatsapp form title", "context": "change_whatsapp_form"}, "text_current_whatsapp_number": "वर्तमान व्हाट्सऐप नंबर", "@text_current_whatsapp_number": {"description": "Current whatsapp number field label", "context": "change_whatsapp_form"}, "text_new_whatsapp_number": "नया व्हाट्सऐप नंबर", "@text_new_whatsapp_number": {"description": "New whatsapp number field label", "context": "change_whatsapp_form"}, "text_please_enter_new_whatsapp": "कृपया नया व्हाट्सऐप मोबाइल नंबर दर्ज करें", "@text_please_enter_new_whatsapp": {"description": "Validation message for new whatsapp number field", "context": "change_whatsapp_form"}, "text_train": "ट्रेन", "@text_train": {"description": "Table column header for train", "context": "add_train_screen"}, "text_coaches": "कोच", "@text_coaches": {"description": "Table column header for coaches", "context": "add_train_screen"}, "text_origin_date": "मूल तारीख", "@text_origin_date": {"description": "Table column header for origin date", "context": "add_train_screen"}, "text_na": "उपलब्ध नहीं", "@text_na": {"description": "Not available text", "context": "add_train_screen"}, "rm_feedback_app_bar_title": "रेलमदद यात्री फीडबैक", "rm_feedback_main_title": "रेलमदद फीडबैक", "form_pnr_number": "PNR नंबर *", "form_crn_number": "CRN नंबर*", "form_train_no": "ट्रेन नंबर *", "form_train_name": "ट्रेन का नाम *", "form_coach_no": "कोच नंबर *", "form_berth_no": "बर्थ नंबर *", "form_email_id": "ईमेल आईडी", "form_issue_type": "समस्या का प्रकार", "form_sub_issue_type": "उप समस्या का प्रकार", "form_resolved_status": "हल हो गया (हाँ/नहीं) *", "form_marks": "अंक (1 से 10) *", "form_task_status": "कार्य की स्थिति *", "btn_validate": "सत्यापित करें", "btn_verified": "सत्यापित", "btn_verify_email": "ईमेल सत्यापित करें", "btn_verify_otp": "OTP सत्यापित करें", "btn_submit_feedback": "फीडबैक जमा करें", "btn_upload_pnr_image": "PNR छवि अपलोड करें", "btn_pick_media": "फीडबैक के लिए छवि/वीडियो चुनें", "btn_camera": "कैमरा", "btn_gallery": "गैलरी", "btn_image": "छवि", "btn_video": "वीडियो", "btn_i_understand": "मैं समझ गया", "status_verified": "सत्यापित", "status_pending": "लंबित", "status_completed": "पूर्ण", "status_yes": "हाँ", "status_no": "नहीं", "status_select": "चुनें", "section_email_verification": "ईमेल सत्यापन (वैकल्पिक)", "section_selected_images": "चयनित छवियाँ:", "section_selected_videos": "चयनित वीडियो:", "dialog_email_verification_info": "ईमेल सत्यापन जानकारी", "dialog_select_media_type": "मीडिया प्रकार चुनें", "validation_fill_all_fields": "कृपया सभी फ़ील्ड को वैध जानकारी के साथ भरें।", "validation_pnr_digits": "PNR नंबर 8 या 10 अंकों का होना चाहिए", "validation_berth_number": "बर्थ नंबर एक वैध संख्या होनी चाहिए", "validation_feedback_length": "फीडबैक 100 अक्षरों से अधिक नहीं हो सकता", "validation_email_required": "कृपया एक वैध ईमेल आईडी दर्ज करें।", "validation_otp_required": "कृपया OTP दर्ज करें।", "validation_train_no_required": "ट्रेन नंबर आवश्यक है", "validation_train_name_required": "ट्रेन का नाम आवश्यक है", "validation_passenger_name_required": "यात्री का नाम आवश्यक है", "validation_mobile_required": "मोबाइल नंबर आवश्यक है", "validation_mobile_digits": "मोबाइल नंबर 10 अंकों का होना चाहिए", "validation_issue_type_required": "कृपया समस्या का प्रकार चुनें", "validation_sub_issue_required": "कृपया उप-समस्या का प्रकार चुनें", "validation_resolved_required": "कृपया हल की स्थिति चुनें", "validation_marks_required": "कृपया अंक चुनें", "msg_pnr_images_limit": "आप केवल 3 PNR छवियाँ चुन सकते हैं", "msg_feedback_images_limit": "अधिकतम 3 फीडबैक छवियों की अनुमति है", "msg_images_added_limit": "केवल {count} छवियाँ जोड़ी गईं। अधिकतम 3 की सीमा पहुँच गई।", "msg_error_picking_media": "मीडिया चुनने में त्रुटि: {error}", "msg_failed_fetch_train_name": "ट्रेन का नाम लाने में विफल", "msg_invalid_pnr": "अमान्य PNR नंबर।", "msg_pnr_success": "PNR विवरण सफलतापूर्वक प्राप्त किया गया।", "msg_pnr_validation_failed": "PNR विवरण सत्यापित करने में विफल। अमान्य PNR नंबर।", "msg_email_verification_sent": "ईमेल सत्यापन शुरू किया गया। कृपया अपने इनबॉक्स और स्पैम फ़ोल्डर दोनों की जाँच करें।।", "msg_otp_verified": "OTP सफलतापूर्वक सत्यापित।", "msg_feedback_submitted": "फीडबैक सफलतापूर्वक जमा किया गया!", "msg_feedback_failed": "फीडबैक जमा करने में विफल", "msg_unexpected_error": "एक अप्रत्याशित त्रुटि हुई। कृपया पुनः प्रयास करें।", "info_spam_folder_note": "कृपया ध्यान दें कि सत्यापन ईमेल कभी-कभी आपके स्पैम/जंक फ़ोल्डर में पहुंच सकते हैं।", "info_after_requesting_otp": "OTP का अनुरोध करने के बाद:", "info_check_inbox": "पहले अपना इनबॉक्स जाँचें", "info_check_spam": "यदि नहीं मिला, तो स्पैम/जंक फ़ोल्डर जाँचें", "info_add_safe_sender": "हमारे डोमेन को अपनी सुरक्षित भेजने वाली सूची में जोड़ें", "text_no_feedback_images": "कोई फीडबैक छवि चयनित नहीं", "text_no_pnr_images": "कोई PNR छवि चयनित नहीं", "text_character_count": "{count}/100 अक्षर", "loading_sending_otp": "OTP भेजा जा रहा है", "loading_verifying_otp": "OTP सत्यापित किया जा रहा है", "loading_submitting_feedback": "फीडबैक जमा किया जा रहा है", "attendance_api_summary": "API सारांश विवरण", "@attendance_api_summary": {"description": "Attendance: API Summary Details", "context": "attendance"}, "attendance_assigned_coaches": "🚆 आवंटित Coaches:", "@attendance_assigned_coaches": {"description": "Attendance: 🚆 Assigned Coaches:", "context": "attendance"}, "attendance_available_label": "उपलब्ध: {count}", "@attendance_available_label": {"description": "Attendance: Available: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_available_section": "उपलब्ध", "@attendance_available_section": {"description": "Attendance: Available", "context": "attendance"}, "attendance_berths_count": "{count} berths", "@attendance_berths_count": {"description": "Attendance: {count} berths", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_buffer_time_restriction": "The समय between the current समय and the ट्रेन's arrival समय is not within the 2-hour buffer.", "@attendance_buffer_time_restriction": {"description": "Attendance: The time between the current time and the train's arrival time is not within the 2-hour buffer.", "context": "attendance"}, "attendance_cancel": "रद्<PERSON> करें", "@attendance_cancel": {"description": "Attendance: Cancel", "context": "attendance"}, "attendance_cancelled": "Cancelled", "@attendance_cancelled": {"description": "Attendance: Cancelled", "context": "attendance"}, "attendance_chart_not_prepared": "चार्ट has not been prepared for this स्टेशन", "@attendance_chart_not_prepared": {"description": "Attendance: Chart has not been prepared for this station", "context": "attendance"}, "attendance_charting_refreshed": "चार्टिंग refreshed at: {time}", "@attendance_charting_refreshed": {"description": "Attendance: Charting refreshed at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_started": "चार्टिंग started at: {time}", "@attendance_charting_started": {"description": "Attendance: Charting started at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_time": "चार्टिंग समय: {time}", "@attendance_charting_time": {"description": "Attendance: Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_click_more": "click for more...", "@attendance_click_more": {"description": "Attendance: click for more...", "context": "attendance"}, "attendance_coach_label": "🚃 कोच: {coach}", "@attendance_coach_label": {"description": "Attendance: 🚃 Coach: {coach}", "context": "attendance", "placeholders": {"coach": {"type": "String"}}}, "attendance_coach_occupancy": "कोच Occupancy विवरण", "@attendance_coach_occupancy": {"description": "Attendance: Coach Occupancy <PERSON>", "context": "attendance"}, "attendance_coach_type": "कोच Type:", "@attendance_coach_type": {"description": "Attendance: Coach Type:", "context": "attendance"}, "attendance_coaches": "Coaches: {coaches}", "@attendance_coaches": {"description": "Attendance: Coaches: {coaches}", "context": "attendance", "placeholders": {"coaches": {"type": "String"}}}, "attendance_concise_view": "Concise दृश्य", "@attendance_concise_view": {"description": "Attendance: Concise View", "context": "attendance"}, "attendance_count_label": "A: {count}", "@attendance_count_label": {"description": "Attendance: A: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_daily": "Daily", "@attendance_daily": {"description": "Attendance: Daily", "context": "attendance"}, "attendance_data_refreshed": "डेटा refreshed सफलतापूर्वक", "@attendance_data_refreshed": {"description": "Attendance: Data refreshed successfully", "context": "attendance"}, "attendance_deboarding": "🔴 उतरना:", "@attendance_deboarding": {"description": "Attendance: 🔴 Deboarding:", "context": "attendance"}, "attendance_deboarding_none": "🔴 उतरना: None", "@attendance_deboarding_none": {"description": "Attendance: 🔴 Deboarding: None", "context": "attendance"}, "attendance_detailed_view": "Detailed दृश्य", "@attendance_detailed_view": {"description": "Attendance: Detailed View", "context": "attendance"}, "attendance_ehk_assigned": "EHK आवंटित for ट्रेन:{ehkName}", "@attendance_ehk_assigned": {"description": "Attendance: EHK Assigned for train:{ehkName}", "context": "attendance", "placeholders": {"ehkName": {"type": "String"}}}, "attendance_expected_charting": "Expected चार्टिंग समय: {time}", "@attendance_expected_charting": {"description": "Attendance: Expected Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_failed_load_data": "असफल to load detailed डेटा: {error}", "@attendance_failed_load_data": {"description": "Attendance: Failed to load detailed data: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_failed_update_status": "असफल to अपडेट करें ट्रेन स्थिति", "@attendance_failed_update_status": {"description": "Attendance: Failed to update train status", "context": "attendance"}, "attendance_go": "जाएं", "@attendance_go": {"description": "Attendance: Go", "context": "attendance"}, "attendance_in_route": "In-route", "@attendance_in_route": {"description": "Attendance: In-route", "context": "attendance"}, "attendance_inside": "अंदर", "@attendance_inside": {"description": "Attendance: Inside", "context": "attendance"}, "attendance_inside_train": "You are now चिह्नित as अंदर the ट्रेन", "@attendance_inside_train": {"description": "Attendance: You are now marked as inside the train", "context": "attendance"}, "attendance_journey_status_updated": "यात्रा स्थिति updated to {status}", "@attendance_journey_status_updated": {"description": "Attendance: Journey status updated to {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_last_fetched": "Last fetched: {time}", "@attendance_last_fetched": {"description": "Attendance: Last fetched: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_loading": "लोड हो रहा है...", "@attendance_loading": {"description": "Attendance: Loading...", "context": "attendance"}, "attendance_location_not_fetched": "ट्रेन स्थान is not fetched yet, कृपया try again later", "@attendance_location_not_fetched": {"description": "Attendance: Train Location is not fetched yet, please try again later", "context": "attendance"}, "attendance_na": "N/A", "@attendance_na": {"description": "Attendance: N/A", "context": "attendance"}, "attendance_near_stations": "You're near the following स्टेशन(s):", "@attendance_near_stations": {"description": "Attendance: You're near the following station(s):", "context": "attendance"}, "attendance_nearby_station_alert": "🛤️ नजदीकी स्टेशन चेतावनी", "@attendance_nearby_station_alert": {"description": "Attendance: 🛤️ Nearby Station Alert", "context": "attendance"}, "attendance_non_sleeper": "Non-Sleeper", "@attendance_non_sleeper": {"description": "Attendance: Non-Sleeper", "context": "attendance"}, "attendance_not_attendance_station": "उपस्थिति cannot be चिह्नित for स्टेशन {stationCode} as it is not an उपस्थिति स्टेशन.", "@attendance_not_attendance_station": {"description": "Attendance: Attendance cannot be marked for station {stationCode} as it is not an attendance station.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_not_inside_train": "You are not अंदर the ट्रेन. कृपया जाएं अंदर the ट्रेन first.", "@attendance_not_inside_train": {"description": "Attendance: You are not inside the train. Please go inside the train first.", "context": "attendance"}, "attendance_off_label": "Off: {count}", "@attendance_off_label": {"description": "Attendance: Off: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_offboarding_section": "Offboarding", "@attendance_offboarding_section": {"description": "Attendance: Offboarding", "context": "attendance"}, "attendance_ok": "ठीक", "@attendance_ok": {"description": "Attendance: OK", "context": "attendance"}, "attendance_on_label": "On: {count}", "@attendance_on_label": {"description": "Attendance: On: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_onboarding": "🟢 चढ़ना:", "@attendance_onboarding": {"description": "Attendance: 🟢 Onboarding:", "context": "attendance"}, "attendance_onboarding_none": "🟢 चढ़ना: None", "@attendance_onboarding_none": {"description": "Attendance: 🟢 Onboarding: None", "context": "attendance"}, "attendance_onboarding_section": "चढ़ना", "@attendance_onboarding_section": {"description": "Attendance: Onboarding", "context": "attendance"}, "attendance_other_ca": "Other CA", "@attendance_other_ca": {"description": "Attendance: Other CA", "context": "attendance"}, "attendance_other_ehk": "Other EHK/OBHS", "@attendance_other_ehk": {"description": "Attendance: Other EHK/OBHS", "context": "attendance"}, "attendance_outside_train": "You are now चिह्नित as बाहर the ट्रेन", "@attendance_outside_train": {"description": "Attendance: You are now marked as outside the train", "context": "attendance"}, "attendance_passenger_chart": "यात्री चार्ट   Atten..", "@attendance_passenger_chart": {"description": "Attendance: Passenger Chart   Atten..", "context": "attendance"}, "attendance_refresh_failed": "रिफ्रेश असफल: {error}", "@attendance_refresh_failed": {"description": "Attendance: Refresh failed: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_screen_title": "उपस्थिति", "@attendance_screen_title": {"description": "Attendance: Attendance", "context": "attendance"}, "attendance_select_date": "चुनें तारीख", "@attendance_select_date": {"description": "Attendance: Select date", "context": "attendance"}, "attendance_select_train_date": "कृपया चुनें a ट्रेन संख्या and तारीख.", "@attendance_select_train_date": {"description": "Attendance: Please select a train number and date.", "context": "attendance"}, "attendance_select_train_first": "कृपया चुनें a ट्रेन first", "@attendance_select_train_first": {"description": "Attendance: Please select a train first", "context": "attendance"}, "attendance_self": "Self", "@attendance_self": {"description": "Attendance: Self", "context": "attendance"}, "attendance_sleeper": "Sleeper", "@attendance_sleeper": {"description": "Attendance: Sleeper", "context": "attendance"}, "attendance_station_details": "स्टेशन विवरण - {stationCode}", "@attendance_station_details": {"description": "Attendance: Station Details - {stationCode}", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_stoppages": "Stoppages", "@attendance_stoppages": {"description": "Attendance: Stoppages", "context": "attendance"}, "attendance_timings": "Tim<PERSON>", "@attendance_timings": {"description": "Attendance: Timings", "context": "attendance"}, "attendance_today": "Today", "@attendance_today": {"description": "Attendance: Today", "context": "attendance"}, "attendance_too_far_from_station": "You're over 50 KM away from the selected स्टेशन {stationCode}. उपस्थिति can only be चिह्नित when you're within the allowed range.", "@attendance_too_far_from_station": {"description": "Attendance: You're over 50 KM away from the selected station {stationCode}. Attendance can only be marked when you're within the allowed range.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_train": "ट्रेन", "@attendance_train": {"description": "Attendance: Train", "context": "attendance"}, "attendance_train_date": "ट्रेन {trainNo} - {date}", "@attendance_train_date": {"description": "Attendance: Train {trainNo} - {date}", "context": "attendance", "placeholders": {"trainNo": {"type": "String"}, "date": {"type": "String"}}}, "attendance_train_depot": "ट्रेन डिपो:{depot}", "@attendance_train_depot": {"description": "Attendance: Train Depot:{depot}", "context": "attendance", "placeholders": {"depot": {"type": "String"}}}, "attendance_train_not_running": "ट्रेन Not Running", "@attendance_train_not_running": {"description": "Attendance: Train Not Running", "context": "attendance"}, "attendance_train_not_running_message": "ट्रेन {trainNumber} is NOT running on {dayOfWeek}", "@attendance_train_not_running_message": {"description": "Attendance: Train {trainNumber} is NOT running on {dayOfWeek}", "context": "attendance", "placeholders": {"trainNumber": {"type": "String"}, "dayOfWeek": {"type": "String"}}}, "attendance_update": "अपडेट करें", "@attendance_update": {"description": "Attendance: Update", "context": "attendance"}, "attendance_update_journey_status": "अपडेट करें यात्रा स्थिति", "@attendance_update_journey_status": {"description": "Attendance: Update Journey Status", "context": "attendance"}, "attendance_update_message": "A new संस्करण of the app is उपलब्ध. You must अपडेट करें to continue using the app.", "@attendance_update_message": {"description": "Attendance: A new version of the app is available. You must update to continue using the app.", "context": "attendance"}, "attendance_update_now": "अपडेट करें Now", "@attendance_update_now": {"description": "Attendance: Update Now", "context": "attendance"}, "attendance_update_required": "अपडेट करें आवश्यक", "@attendance_update_required": {"description": "Attendance: Update Required", "context": "attendance"}, "attendance_user_location": "उपयोगकर्ता स्थान:{locationStatus}", "@attendance_user_location": {"description": "Attendance: User Location:{locationStatus}", "context": "attendance", "placeholders": {"locationStatus": {"type": "String"}}}, "attendance_details_distance": "दूरी: {distance} km", "@attendance_details_distance": {"description": "Attendance: Distance: {distance} km", "context": "attendance", "placeholders": {"distance": {"type": "String"}}}, "attendance_details_error": "त्रुटि: {error}", "@attendance_details_error": {"description": "Attendance: Error: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_details_journey_date": "यात्रा तारीख:", "@attendance_details_journey_date": {"description": "Attendance: Journey Date:", "context": "attendance"}, "attendance_details_latitude": "latitude: {latitude}", "@attendance_details_latitude": {"description": "Attendance: latitude: {latitude}", "context": "attendance", "placeholders": {"latitude": {"type": "String"}}}, "attendance_details_longitude": "longitude: {longitude}", "@attendance_details_longitude": {"description": "Attendance: longitude: {longitude}", "context": "attendance", "placeholders": {"longitude": {"type": "String"}}}, "attendance_details_match_percentage": "Match प्रतिशत: {percentage}", "@attendance_details_match_percentage": {"description": "Attendance: Match Percentage: {percentage}", "context": "attendance", "placeholders": {"percentage": {"type": "String"}}}, "attendance_details_nearest_station": "nearest स्टेशन: {station}", "@attendance_details_nearest_station": {"description": "Attendance: nearest Station: {station}", "context": "attendance", "placeholders": {"station": {"type": "String"}}}, "attendance_details_no_attendance": "No उपस्थिति found.", "@attendance_details_no_attendance": {"description": "Attendance: No attendance found.", "context": "attendance"}, "attendance_details_no_data": "No डेटा उपलब्ध.", "@attendance_details_no_data": {"description": "Attendance: No data available.", "context": "attendance"}, "attendance_details_no_human_detected": "No human detected", "@attendance_details_no_human_detected": {"description": "Attendance: No human detected", "context": "attendance"}, "attendance_details_station_code": "स्टेशन Code:", "@attendance_details_station_code": {"description": "Attendance: Station Code:", "context": "attendance"}, "attendance_details_status": "स्थिति: {status}", "@attendance_details_status": {"description": "Attendance: Status: {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_details_status_marked": "चिह्नित", "@attendance_details_status_marked": {"description": "Attendance: Marked", "context": "attendance"}, "attendance_details_status_pending": "Pending", "@attendance_details_status_pending": {"description": "Attendance: Pending", "context": "attendance"}, "attendance_details_title": "उपस्थिति विवरण", "@attendance_details_title": {"description": "Attendance: Attendance Details", "context": "attendance"}, "attendance_details_train_number": "ट्रेन संख्या:", "@attendance_details_train_number": {"description": "Attendance: Train Number:", "context": "attendance"}, "attendance_details_updated": "All विवरण updated सफलतापूर्वक.", "@attendance_details_updated": {"description": "Attendance: All details updated successfully.", "context": "attendance"}, "attendance_details_updated_at": "Updated At: {updatedAt}", "@attendance_details_updated_at": {"description": "Attendance: Updated At: {updatedAt}", "context": "attendance", "placeholders": {"updatedAt": {"type": "String"}}}, "attendance_details_updated_by": "Updated By: {updatedBy}", "@attendance_details_updated_by": {"description": "Attendance: Updated By: {updatedBy}", "context": "attendance", "placeholders": {"updatedBy": {"type": "String"}}}, "attendance_details_username": "Username: {username}", "@attendance_details_username": {"description": "Attendance: Username: {username}", "context": "attendance", "placeholders": {"username": {"type": "String"}}}, "btn_no_attendance_found": "No उपस्थिति found.", "@btn_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_attendance_already_submitted": "उपस्थिति Already Submitted", "@text_attendance_already_submitted": {"description": "Attendance: Attendance Already Submitted", "context": "attendance"}, "text_attendance_marked_successfully": "उपस्थिति चिह्नित सफलतापूर्वक!", "@text_attendance_marked_successfully": {"description": "Attendance: Attendance marked successfully!", "context": "attendance"}, "text_no_attendance_found": "कोई उपस्थिति नहीं मिली।", "@text_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_other_ehkobhs": "अन्य EHK/OBHS", "@text_other_ehkobhs": {"description": "Text from text_widgets: Other EHK/OBHS", "context": "text_widgets"}, "text_chart_has_not": "इस स्टेशन के लिए चार्ट तैयार नहीं किया गया है", "@text_chart_has_not": {"description": "Text from text_widgets: Chart has not been prepared for this station", "context": "text_widgets"}, "text_camera": "कैमरा", "@text_camera": {"description": "Text from text_widgets: Camera", "context": "text_widgets"}, "text_gallery": "गैलरी", "@text_gallery": {"description": "Text from text_widgets: Gallery", "context": "text_widgets"}, "text_error_message": "त्रुटि: $message", "@text_error_message": {"description": "Text from text_widgets: Error: $message", "context": "text_widgets"}, "text_failed_to_get": "स्थान प्राप्त करने में विफल: $e", "@text_failed_to_get": {"description": "Text from text_widgets: Failed to get location: $e", "context": "text_widgets"}, "text_error_fetching_images": "छवियां प्राप्त करने में त्रुटि: $e", "@text_error_fetching_images": {"description": "Text from text_widgets: Error fetching images: $e", "context": "text_widgets"}, "text_back_to_all": "सभी उपयोगकर्ताओं पर वापस जाएं", "@text_back_to_all": {"description": "Text from text_widgets: Back to all users", "context": "text_widgets"}, "text_submit": "<PERSON><PERSON><PERSON> करें", "@text_submit": {"description": "Text from text_widgets: Submit", "context": "text_widgets"}, "text_compressing_image": "छवि संपीड़ित की जा रही है", "@text_compressing_image": {"description": "Text from text_widgets: Compressing image", "context": "text_widgets"}, "text_upload_entrykeysubstring0_6": "फ़ाइल अपलोड करें...", "@text_upload_entrykeysubstring0_6": {"description": "Text from text_widgets: Upload file...", "context": "text_widgets"}, "text_image_uploading": "छवि अपलोड हो रही है", "@text_image_uploading": {"description": "Text from text_widgets: Image Uploading", "context": "text_widgets"}, "text_error_snapshoterror": "त्रुटि हुई", "@text_error_snapshoterror": {"description": "Text from text_widgets: <PERSON><PERSON><PERSON> occurred", "context": "text_widgets"}, "text_latitude_entrylatitude": "अक्षांश: --", "@text_latitude_entrylatitude": {"description": "Text from text_widgets: Latitude: --", "context": "text_widgets"}, "text_longitude_entrylongitude": "देशांतर: --", "@text_longitude_entrylongitude": {"description": "Text from text_widgets: Longitude: --", "context": "text_widgets"}, "text_distance_entrydistance_km": "दूरी: -- किमी", "@text_distance_entrydistance_km": {"description": "Text from text_widgets: Distance: -- km", "context": "text_widgets"}, "text_updated_by_entryupdatedby": "द्वारा अपडेट किया गया: --", "@text_updated_by_entryupdatedby": {"description": "Text from text_widgets: Updated By: --", "context": "text_widgets"}, "text_no_data_available": "कोई डेटा उपलब्ध नहीं है।", "@text_no_data_available": {"description": "Text from text_widgets: No data available.", "context": "text_widgets"}, "text_show_more": "और दिखाएं", "@text_show_more": {"description": "Text from text_widgets: Show More", "context": "text_widgets"}, "text_show_less": "कम दिखाएं", "@text_show_less": {"description": "Text from text_widgets: Show Less", "context": "text_widgets"}, "text_could_not_open": "फ़ाइल नहीं खोली जा सकी", "@text_could_not_open": {"description": "Text from text_widgets: Could not open file", "context": "text_widgets"}, "text_download_started": "डाउनलोड शुरू हुआ!", "@text_download_started": {"description": "Text from text_widgets: Download Started!", "context": "text_widgets"}, "text_pdf_downloaded_successfully": "PDF सफलतापूर्वक डाउनलोड हुआ --", "@text_pdf_downloaded_successfully": {"description": "Text from text_widgets: PDF downloaded successfully to --", "context": "text_widgets"}, "text_download": "डाउनलोड", "@text_download": {"description": "Text from text_widgets: Download", "context": "text_widgets"}, "text_get_in_email": "ईमेल में प्राप्त करें", "@text_get_in_email": {"description": "Text from text_widgets: Get in Email", "context": "text_widgets"}, "text_could_not_launch": "लिंक लॉन्च नहीं हो सका", "@text_could_not_launch": {"description": "Text from text_widgets: Could not launch the link", "context": "text_widgets"}, "text_welcome_to_railops": "रेलऑप्स में आपका स्वागत है", "@text_welcome_to_railops": {"description": "Welcome message on login screen", "context": "login_screen"}, "text_sign_up_to_railops": "रेलऑप्स में साइन अप करें", "@text_sign_up_to_railops": {"description": "Sign up screen title", "context": "sign_up_screen"}, "text_login_using_fingerprint": "फिंगरप्रिंट का उपयोग करके लॉगिन करें", "@text_login_using_fingerprint": {"description": "Biometric authentication prompt", "context": "authentication"}, "text_logging_in_please_wait": "लॉगिन हो रहा है... कृपया प्रतीक्षा करें।", "@text_logging_in_please_wait": {"description": "Loading message during login", "context": "authentication"}, "text_log_in": "लॉग इन", "@text_log_in": {"description": "Login button text", "context": "authentication"}, "text_home": "होम", "@text_home": {"description": "Home navigation title", "context": "navigation"}, "text_home_screen": "होम स्क्रीन", "@text_home_screen": {"description": "Home screen title text", "context": "home_screen"}, "form_password": "पासवर्ड *", "@form_password": {"description": "Password field label", "context": "login_form"}, "error_enter_mobile_number": "कृपया अपना मोबाइल नंबर दर्ज करें", "@error_enter_mobile_number": {"description": "Mobile number validation error", "context": "login_form"}, "error_mobile_number_digits": "मोबाइल नंबर 10 अंकों का होना चाहिए", "@error_mobile_number_digits": {"description": "Mobile number length validation error", "context": "login_form"}, "error_enter_password": "कृपया अपना पासवर्ड दर्ज करें", "@error_enter_password": {"description": "Password validation error", "context": "login_form"}, "btn_log_in_with_mobile": "मोबाइल नंबर से लॉग इन करें", "@btn_log_in_with_mobile": {"description": "Mobile login button text", "context": "login_form"}, "btn_new_user_sign_up": "नए उपयोगकर्ता? यहाँ साइन अप करें", "@btn_new_user_sign_up": {"description": "Sign up button text", "context": "login_form"}, "text_privacy_policy": "गोपनीयता नीति", "@text_privacy_policy": {"description": "Privacy policy link text", "context": "login_form"}, "text_terms_conditions": "नियम और शर्तें", "@text_terms_conditions": {"description": "Terms and conditions link text", "context": "login_form"}, "text_know_about_app": "इस ऐप के बारे में जानें", "@text_know_about_app": {"description": "App information link text", "context": "login_form"}, "msg_login_successful": "लॉगिन सफल", "@msg_login_successful": {"description": "Login success message", "context": "authentication"}, "msg_invalid_pin": "अमान्य पिन", "@msg_invalid_pin": {"description": "Invalid PIN error message", "context": "authentication"}, "form_first_name_required": "पहला नाम *", "@form_first_name_required": {"description": "Required first name field label", "context": "signup_form"}, "form_first_name_hint": "अपना पहला नाम दर्ज करें", "@form_first_name_hint": {"description": "Hint text for first name field", "context": "signup_form"}, "form_middle_name_optional": "मध्य नाम (वैकल्पिक)", "@form_middle_name_optional": {"description": "Optional middle name field label", "context": "signup_form"}, "form_middle_name_hint": "अपना मध्य नाम दर्ज करें", "@form_middle_name_hint": {"description": "Hint text for middle name field", "context": "signup_form"}, "form_last_name_required": "अंतिम नाम *", "@form_last_name_required": {"description": "Required last name field label", "context": "signup_form"}, "form_last_name_hint": "अपना अंतिम नाम दर्ज करें", "@form_last_name_hint": {"description": "Hint text for last name field", "context": "signup_form"}, "form_whatsapp_number": "व्हाट्सऐप नंबर", "@form_whatsapp_number": {"description": "WhatsApp number field label", "context": "signup_form"}, "form_whatsapp_hint": "10 अंकों का व्हाट्सऐप नंबर दर्ज करें", "@form_whatsapp_hint": {"description": "Hint text for WhatsApp number field", "context": "signup_form"}, "form_secondary_phone_optional": "द्वितीयक फोन नंबर (वैकल्पिक)", "@form_secondary_phone_optional": {"description": "Optional secondary phone number field label", "context": "signup_form"}, "form_secondary_phone_hint": "10 अंकों का द्वितीयक फोन नंबर दर्ज करें", "@form_secondary_phone_hint": {"description": "Hint text for secondary phone number field", "context": "signup_form"}, "btn_no_email": "मेरे पास ईमेल नहीं है", "@btn_no_email": {"description": "Button text for users without email", "context": "signup_form"}, "btn_request_signup": "साइन अप के लिए अनुरोध", "@btn_request_signup": {"description": "Submit button text for signup form", "context": "signup_form"}, "btn_already_have_account": "पहले से खाता है? लॉगिन करें", "@btn_already_have_account": {"description": "Link text to login screen", "context": "signup_form"}, "text_whatsapp_same_as_phone": "व्हाट्सऐप नंबर फोन नंबर के समान है", "@text_whatsapp_same_as_phone": {"description": "Text when WhatsApp number matches phone number", "context": "signup_form"}, "text_use_same_whatsapp": "व्हाट्सऐप के लिए वही नंबर उपयोग करें?", "@text_use_same_whatsapp": {"description": "Question text for using same number for WhatsApp", "context": "signup_form"}, "text_verified": "सत्यापित", "@text_verified": {"description": "Text indicating field is verified", "context": "signup_form"}, "msg_submitting_data": "डेटा जमा कर रहे हैं। कृपया प्रतीक्षा करें!", "@msg_submitting_data": {"description": "Loading message during form submission", "context": "signup_form"}, "msg_success": "सफलता", "@msg_success": {"description": "Success message title", "context": "signup_form"}, "msg_error": "त्रुटि", "@msg_error": {"description": "Error message title", "context": "signup_form"}, "msg_form_incomplete": "फॉर्म अधूरा", "@msg_form_incomplete": {"description": "Form validation error title", "context": "signup_form"}, "msg_complete_required_fields": "कृपया सभी आवश्यक फ़ील्ड सही तरीके से भरें", "@msg_complete_required_fields": {"description": "Form validation error message", "context": "signup_form"}, "msg_information": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ी", "@msg_information": {"description": "Information dialog title", "context": "signup_form"}, "msg_complete_fields_order": "कृपया सभी फ़ील्ड क्रम में भरें। प्रत्येक फ़ील्ड केवल तभी सक्षम होगा जब पिछला फ़ील्ड सही तरीके से भरा और सत्यापित हो।", "@msg_complete_fields_order": {"description": "Information message about field completion order", "context": "signup_form"}, "error_enter_first_name": "कृपया अपना पहला नाम दर्ज करें", "@error_enter_first_name": {"description": "Validation error for empty first name", "context": "signup_form"}, "error_enter_last_name": "कृपया अपना अंतिम नाम दर्ज करें", "@error_enter_last_name": {"description": "Validation error for empty last name", "context": "signup_form"}, "error_enter_whatsapp": "कृपया व्हाट्सऐप नंबर दर्ज करें", "@error_enter_whatsapp": {"description": "Validation error for empty WhatsApp number", "context": "signup_form"}, "error_whatsapp_10_digits": "व्हाट्सऐप नंबर बिल्कुल 10 अंकों का होना चाहिए", "@error_whatsapp_10_digits": {"description": "Validation error for WhatsApp number length", "context": "signup_form"}, "error_enter_only_numbers": "कृपया केवल संख्याएं दर्ज करें", "@error_enter_only_numbers": {"description": "Validation error for non-numeric input", "context": "signup_form"}, "error_secondary_phone_10_digits": "द्वितीयक फोन नंबर बिल्कुल 10 अंकों का होना चाहिए", "@error_secondary_phone_10_digits": {"description": "Validation error for secondary phone number length", "context": "signup_form"}, "error_phone_numbers_different": "फोन नंबर और द्वितीयक फोन नंबर अलग होने चाहिए", "@error_phone_numbers_different": {"description": "Validation error when phone numbers are the same", "context": "signup_form"}, "error_request_error": "अनुरोध त्रुटि", "@error_request_error": {"description": "Error dialog title for signup request errors", "context": "signup_error"}, "error_phone_number": "फोन नंबर", "@error_phone_number": {"description": "Label for phone number in error dialog", "context": "signup_error"}, "error_whatsapp_number": "व्हाट्सऐप नंबर", "@error_whatsapp_number": {"description": "Label for WhatsApp number in error dialog", "context": "signup_error"}, "error_email_id": "ईमेल आईडी", "@error_email_id": {"description": "Label for email ID in error dialog", "context": "signup_error"}, "error_emp_number": "कर्मचारी संख्या", "@error_emp_number": {"description": "Label for employee number in error dialog", "context": "signup_error"}, "error_update_marked_info": "कृपया लाल क्रॉस से चिह्नित जानकारी को अपडेट करें और पुनः प्रयास करें।", "@error_update_marked_info": {"description": "Instruction text in error dialog", "context": "signup_error"}, "status_new": "नया", "@status_new": {"description": "Status text for new entries", "context": "signup_error"}, "status_already_taken": "पहले से लिया गया", "@status_already_taken": {"description": "Status text for taken entries", "context": "signup_error"}, "status_already_requested": "पहले से अनुरोधित", "@status_already_requested": {"description": "Status text for requested entries", "context": "signup_error"}, "status_unknown": "अज्ञात", "@status_unknown": {"description": "Status text for unknown entries", "context": "signup_error"}, "screen_title_train_rake_deficiency_report": "ट्रेन रेक कमी रिपोर्ट", "@screen_title_train_rake_deficiency_report": {"description": "AppBar title for OBHS to MCC handover screen", "context": "obhs_to_mcc_handover_screen"}, "text_obhs_to_mcc_handover": "OBHS से MCC हैंडओवर", "@text_obhs_to_mcc_handover": {"description": "Main heading for OBHS to MCC handover screen", "context": "obhs_to_mcc_handover_screen"}, "error_failed_to_load_issues": "समस्याएं लोड करने में विफल: {error}", "@error_failed_to_load_issues": {"description": "Error message when loading issues fails", "context": "obhs_to_mcc_handover_screen", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "error_invalid_response_format": "सर्वर से अमान्य प्रतिक्रिया प्रारूप", "@error_invalid_response_format": {"description": "Error message for invalid server response", "context": "obhs_to_mcc_handover_screen"}, "error_failed_to_load_trip_report": "ट्रिप रिपोर्ट लोड करने में विफल: {error}", "@error_failed_to_load_trip_report": {"description": "Error message when loading trip report fails", "context": "obhs_to_mcc_handover_screen", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "msg_data_refreshed_successfully": "डेटा सफलतापूर्वक रीफ्रेश किया गया", "@msg_data_refreshed_successfully": {"description": "Success message for data refresh", "context": "obhs_to_mcc_handover_screen"}, "error_refresh_failed": "रीफ्रेश विफल: {error}", "@error_refresh_failed": {"description": "Error message when refresh fails", "context": "obhs_to_mcc_handover_screen", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_no_coaches_found": "कोई कोच नहीं मिला।", "@text_no_coaches_found": {"description": "Message when no coaches are available", "context": "obhs_to_mcc_handover_screen"}, "text_please_select_train_and_date": "कृपया ट्रेन और तारीख चुनें।", "@text_please_select_train_and_date": {"description": "Instruction to select train and date", "context": "obhs_to_mcc_handover_screen"}, "text_coach": "कोच {coach<PERSON><PERSON>ber}", "@text_coach": {"description": "Coach label with number", "context": "obhs_to_mcc_handover_screen", "placeholders": {"coachNumber": {"type": "String", "description": "Coach number"}}}, "text_no_issues_reported": "कोई समस्या रिपोर्ट नहीं की गई", "@text_no_issues_reported": {"description": "Message when no issues are reported for a coach", "context": "obhs_to_mcc_handover_screen"}, "text_reported_issues": "रिपोर्ट की गई समस्याएं:", "@text_reported_issues": {"description": "Label for reported issues section", "context": "obhs_to_mcc_handover_screen"}, "text_unknown_issue": "अज्ञात समस्या", "@text_unknown_issue": {"description": "Fallback text for unknown issues", "context": "obhs_to_mcc_handover_screen"}, "tooltip_upload_coach_image": "कोच छवि अपलोड करें", "@tooltip_upload_coach_image": {"description": "Tooltip for upload coach image button", "context": "obhs_to_mcc_handover_screen"}, "btn_manage_issues": "समस्याओं का प्रबंधन करें", "@btn_manage_issues": {"description": "<PERSON><PERSON> text for managing issues", "context": "obhs_to_mcc_handover_screen"}, "error_please_select_train_and_date_first": "कृपया पहले ट्रेन और तारीख चुनें", "@error_please_select_train_and_date_first": {"description": "Error message when train and date are not selected", "context": "obhs_to_mcc_handover_screen"}, "screen_title_mcc_to_obhs_handover_report": "MCC से OBHS हैंडओवर रिपोर्ट", "@screen_title_mcc_to_obhs_handover_report": {"description": "AppBar title for MCC to OBHS handover screen", "context": "mcc_to_obhs_handover_screen"}, "text_coach_handover_report": "कोच हैंडओवर रिपोर्ट", "@text_coach_handover_report": {"description": "Main heading for MCC to OBHS handover screen", "context": "mcc_to_obhs_handover_screen"}, "error_failed_to_load_handover_items": "हैंडओवर आइटम लोड करने में विफल: {error}", "@error_failed_to_load_handover_items": {"description": "Error message when loading handover items fails", "context": "mcc_to_obhs_handover_screen", "placeholders": {"error": {"type": "String", "description": "Error details"}}}}