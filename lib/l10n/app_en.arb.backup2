{"@@locale": "en", "text_reenter_otp": "Re-enter OTP", "@text_reenter_otp": {"description": "Text from text_widgets: Re-enter OTP", "context": "text_widgets"}, "text_deny": "<PERSON><PERSON>", "@text_deny": {"description": "Text from text_widgets: <PERSON>y", "context": "text_widgets"}, "text_enable": "Enable", "@text_enable": {"description": "Text from text_widgets: Enable", "context": "text_widgets"}, "text_location_access_required": "Location Access Required", "@text_location_access_required": {"description": "Text from text_widgets: Location Access Required", "context": "text_widgets"}, "text_decline": "Decline", "@text_decline": {"description": "Text from text_widgets: Decline", "context": "text_widgets"}, "text_accept": "Accept", "@text_accept": {"description": "Text from text_widgets: Accept", "context": "text_widgets"}, "text_confirm_delete": "Confirm Delete", "@text_confirm_delete": {"description": "Title for delete confirmation dialog", "context": "issue_screen"}, "text_cancel": "Cancel", "@text_cancel": {"description": "Cancel button text", "context": "edit_profile_form"}, "text_delete": "Delete", "@text_delete": {"description": "Text from text_widgets: Delete", "context": "text_widgets"}, "text_storage_permission_is": "Storage permission is required to download files", "@text_storage_permission_is": {"description": "Text from text_widgets: Storage permission is required to download files", "context": "text_widgets"}, "text_please_select_a": "Please select a train first", "@text_please_select_a": {"description": "Text from text_widgets: Please select a train first", "context": "text_widgets"}, "text_update_required": "Update Required", "@text_update_required": {"description": "Text from text_widgets: Update Required", "context": "text_widgets"}, "text_update_now": "Update Now", "@text_update_now": {"description": "Text from text_widgets: Update Now", "context": "text_widgets"}, "text_please_select_a_1": "Please select a train number and date.", "@text_please_select_a_1": {"description": "Text from text_widgets: Please select a train number and date.", "context": "text_widgets"}, "text_all_details_updated": "All details updated successfully.", "@text_all_details_updated": {"description": "Text from text_widgets: All details updated successfully.", "context": "text_widgets"}, "text_failed_to_update": "Failed to update details: $e", "@text_failed_to_update": {"description": "Text from text_widgets: Failed to update details: $e", "context": "text_widgets"}, "text_data_refreshed_successfully": "Data refreshed successfully", "@text_data_refreshed_successfully": {"description": "Success message for data refresh", "context": "edit_train_screen"}, "text_train_location_saved": "Train Location Saved Successfully", "@text_train_location_saved": {"description": "Text from text_widgets: Train Location Saved Successfully", "context": "text_widgets"}, "text_self": "Self", "@text_self": {"description": "Text from text_widgets: Self", "context": "text_widgets"}, "text_other_ca": "Other CA", "@text_other_ca": {"description": "Text from text_widgets: Other CA", "context": "text_widgets"}, "text_other_ehkobhs": "Other EHK/OBHS", "@text_other_ehkobhs": {"description": "Text from text_widgets: Other EHK/OBHS", "context": "text_widgets"}, "text_chart_has_not": "Chart has not been prepared for this station", "@text_chart_has_not": {"description": "Text from text_widgets: Chart has not been prepared for this station", "context": "text_widgets"}, "text_camera": "Camera", "@text_camera": {"description": "Text for camera option", "context": "rail_sathi_write_complaint"}, "text_gallery": "Gallery", "@text_gallery": {"description": "Text for gallery option", "context": "rail_sathi_write_complaint"}, "text_getting_location": "Getting location...", "@text_getting_location": {"description": "Text from text_widgets: Getting location...", "context": "text_widgets"}, "text_attendance_marked_successfully": "Attendance marked successfully!", "@text_attendance_marked_successfully": {"description": "Text from text_widgets: Attendance marked successfully!", "context": "text_widgets"}, "text_error_message": "Error: $message", "@text_error_message": {"description": "Text from text_widgets: Error: $message", "context": "text_widgets"}, "text_failed_to_get": "Failed to get location: $e", "@text_failed_to_get": {"description": "Text from text_widgets: Failed to get location: $e", "context": "text_widgets"}, "text_error_fetching_images": "Error fetching images: $e", "@text_error_fetching_images": {"description": "Text from text_widgets: Error fetching images: $e", "context": "text_widgets"}, "text_attendance_already_submitted": "Attendance Already Submitted", "@text_attendance_already_submitted": {"description": "Text from text_widgets: Attendance Already Submitted", "context": "text_widgets"}, "text_back_to_all": "Back to all users", "@text_back_to_all": {"description": "Text from text_widgets: Back to all users", "context": "text_widgets"}, "text_submit": "Submit", "@text_submit": {"description": "Submit button text", "context": "edit_train_screen"}, "text_upload_status": "Upload Status", "@text_upload_status": {"description": "Text from text_widgets: Upload Status", "context": "text_widgets"}, "text_compressing_image": "Compressing image...", "@text_compressing_image": {"description": "Status message during image compression", "context": "image_upload"}, "text_upload_entrykeysubstring0_6": "Upload file...", "@text_upload_entrykeysubstring0_6": {"description": "Text from text_widgets: Upload file...", "context": "text_widgets"}, "text_close": "Close", "@text_close": {"description": "Close button text", "context": "profile_forms"}, "text_image_uploading": "Image Uploading", "@text_image_uploading": {"description": "Text from text_widgets: Image Uploading", "context": "text_widgets"}, "text_no_attendance_found": "No attendance found.", "@text_no_attendance_found": {"description": "Text from text_widgets: No attendance found.", "context": "text_widgets"}, "text_error_snapshoterror": "Error occurred", "@text_error_snapshoterror": {"description": "Text from text_widgets: <PERSON><PERSON><PERSON> occurred", "context": "text_widgets"}, "text_latitude_entrylatitude": "Latitude: --", "@text_latitude_entrylatitude": {"description": "Text from text_widgets: Latitude: --", "context": "text_widgets"}, "text_longitude_entrylongitude": "Longitude: --", "@text_longitude_entrylongitude": {"description": "Text from text_widgets: Longitude: --", "context": "text_widgets"}, "text_distance_entrydistance_km": "Distance: -- km", "@text_distance_entrydistance_km": {"description": "Text from text_widgets: Distance: -- km", "context": "text_widgets"}, "text_updated_by_entryupdatedby": "Updated By: --", "@text_updated_by_entryupdatedby": {"description": "Text from text_widgets: Updated By: --", "context": "text_widgets"}, "text_no_data_available": "No data available", "@text_no_data_available": {"description": "Message when no data is available", "context": "train_details_screen"}, "text_show_more": "Show More", "@text_show_more": {"description": "Text from text_widgets: Show More", "context": "text_widgets"}, "text_show_less": "Show Less", "@text_show_less": {"description": "Text from text_widgets: Show Less", "context": "text_widgets"}, "text_could_not_open": "Could not open file", "@text_could_not_open": {"description": "Text from text_widgets: Could not open file", "context": "text_widgets"}, "text_download_started": "Download Started!", "@text_download_started": {"description": "Text from text_widgets: Download Started!", "context": "text_widgets"}, "text_pdf_downloaded_successfully": "PDF downloaded successfully to --", "@text_pdf_downloaded_successfully": {"description": "Text from text_widgets: PDF downloaded successfully to --", "context": "text_widgets"}, "text_download": "Download", "@text_download": {"description": "Text from text_widgets: Download", "context": "text_widgets"}, "text_get_in_email": "Get in Email", "@text_get_in_email": {"description": "Text from text_widgets: Get in Email", "context": "text_widgets"}, "text_could_not_launch": "Could not launch the link", "@text_could_not_launch": {"description": "Text from text_widgets: Could not launch the link", "context": "text_widgets"}, "text_permission_denied": "Permission Denied", "@text_permission_denied": {"description": "Dialog title for permission denied", "context": "enable_disable_user"}, "text_requested_users": "Requested Users", "@text_requested_users": {"description": "Text from text_widgets: Requested Users", "context": "text_widgets"}, "text_end_date_cannot": "End date cannot be before start date", "@text_end_date_cannot": {"description": "Text from text_widgets: End date cannot be before start date", "context": "text_widgets"}, "text_please_select_both": "Please select both suspension dates", "@text_please_select_both": {"description": "Text from text_widgets: Please select both suspension dates", "context": "text_widgets"}, "text_update_user_details": "Update User Details", "@text_update_user_details": {"description": "Text from text_widgets: Update User Details", "context": "text_widgets"}, "text_request_for_update": "Request For Update User Details", "@text_request_for_update": {"description": "Text from text_widgets: Request For Update User Details", "context": "text_widgets"}, "text_i_dont_have": "I don't have an email", "@text_i_dont_have": {"description": "Text from text_widgets: I don't have an email", "context": "text_widgets"}, "text_information": "Information", "@text_information": {"description": "Text from text_widgets: Information", "context": "text_widgets"}, "text_request_for_sign": "Request For Sign Up", "@text_request_for_sign": {"description": "Text from text_widgets: Request For Sign Up", "context": "text_widgets"}, "text_forgot_password": "Forgot Password", "@text_forgot_password": {"description": "Text from text_widgets: Forgot Password", "context": "text_widgets"}, "text_forgotten_password": "Forgotten Password", "@text_forgotten_password": {"description": "Forgotten password screen title", "context": "forgot_password_screen"}, "text_mobile_otp_login": "Mobile OTP Login", "@text_mobile_otp_login": {"description": "Mobile OTP screen title", "context": "mobile_otp_screen"}, "text_enter_otp": "Enter OTP", "@text_enter_otp": {"description": "OTP input field label", "context": "rm_feedback_screen"}, "text_otp_sent_to_mobile": "Please enter the OTP sent to your mobile number: {mobileNumber}", "@text_otp_sent_to_mobile": {"description": "OTP instruction with mobile number", "context": "mobile_otp_screen", "placeholders": {"mobileNumber": {"type": "String", "description": "The mobile number where OTP was sent"}}}, "text_profile": "Profile", "@text_profile": {"description": "Profile menu item", "context": "custom_app_bar"}, "text_error": "Error", "@text_error": {"description": "Error text label", "context": "review_feedback"}, "text_send_otp": "Send OTP", "@text_send_otp": {"description": "Send OTP button text", "context": "change_email_modal"}, "text_verify_otp": "Verify OTP", "@text_verify_otp": {"description": "Verify OTP button text", "context": "profile_forms"}, "text_sign_in_with": "Sign in with Google", "@text_sign_in_with": {"description": "Text from text_widgets: Sign in with Google", "context": "text_widgets"}, "text_new_user_sign": "New User? Sign Up Here", "@text_new_user_sign": {"description": "Text from text_widgets: New User? Sign Up Here", "context": "text_widgets"}, "text_resend_otp": "Resend OTP", "@text_resend_otp": {"description": "Resend OTP button text", "context": "profile_forms"}, "text_please_enter_a": "Please enter a valid 10-digit PNR number", "@text_please_enter_a": {"description": "Text from text_widgets: Please enter a valid 10-digit PNR number", "context": "text_widgets"}, "text_failed_to_fetch": "Failed to fetch PNR data. Please try again.", "@text_failed_to_fetch": {"description": "Text from text_widgets: Failed to fetch PNR data. Please try again.", "context": "text_widgets"}, "text_check_pnr_status": "Check PNR Status", "@text_check_pnr_status": {"description": "Header text for PNR status check", "context": "pnr_status"}, "text_pnr_number": "PNR Number", "@text_pnr_number": {"description": "Text from text_widgets: PNR Number", "context": "text_widgets"}, "text_no_pnr_data": "No PNR Data Found", "@text_no_pnr_data": {"description": "Text from text_widgets: No PNR Data Found", "context": "text_widgets"}, "text_please_turn_on": "Please turn on location services", "@text_please_turn_on": {"description": "Text from text_widgets: Please turn on location services", "context": "text_widgets"}, "text_an_error_occurred": "An error occurred: $e", "@text_an_error_occurred": {"description": "Text from text_widgets: An error occurred: $e", "context": "text_widgets"}, "text_your_current_location": "Your current location", "@text_your_current_location": {"description": "Text from text_widgets: Your current location", "context": "text_widgets"}, "text_location_services_disabled": "Location Services Disabled", "@text_location_services_disabled": {"description": "Alert dialog title when location services are disabled", "context": "train_details_screen"}, "text_please_enable_location": "Please enable location services to proceed.", "@text_please_enable_location": {"description": "Text from text_widgets: Please enable location services to proceed.", "context": "text_widgets"}, "text_location_permission_denied": "Location Permission Denied", "@text_location_permission_denied": {"description": "Alert dialog title when location permission is denied", "context": "train_details_screen"}, "text_open_settings": "Open Settings", "@text_open_settings": {"description": "Button text to open app settings", "context": "train_details_screen"}, "text_location_permission_denied_1": "Location Permission Denied Forever", "@text_location_permission_denied_1": {"description": "Text from text_widgets: Location Permission Denied Forever", "context": "text_widgets"}, "text_refresh_failed_e": "Refresh failed: --", "@text_refresh_failed_e": {"description": "Text from text_widgets: Refresh failed: --", "context": "text_widgets"}, "text_no_location_data": "No location data available.", "@text_no_location_data": {"description": "Text from text_widgets: No location data available.", "context": "text_widgets"}, "text_no_data_available_1": "No data available", "@text_no_data_available_1": {"description": "Text from text_widgets: No data available", "context": "text_widgets"}, "text_no_train_details": "No train details available.", "@text_no_train_details": {"description": "Text from text_widgets: No train details available.", "context": "text_widgets"}, "text_recent_train_details": "Recent Train Details", "@text_recent_train_details": {"description": "Header for recent train details table", "context": "profile_screen"}, "attendance_train_not_running_details": "Train {trainNumber} is NOT running on {dayOfWeek}\nRunning Days: {runningDays}", "@attendance_train_not_running_details": {"description": "Train not running message with details", "context": "attendance_screen", "placeholders": {"trainNumber": {"type": "String"}, "dayOfWeek": {"type": "String"}, "runningDays": {"type": "String"}}}, "attendance_data_refreshed": "Data refreshed successfully", "@attendance_data_refreshed": {"description": "Success message when data is refreshed", "context": "attendance_screen"}, "attendance_refresh_failed": "Refresh failed: {error}", "@attendance_refresh_failed": {"description": "Error message when refresh fails", "context": "attendance_screen", "placeholders": {"error": {"type": "String"}}}, "attendance_nearby_station_alert": "🛤️ Nearby Station Alert", "@attendance_nearby_station_alert": {"description": "Title for nearby station alert dialog", "context": "attendance_screen"}, "attendance_near_stations": "You're near the following station(s):", "@attendance_near_stations": {"description": "Message about nearby stations", "context": "attendance_screen"}, "attendance_assigned_coaches": "🚆 Assigned Coaches:", "@attendance_assigned_coaches": {"description": "Label for assigned coaches section", "context": "attendance_screen"}, "attendance_coaches_list": "Coaches: {coaches}", "@attendance_coaches_list": {"description": "List of assigned coaches", "context": "attendance_screen", "placeholders": {"coaches": {"type": "String"}}}, "text_none": "None", "@text_none": {"description": "Text from text_widgets: None", "context": "text_widgets"}, "text_download_pdf_for": "Download PDF for all stations", "@text_download_pdf_for": {"description": "Text from text_widgets: Download PDF for all stations", "context": "text_widgets"}, "text_mail_pdf_for": "Mail PDF for all stations", "@text_mail_pdf_for": {"description": "Text from text_widgets: Mail PDF for all stations", "context": "text_widgets"}, "text_add_configuration": "Add Configuration", "@text_add_configuration": {"description": "Text from text_widgets: Add Configuration", "context": "text_widgets"}, "text_select_charting_day": "Select Charting Day", "@text_select_charting_day": {"description": "Text from text_widgets: Select Charting Day", "context": "text_widgets"}, "text_submitting": "Submitting...", "@text_submitting": {"description": "Loading text when submitting data", "context": "edit_train_screen"}, "text_return_gap_updated": "Return gap updated successfully", "@text_return_gap_updated": {"description": "Text from text_widgets: Return gap updated successfully", "context": "text_widgets"}, "text_data_not_refreshed": "Data not refreshed: {error}", "@text_data_not_refreshed": {"description": "Error message when data refresh fails", "context": "edit_train_screen", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "text_add": "Add", "@text_add": {"description": "Add button text", "context": "edit_train_screen"}, "text_train_details_updated": "Train details updated successfully", "@text_train_details_updated": {"description": "Success message for train details update", "context": "edit_train_screen"}, "text_add_train_details": "Add Train Details", "@text_add_train_details": {"description": "App bar title for add train screen", "context": "add_train_screen"}, "text_add_train": "Add Train", "@text_add_train": {"description": "App bar title for add train profile screen", "context": "add_train_profile_screen"}, "text_stoppages_in_sequence": "Stoppages in Sequence", "@text_stoppages_in_sequence": {"description": "Section title for stoppages input", "context": "add_train_screen"}, "text_submitting_train_details": "Submitting train details...", "@text_submitting_train_details": {"description": "Loading message for train submission", "context": "add_train_screen"}, "text_edit_configuration": "Edit Configuration", "@text_edit_configuration": {"description": "Edit configuration section title", "context": "edit_train_screen"}, "text_no_coaches_available": "No coaches available", "@text_no_coaches_available": {"description": "Text from text_widgets: No coaches available", "context": "text_widgets"}, "text_invalid_response_format": "Invalid response format from server", "@text_invalid_response_format": {"description": "Text from text_widgets: Invalid response format from server", "context": "text_widgets"}, "text_please_select_a_2": "Please select a train and date first", "@text_please_select_a_2": {"description": "Text from text_widgets: Please select a train and date first", "context": "text_widgets"}, "text_coach_handover_report": "Coach Handover Report", "@text_coach_handover_report": {"description": "Main heading for MCC to OBHS handover screen", "context": "mcc_to_obhs_handover_screen"}, "text_save_selection": "Save Selection", "@text_save_selection": {"description": "Text from text_widgets: Save Selection", "context": "text_widgets"}, "text_select_media_type": "Select Media Type", "@text_select_media_type": {"description": "Dialog title for media type selection", "context": "CoachIssueImageUpload"}, "text_image": "Image", "@text_image": {"description": "Text from text_widgets: Image", "context": "text_widgets"}, "text_video": "Video", "@text_video": {"description": "Text from text_widgets: Video", "context": "text_widgets"}, "text_please_select_images": "Please select images to upload", "@text_please_select_images": {"description": "Text from text_widgets: Please select images to upload", "context": "text_widgets"}, "text_please_select_at": "Please select at least one issue", "@text_please_select_at": {"description": "Text from text_widgets: Please select at least one issue", "context": "text_widgets"}, "text_failed_to_upload": "Failed to upload images", "@text_failed_to_upload": {"description": "Text from text_widgets: Failed to upload images", "context": "text_widgets"}, "text_error_updating_issue": "Error updating issue: $e", "@text_error_updating_issue": {"description": "Text from text_widgets: Error updating issue: $e", "context": "text_widgets"}, "text_statustype_by": "$statusType By", "@text_statustype_by": {"description": "Text from text_widgets: $statusType By", "context": "text_widgets"}, "text_no_images_available": "No images available", "@text_no_images_available": {"description": "Text from text_widgets: No images available", "context": "text_widgets"}, "text_issuesubissue": "Issue/Subissue", "@text_issuesubissue": {"description": "Text from text_widgets: Issue/Subissue", "context": "text_widgets"}, "text_status": "Status", "@text_status": {"description": "Label for status", "context": "review_feedback"}, "text_submitupdate": "Submit/Update", "@text_submitupdate": {"description": "Text from text_widgets: Submit/Update", "context": "text_widgets"}, "text_reportedby": "Reported_by", "@text_reportedby": {"description": "Text from text_widgets: Reported_by", "context": "text_widgets"}, "text_fixedby": "Fixed_by", "@text_fixedby": {"description": "Text from text_widgets: Fixed_by", "context": "text_widgets"}, "text_resolvedby": "Resolved_by", "@text_resolvedby": {"description": "Text from text_widgets: Resolved_by", "context": "text_widgets"}, "text_pick_images": "Pick Images", "@text_pick_images": {"description": "Text from text_widgets: Pick Images", "context": "text_widgets"}, "text_uploading": "Uploading...", "@text_uploading": {"description": "Text from text_widgets: Uploading...", "context": "text_widgets"}, "text_submit_upload": "Submit & Upload", "@text_submit_upload": {"description": "Text from text_widgets: Submit & Upload", "context": "text_widgets"}, "text_retry": "Retry", "@text_retry": {"description": "Text from text_widgets: Retry", "context": "text_widgets"}, "text_approve": "Approve", "@text_approve": {"description": "Text from text_widgets: Approve", "context": "text_widgets"}, "text_no_requests_selected": "No requests selected", "@text_no_requests_selected": {"description": "Text from text_widgets: No requests selected", "context": "text_widgets"}, "text_are_you_sure": "Are you sure you want to approve these users:", "@text_are_you_sure": {"description": "Text from text_widgets: Are you sure you want to approve these users:", "context": "text_widgets"}, "text_requeststoprocesslength_users_approved": "Users approved successfully", "@text_requeststoprocesslength_users_approved": {"description": "Text from text_widgets: Users approved successfully", "context": "text_widgets"}, "text_confirm_denial": "Confirm Denial", "@text_confirm_denial": {"description": "Text from text_widgets: Confirm Denial", "context": "text_widgets"}, "text_deny_request": "<PERSON>y <PERSON>", "@text_deny_request": {"description": "Text from text_widgets: <PERSON><PERSON>", "context": "text_widgets"}, "text_approve_selected": "Approve Selected", "@text_approve_selected": {"description": "Text from text_widgets: Approve Selected", "context": "text_widgets"}, "text_approve_all": "Approve All", "@text_approve_all": {"description": "Text from text_widgets: Approve All", "context": "text_widgets"}, "text_processing_requests": "Processing requests...", "@text_processing_requests": {"description": "Text from text_widgets: Processing requests...", "context": "text_widgets"}, "text_clear_search": "Clear search", "@text_clear_search": {"description": "Text from text_widgets: Clear search", "context": "text_widgets"}, "text_failed_to_fetch_1": "Failed to fetch complaints", "@text_failed_to_fetch_1": {"description": "Text from text_widgets: Failed to fetch complaints", "context": "text_widgets"}, "text_error_fetching_trains": "Error fetching trains: $e", "@text_error_fetching_trains": {"description": "Text from text_widgets: Error fetching trains: $e", "context": "text_widgets"}, "text_traintrainno_traintrainname": "Train Details", "@text_traintrainno_traintrainname": {"description": "Text from text_widgets: Train Details", "context": "text_widgets"}, "text_other": "Other", "@text_other": {"description": "Option text for other selection", "context": "rail_sathi_write_complaint"}, "text_existing_images": "Existing Images:", "@text_existing_images": {"description": "Label for existing images section", "context": "view_complaints"}, "text_delete_image": "Delete Image", "@text_delete_image": {"description": "Title for delete image dialog", "context": "rail_sathi_view_complaints"}, "text_image_deleted": "Image deleted", "@text_image_deleted": {"description": "Message when image is deleted", "context": "rail_sathi_view_complaints"}, "text_newly_selected_images": "Newly Selected Images:", "@text_newly_selected_images": {"description": "Label for newly selected images section", "context": "view_complaints"}, "text_add_image": "Add Image", "@text_add_image": {"description": "Text from text_widgets: Add Image", "context": "text_widgets"}, "text_complaint_updated": "<PERSON><PERSON><PERSON><PERSON> updated", "@text_complaint_updated": {"description": "Text from text_widgets: Complaint updated", "context": "text_widgets"}, "text_update_failed": "Update failed", "@text_update_failed": {"description": "Text from text_widgets: Update failed", "context": "text_widgets"}, "text_save_changes": "Save Changes", "@text_save_changes": {"description": "Text from text_widgets: Save Changes", "context": "text_widgets"}, "text_delete_complaint": "Delete Co<PERSON>t", "@text_delete_complaint": {"description": "Text from text_widgets: Delete Complaint", "context": "text_widgets"}, "text_are_you_sure_1": "Are you sure you want to delete this complaint?", "@text_are_you_sure_1": {"description": "Text from text_widgets: Are you sure you want to delete this complaint?", "context": "text_widgets"}, "text_complaint_deleted_successfully": "<PERSON><PERSON><PERSON><PERSON> deleted successfully", "@text_complaint_deleted_successfully": {"description": "Text from text_widgets: Complaint deleted successfully", "context": "text_widgets"}, "text_failed_to_delete": "Failed to delete complaint", "@text_failed_to_delete": {"description": "Text from text_widgets: Failed to delete complaint", "context": "text_widgets"}, "text_select_date": "Select Date", "@text_select_date": {"description": "Date picker label", "context": "add_train_form"}, "text_no_complaints_found": "No complaints found", "@text_no_complaints_found": {"description": "Message when no complaints are found", "context": "view_complaints"}, "text_train_no_complainttrainnumber": "Train No: --", "@text_train_no_complainttrainnumber": {"description": "Text from text_widgets: Train No: --", "context": "text_widgets"}, "text_date_complaintcomplaindate": "Date: --", "@text_date_complaintcomplaindate": {"description": "Text from text_widgets: Date: --", "context": "text_widgets"}, "text_pnr_complaintpnrnumber": "PNR: --", "@text_pnr_complaintpnrnumber": {"description": "Text from text_widgets: PNR: --", "context": "text_widgets"}, "text_edit": "Edit", "@text_edit": {"description": "Edit button text", "context": "edit_train_screen"}, "text_success": "Success", "@text_success": {"description": "Success dialog title", "context": "change_mobile_form"}, "text_complaint_submitted_successfully": "<PERSON><PERSON><PERSON><PERSON> submitted successfully!", "@text_complaint_submitted_successfully": {"description": "Text from text_widgets: Complaint submitted successfully!", "context": "text_widgets"}, "text_failed_to_submit": "Failed to submit complaint", "@text_failed_to_submit": {"description": "Text from text_widgets: Failed to submit complaint", "context": "text_widgets"}, "text_error_e": "Error: $e", "@text_error_e": {"description": "Text from text_widgets: Error: $e", "context": "text_widgets"}, "text_ehk_ehkdisplay": "EHK: $ehkDisplay", "@text_ehk_ehkdisplay": {"description": "Text from text_widgets: EHK: $ehkDisplay", "context": "text_widgets"}, "text_pending": "Pending", "@text_pending": {"description": "Text from text_widgets: Pending", "context": "text_widgets"}, "text_completed": "Completed", "@text_completed": {"description": "Text from text_widgets: Completed", "context": "text_widgets"}, "text_upload_imagevideo": "Upload Image/Video", "@text_upload_imagevideo": {"description": "Text from text_widgets: Upload Image/Video", "context": "text_widgets"}, "text_submit_issue": "Submit Issue", "@text_submit_issue": {"description": "Text from text_widgets: Submit Issue", "context": "text_widgets"}, "text_validate": "Validate", "@text_validate": {"description": "Text from text_widgets: Validate", "context": "text_widgets"}, "text_next": "Next", "@text_next": {"description": "Text from text_widgets: Next", "context": "text_widgets"}, "text_error_loading_authentication": "Error loading authentication state", "@text_error_loading_authentication": {"description": "Text from text_widgets: Error loading authentication state", "context": "text_widgets"}, "text_storage_permission_required": "Storage Permission Required", "@text_storage_permission_required": {"description": "Text from text_widgets: Storage Permission Required", "context": "text_widgets"}, "text_please_select_a_3": "Please select a JSON file to submit.", "@text_please_select_a_3": {"description": "Text from text_widgets: Please select a JSON file to submit.", "context": "text_widgets"}, "text_error_json_file": "Error: Json file is not in the correct format", "@text_error_json_file": {"description": "Text from text_widgets: Error: Json file is not in the correct format", "context": "text_widgets"}, "text_selection_cleared": "Selection cleared.", "@text_selection_cleared": {"description": "Text from text_widgets: Selection cleared.", "context": "text_widgets"}, "text_upload_json_data": "Upload Json Data", "@text_upload_json_data": {"description": "Text from text_widgets: Upload Json Data", "context": "text_widgets"}, "text_update": "Update", "@text_update": {"description": "Text from text_widgets: Update", "context": "text_widgets"}, "text_add_issue": "Add Issue", "@text_add_issue": {"description": "Option to add issue in dialog", "context": "issue_screen"}, "text_no_subissues": "No subissues", "@text_no_subissues": {"description": "Text when issue has no subissues", "context": "issue_screen"}, "text_select_issue": "Select Issue", "@text_select_issue": {"description": "Dropdown hint for issue selection", "context": "issue_screen"}, "text_add_subissue": "Add Subissue", "@text_add_subissue": {"description": "Option to add subissue in dialog", "context": "issue_screen"}, "text_add_new_item": "Add New Item", "@text_add_new_item": {"description": "Title for add new item dialog", "context": "issue_screen"}, "text_images_or_videos": "Images or Videos upload initiated successfully", "@text_images_or_videos": {"description": "Text from text_widgets: Images or Videos upload initiated successfully", "context": "text_widgets"}, "text_please_select_an": "Please select an image and issue to upload", "@text_please_select_an": {"description": "Text from text_widgets: Please select an image and issue to upload", "context": "text_widgets"}, "text_issues_saved_upload": "Issues saved upload Images/Videos", "@text_issues_saved_upload": {"description": "Text from text_widgets: Issues saved upload Images/Videos", "context": "text_widgets"}, "text_select_issues_for": "Select Issues for Coach $coach", "@text_select_issues_for": {"description": "Text from text_widgets: Select Issues for Coach $coach", "context": "text_widgets"}, "text_pick_imagesvideos": "Pick Images/Videos", "@text_pick_imagesvideos": {"description": "Text from text_widgets: Pick Images/Videos", "context": "text_widgets"}, "text_please_select_imagevideo": "Please select image/video to upload", "@text_please_select_imagevideo": {"description": "Text from text_widgets: Please select image/video to upload", "context": "text_widgets"}, "text_confirm_deletion": "Confirm Deletion", "@text_confirm_deletion": {"description": "Text from text_widgets: Confirm Deletion", "context": "text_widgets"}, "text_delete_report": "Delete Report", "@text_delete_report": {"description": "Text from text_widgets: Delete Report", "context": "text_widgets"}, "text_coach_issue_status": "Coach Issue Status", "@text_coach_issue_status": {"description": "Text from text_widgets: Coach Issue Status", "context": "text_widgets"}, "text_both_person_and": "Both person and date/time are required.", "@text_both_person_and": {"description": "Text from text_widgets: Both person and date/time are required.", "context": "text_widgets"}, "text_select_widgetstatustype_by": "Select By & Date", "@text_select_widgetstatustype_by": {"description": "Text from text_widgets: Select By & Date", "context": "text_widgets"}, "text_subissue_widgetname": "SubIssue : --", "@text_subissue_widgetname": {"description": "Text from text_widgets: SubIssue : --", "context": "text_widgets"}, "text_issue_widgetname": "Issue : --", "@text_issue_widgetname": {"description": "Text from text_widgets: Issue : --", "context": "text_widgets"}, "text_confirm": "Confirm", "@text_confirm": {"description": "Text from text_widgets: Confirm", "context": "text_widgets"}, "text_manage_issues": "Manage Issues", "@text_manage_issues": {"description": "Text from text_widgets: Manage Issues", "context": "text_widgets"}, "text_rake_deficiency_report": "Rake Deficiency Report Issues", "@text_rake_deficiency_report": {"description": "Text from text_widgets: Rake Deficiency Report Issues", "context": "text_widgets"}, "text_upload_pnr_image": "Upload PNR Image", "@text_upload_pnr_image": {"description": "Text from text_widgets: Upload PNR Image", "context": "text_widgets"}, "text_pick_imagesvideos_for": "Pick Images/Videos for Feedback", "@text_pick_imagesvideos_for": {"description": "Text from text_widgets: Pick Images/Videos for Feedback", "context": "text_widgets"}, "text_please_wait_until": "Please wait until the upload is complete", "@text_please_wait_until": {"description": "Text from text_widgets: Please wait until the upload is complete", "context": "text_widgets"}, "text_submit_feedback": "Submit <PERSON>", "@text_submit_feedback": {"description": "Text from text_widgets: Submit Feedback", "context": "text_widgets"}, "text_verify_email": "<PERSON><PERSON><PERSON>", "@text_verify_email": {"description": "Text from text_widgets: Verify Email", "context": "text_widgets"}, "text_check_your_inbox": "• Check your inbox first", "@text_check_your_inbox": {"description": "Text from text_widgets: • Check your inbox first", "context": "text_widgets"}, "text_if_not_found": "• If not found, check spam/junk folder", "@text_if_not_found": {"description": "Text from text_widgets: • If not found, check spam/junk folder", "context": "text_widgets"}, "text_add_our_domain": "• Add our domain to your safe sender list", "@text_add_our_domain": {"description": "Text from text_widgets: • Add our domain to your safe sender list", "context": "text_widgets"}, "text_i_understand": "I Understand", "@text_i_understand": {"description": "Text from text_widgets: I Understand", "context": "text_widgets"}, "text_nonac": "NONAC", "@text_nonac": {"description": "Text from text_widgets: NONAC", "context": "text_widgets"}, "text_select": "Select", "@text_select": {"description": "Text from text_widgets: Select", "context": "text_widgets"}, "text_failed_to_load": "Failed to load image", "@text_failed_to_load": {"description": "Text from text_widgets: Failed to load image", "context": "text_widgets"}, "text_review_feedback": "Review Feedback", "@text_review_feedback": {"description": "Review feedback dialog title", "context": "normal_review_feedback_dialogue"}, "text_deleting_feedback": "Deleting feedback...", "@text_deleting_feedback": {"description": "Text from text_widgets: Deleting feedback...", "context": "text_widgets"}, "text_feedback_deleted_successfully": "Feedback deleted successfully", "@text_feedback_deleted_successfully": {"description": "Text from text_widgets: Feedback deleted successfully", "context": "text_widgets"}, "text_error_deleting_feedback": "Error deleting feedback: $e", "@text_error_deleting_feedback": {"description": "Text from text_widgets: Error deleting feedback: $e", "context": "text_widgets"}, "text_no_feedback_available": "No feedback available for this train.", "@text_no_feedback_available": {"description": "Text from text_widgets: No feedback available for this train.", "context": "text_widgets"}, "text_train_no_trainnumber": "Train No: $trainNumber", "@text_train_no_trainnumber": {"description": "Text from text_widgets: Train No: $trainNumber", "context": "text_widgets"}, "text_non_ac": "Non AC", "@text_non_ac": {"description": "Text from text_widgets: Non AC", "context": "text_widgets"}, "text_message": "Message", "@text_message": {"description": "Text from text_widgets: Message", "context": "text_widgets"}, "text_job_chart_status": "Job Chart Status Added", "@text_job_chart_status": {"description": "Text from text_widgets: Job Chart Status Added", "context": "text_widgets"}, "text_please_select_all": "Please select all fields before submitting.", "@text_please_select_all": {"description": "Text from text_widgets: Please select all fields before submitting.", "context": "text_widgets"}, "text_update_amount_for": "Update Amount for $userId", "@text_update_amount_for": {"description": "Text from text_widgets: Update Amount for $userId", "context": "text_widgets"}, "text_assigned": "Assigned", "@text_assigned": {"description": "Text from text_widgets: Assigned", "context": "text_widgets"}, "text_amount": "Amount", "@text_amount": {"description": "Text from text_widgets: Amount", "context": "text_widgets"}, "text_no_image_url": "No image URL provided", "@text_no_image_url": {"description": "Text from text_widgets: No image URL provided", "context": "text_widgets"}, "text_image_downloaded_successfully": "Image downloaded successfully!", "@text_image_downloaded_successfully": {"description": "Text from text_widgets: Image downloaded successfully!", "context": "text_widgets"}, "text_failed_to_download": "Failed to download image", "@text_failed_to_download": {"description": "Text from text_widgets: Failed to download image", "context": "text_widgets"}, "text_failed_to_download_1": "Failed to download image: $error", "@text_failed_to_download_1": {"description": "Text from text_widgets: Failed to download image: $error", "context": "text_widgets"}, "text_image_detail": "Image Detail", "@text_image_detail": {"description": "Text from text_widgets: Image Detail", "context": "text_widgets"}, "text_download_image": "Download Image", "@text_download_image": {"description": "Text from text_widgets: Download Image", "context": "text_widgets"}, "text_train_trainnumber_details": "Train $trainNumber details deleted successfully", "@text_train_trainnumber_details": {"description": "Text from text_widgets: Train $trainNumber details deleted successfully", "context": "text_widgets"}, "text_confirm_deactivation": "Confirm Deactivation", "@text_confirm_deactivation": {"description": "Deactivation confirmation dialog title", "context": "edit_profile_form"}, "text_proceed": "Proceed", "@text_proceed": {"description": "Proceed button text", "context": "edit_profile_form"}, "text_add_email": "Add <PERSON>", "@text_add_email": {"description": "Add email button text", "context": "edit_profile_form"}, "text_back": "Back", "@text_back": {"description": "Back button text", "context": "edit_profile_form"}, "text_email_verification": "Email Verification", "@text_email_verification": {"description": "Email verification step title", "context": "edit_profile_form"}, "text_phone_verification": "Phone Verification", "@text_phone_verification": {"description": "Phone verification step title", "context": "edit_profile_form"}, "text_logout_confirmation": "Logout Confirmation", "@text_logout_confirmation": {"description": "Text from text_widgets: Logout Confirmation", "context": "text_widgets"}, "text_do_you_want": "Do you want to logout now?", "@text_do_you_want": {"description": "Text from text_widgets: Do you want to logout now?", "context": "text_widgets"}, "text_addupdate": "Add/Update", "@text_addupdate": {"description": "Text from text_widgets: Add/Update", "context": "text_widgets"}, "text_alert": "<PERSON><PERSON>", "@text_alert": {"description": "Alert dialog title", "context": "profile_forms"}, "text_generate_otp": "Generate OTP", "@text_generate_otp": {"description": "Generate OTP button text", "context": "profile_forms"}, "text_otp_sent_successfully": "OTP sent successfully!", "@text_otp_sent_successfully": {"description": "Text from text_widgets: OTP sent successfully!", "context": "text_widgets"}, "text_failed_to_send": "Failed to send OTP: $e", "@text_failed_to_send": {"description": "Text from text_widgets: Failed to send OTP: $e", "context": "text_widgets"}, "text_please_enter_the": "Please enter the OTP", "@text_please_enter_the": {"description": "Text from text_widgets: Please enter the OTP", "context": "text_widgets"}, "text_email_saved_successfully": "<PERSON><PERSON> saved successfully!", "@text_email_saved_successfully": {"description": "Success message when email is saved", "context": "change_email_modal"}, "text_failed_to_verify": "Failed to verify OTP: $e", "@text_failed_to_verify": {"description": "Text from text_widgets: Failed to verify OTP: $e", "context": "text_widgets"}, "text_send_mobile_otp": "Send Mobile OTP", "@text_send_mobile_otp": {"description": "Send mobile OTP button text", "context": "change_password_form"}, "text_send_email_otp": "Send Email OTP", "@text_send_email_otp": {"description": "Send email OTP button text", "context": "change_password_form"}, "text_uploaded_at_formatteddate": "Uploaded at: $formattedDate", "@text_uploaded_at_formatteddate": {"description": "Text from text_widgets: Uploaded at: $formattedDate", "context": "text_widgets"}, "text_uploaded_by_widgetimageresponsecreatedby": "Uploaded by: --", "@text_uploaded_by_widgetimageresponsecreatedby": {"description": "Text from text_widgets: Uploaded by: --", "context": "text_widgets"}, "text_id_widgetimageresponseid": "id: --", "@text_id_widgetimageresponseid": {"description": "Text from text_widgets: id: --", "context": "text_widgets"}, "text_coach_widgetimageresponsecoach": "Coach: --", "@text_coach_widgetimageresponsecoach": {"description": "Text from text_widgets: Coach: --", "context": "text_widgets"}, "text_issue_widgetimageresponseissue": "Issue: --", "@text_issue_widgetimageresponseissue": {"description": "Text from text_widgets: Issue: --", "context": "text_widgets"}, "text_delete_confirmation": "Delete Confirmation", "@text_delete_confirmation": {"description": "Text from text_widgets: Delete Confirmation", "context": "text_widgets"}, "text_are_you_sure_2": "Are you sure you want to delete this image?", "@text_are_you_sure_2": {"description": "Text from text_widgets: Are you sure you want to delete this image?", "context": "text_widgets"}, "text_save": "Save", "@text_save": {"description": "Text from text_widgets: Save", "context": "text_widgets"}, "text_ehkca": "EHK/CA", "@text_ehkca": {"description": "Text from text_widgets: EHK/CA", "context": "text_widgets"}, "text_image_upload_initiated": "Image upload initiated successfully", "@text_image_upload_initiated": {"description": "Text from text_widgets: Image upload initiated successfully", "context": "text_widgets"}, "text_failed_to_upload_1": "Failed to upload image", "@text_failed_to_upload_1": {"description": "Text from text_widgets: Failed to upload image", "context": "text_widgets"}, "text_please_select_an_1": "Please select an image to upload", "@text_please_select_an_1": {"description": "Text from text_widgets: Please select an image to upload", "context": "text_widgets"}, "text_jobchart_deleted_successfully": "Job<PERSON>hart deleted successfully", "@text_jobchart_deleted_successfully": {"description": "Text from text_widgets: <PERSON><PERSON><PERSON> deleted successfully", "context": "text_widgets"}, "text_failed_to_delete_1": "Failed to delete JobChart", "@text_failed_to_delete_1": {"description": "Text from text_widgets: Failed to delete JobChart", "context": "text_widgets"}, "text_pick_image": "Pick Image", "@text_pick_image": {"description": "Text from text_widgets: Pick Image", "context": "text_widgets"}, "text_upload_image": "Upload Image", "@text_upload_image": {"description": "Text from text_widgets: Upload Image", "context": "text_widgets"}, "text_failed_to_load_1": "Failed to load train numbers: $e", "@text_failed_to_load_1": {"description": "Text from text_widgets: Failed to load train numbers: $e", "context": "text_widgets"}, "text_language": "Language", "@text_language": {"description": "Text from text_widgets: Language", "context": "text_widgets"}, "text_select_language": "Select Language", "@text_select_language": {"description": "Text from text_widgets: Select Language", "context": "text_widgets"}, "text_train_tracker": "Train Tracker", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "Assign <PERSON>", "@text_assign_ca": {"description": "Title for assigning Coach <PERSON><PERSON>ant", "context": "assign_ehk_ca_screen"}, "text_assign_cs": "Assign <PERSON>", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR Details", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_rail_sathi": "Rail Sathi", "@text_rail_sathi": {"description": "Text from text_widgets: Rail Sathi", "context": "text_widgets"}, "text_passenger_chart": "Passenger Chart", "@text_passenger_chart": {"description": "Passenger chart section title", "context": "train_details_screen"}, "text_map_screen": "Map Screen", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "Configuration", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "Reports", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "Passenger Feedback", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "Rake Deficiency Report", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS to MCC Handover", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC to OBHS Handover", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "Upload data", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "User Management", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "Issue Management", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "Rail Sathi Qr", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "Customer Care", "@text_customer_care": {"description": "Customer care screen title", "context": "customer_care_screen"}, "text_menu": "<PERSON><PERSON>", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "title_location_access_required": "Location Access Required", "@title_location_access_required": {"description": "Text from app_bar_titles: Location Access Required", "context": "app_bar_titles"}, "title_upload_status": "Upload Status", "@title_upload_status": {"description": "Text from app_bar_titles: Upload Status", "context": "app_bar_titles"}, "title_compressing_image": "Compressing image", "@title_compressing_image": {"description": "Text from app_bar_titles: Compressing image", "context": "app_bar_titles"}, "title_upload_entrykeysubstring0_6": "Upload --...", "@title_upload_entrykeysubstring0_6": {"description": "Text from app_bar_titles: Upload --...", "context": "app_bar_titles"}, "title_permission_denied": "Permission Denied", "@title_permission_denied": {"description": "Text from app_bar_titles: Permission Denied", "context": "app_bar_titles"}, "title_confirm_delete": "Confirm Delete", "@title_confirm_delete": {"description": "Text from app_bar_titles: Confirm Delete", "context": "app_bar_titles"}, "title_add_new_item": "Add New Item", "@title_add_new_item": {"description": "Text from app_bar_titles: Add New Item", "context": "app_bar_titles"}, "title_add_issue": "Add Issue", "@title_add_issue": {"description": "Text from app_bar_titles: Add Issue", "context": "app_bar_titles"}, "title_add_subissue": "Add Subissue", "@title_add_subissue": {"description": "Text from app_bar_titles: Add Subissue", "context": "app_bar_titles"}, "title_select_widgetstatustype_by": "Select -- By & Date", "@title_select_widgetstatustype_by": {"description": "Text from app_bar_titles: Select -- By & Date", "context": "app_bar_titles"}, "title_update_amount_for": "Update Amount for $userId", "@title_update_amount_for": {"description": "Text from app_bar_titles: Update Amount for $userId", "context": "app_bar_titles"}, "form_train_number": "Train Number:", "@form_train_number": {"description": "Label for train number field", "context": "image_upload"}, "form_date": "Date", "@form_date": {"description": "Text from form_labels: Date", "context": "form_labels"}, "form_select_date_ddmmmyyyy": "Select Date (DD-MMM-YYYY)", "@form_select_date_ddmmmyyyy": {"description": "Label for date selection field", "context": "map_screen"}, "form_first_name": "First Name *", "@form_first_name": {"description": "Text from form_labels: First Name *", "context": "form_labels"}, "form_enter_first_name": "Enter first name", "@form_enter_first_name": {"description": "Text from form_labels: Enter first name", "context": "form_labels"}, "form_middle_name_optional": "Middle Name (Optional)", "@form_middle_name_optional": {"description": "Optional middle name field label", "context": "signup_form"}, "form_enter_middle_name": "Enter middle name", "@form_enter_middle_name": {"description": "Text from form_labels: Enter middle name", "context": "form_labels"}, "form_last_name": "Last Name *", "@form_last_name": {"description": "Text from form_labels: Last Name *", "context": "form_labels"}, "form_enter_last_name": "Enter last name", "@form_enter_last_name": {"description": "Text from form_labels: Enter last name", "context": "form_labels"}, "form_phone_number": "Phone Number", "@form_phone_number": {"description": "Label for phone number field", "context": "rail_sathi_write_complaint"}, "form_secondary_phone_number": "Secondary Phone Number (Optional)", "@form_secondary_phone_number": {"description": "Text from form_labels: Secondary Phone Number (Optional)", "context": "form_labels"}, "form_whatsapp_number": "WhatsApp Number", "@form_whatsapp_number": {"description": "WhatsApp number field label", "context": "signup_form"}, "form_enter_10digit_whatsapp": "Enter 10-digit WhatsApp number", "@form_enter_10digit_whatsapp": {"description": "Text from form_labels: Enter 10-digit WhatsApp number", "context": "form_labels"}, "form_email": "Email *", "@form_email": {"description": "Text from form_labels: Email *", "context": "form_labels"}, "form_enter_your_email": "Enter your email address", "@form_enter_your_email": {"description": "Text from form_labels: Enter your email address", "context": "form_labels"}, "form_enter_your_first": "Enter your first name", "@form_enter_your_first": {"description": "Text from form_labels: Enter your first name", "context": "form_labels"}, "form_enter_your_middle": "Enter your middle name", "@form_enter_your_middle": {"description": "Text from form_labels: Enter your middle name", "context": "form_labels"}, "form_enter_your_last": "Enter your last name", "@form_enter_your_last": {"description": "Text from form_labels: Enter your last name", "context": "form_labels"}, "form_enter_10digit_secondary": "Enter 10-digit secondary phone number", "@form_enter_10digit_secondary": {"description": "Text from form_labels: Enter 10-digit secondary phone number", "context": "form_labels"}, "form_email_1": "Email", "@form_email_1": {"description": "Text from form_labels: Email", "context": "form_labels"}, "form_mobile_number": "Mobile Number", "@form_mobile_number": {"description": "Form field label for mobile number", "context": "normal_review_feedback_dialogue"}, "form_enter_your_10digit": "Enter your 10-digit mobile number only", "@form_enter_your_10digit": {"description": "Text from form_labels: Enter your 10-digit mobile number only", "context": "form_labels"}, "form_password": "Password *", "@form_password": {"description": "Password field label", "context": "login_form"}, "form_select_train_numbers": "Select Train Numbers", "@form_select_train_numbers": {"description": "Text from form_labels: Select Train Numbers", "context": "form_labels"}, "form_zone": "Zone", "@form_zone": {"description": "Text from form_labels: Zone", "context": "form_labels"}, "form_zone_1": "Zone *", "@form_zone_1": {"description": "Text from form_labels: Zone *", "context": "form_labels"}, "form_employee_id": "Employee Id *", "@form_employee_id": {"description": "Text from form_labels: Employee Id *", "context": "form_labels"}, "form_depot": "Depot *", "@form_depot": {"description": "Text from form_labels: Depot *", "context": "form_labels"}, "form_reenter_password": "Re-enter Password *", "@form_reenter_password": {"description": "Text from form_labels: Re-enter Password *", "context": "form_labels"}, "form_divisions": "Divisions", "@form_divisions": {"description": "Text from form_labels: Divisions", "context": "form_labels"}, "form_divisions_1": "Divisions *", "@form_divisions_1": {"description": "Text from form_labels: Divisions *", "context": "form_labels"}, "form_select_coaches": "Select Coaches", "@form_select_coaches": {"description": "Text from form_labels: Select Coaches", "context": "form_labels"}, "form_middle_name": "Middle Name", "@form_middle_name": {"description": "Text from form_labels: Middle Name", "context": "form_labels"}, "form_select_train_number": "Select Train Number", "@form_select_train_number": {"description": "Label for train number selection", "context": "map_screen"}, "form_train_name": "Train Name", "@form_train_name": {"description": "Label for train name field", "context": "map_screen"}, "form_select_stations": "Select Stations", "@form_select_stations": {"description": "Text from form_labels: Select Stations", "context": "form_labels"}, "form_select_date": "Select Date", "@form_select_date": {"description": "Text from form_labels: Select Date", "context": "form_labels"}, "form_whatsapp_number_1": "WhatsApp Number *", "@form_whatsapp_number_1": {"description": "Text from form_labels: WhatsApp Number *", "context": "form_labels"}, "form_enter_secondary_phone": "Enter secondary phone number (Optional)", "@form_enter_secondary_phone": {"description": "Text from form_labels: Enter secondary phone number (Optional)", "context": "form_labels"}, "form_mobile_number_1": "Mobile Number *", "@form_mobile_number_1": {"description": "Text from form_labels: Mobile Number *", "context": "form_labels"}, "form_enter_mobile_number": "Enter mobile number to fetch details", "@form_enter_mobile_number": {"description": "Text from form_labels: Enter mobile number to fetch details", "context": "form_labels"}, "form_enter_mobile_number_1": "Enter Mobile Number", "@form_enter_mobile_number_1": {"description": "Text from form_labels: Enter Mobile Number", "context": "form_labels"}, "form_related_train": "Related Train", "@form_related_train": {"description": "Text from form_labels: Related Train", "context": "form_labels"}, "form_division": "Division", "@form_division": {"description": "Text from form_labels: Division", "context": "form_labels"}, "form_depot_1": "Depot", "@form_depot_1": {"description": "Text from form_labels: Depot", "context": "form_labels"}, "form_charting_day": "Charting Day", "@form_charting_day": {"description": "Text from form_labels: Charting Day", "context": "form_labels"}, "form_from_station": "From Station", "@form_from_station": {"description": "Text from form_labels: From Station", "context": "form_labels"}, "form_to_station": "To Station", "@form_to_station": {"description": "Text from form_labels: To Station", "context": "form_labels"}, "form_direction_updown": "Direction (Up/Down)", "@form_direction_updown": {"description": "Text from form_labels: Direction (Up/Down)", "context": "form_labels"}, "form_start_time": "Start Time", "@form_start_time": {"description": "Text from form_labels: Start Time", "context": "form_labels"}, "form_eg_0900_am": "e.g. 09:00 AM", "@form_eg_0900_am": {"description": "Text from form_labels: e.g. 09:00 AM", "context": "form_labels"}, "form_end_time": "End Time", "@form_end_time": {"description": "Text from form_labels: End Time", "context": "form_labels"}, "form_eg_0500_pm": "e.g. 05:00 PM", "@form_eg_0500_pm": {"description": "Text from form_labels: e.g. 05:00 PM", "context": "form_labels"}, "form_charting_time": "Charting Time", "@form_charting_time": {"description": "Text from form_labels: Charting Time", "context": "form_labels"}, "form_return_gap_days": "Return Gap (Days)", "@form_return_gap_days": {"description": "Text from form_labels: Return Gap (Days)", "context": "form_labels"}, "form_inout": "In/Out", "@form_inout": {"description": "Text from form_labels: In/Out", "context": "form_labels"}, "form_related_train_number": "Related Train Number", "@form_related_train_number": {"description": "Text from form_labels: Related Train Number", "context": "form_labels"}, "form_updown": "Up/Down", "@form_updown": {"description": "Text from form_labels: Up/Down", "context": "form_labels"}, "form_train_type": "Train Type", "@form_train_type": {"description": "Text from form_labels: Train Type", "context": "form_labels"}, "form_search_train_number": "Search Train Number", "@form_search_train_number": {"description": "Text from form_labels: Search Train Number", "context": "form_labels"}, "form_coaches_comma_separated": "Coaches (comma separated)", "@form_coaches_comma_separated": {"description": "Text from form_labels: Coaches (comma separated)", "context": "form_labels"}, "form_eg_h_gsl": "E.g., H, GSL, A, B, M", "@form_eg_h_gsl": {"description": "Text from form_labels: E.g., H, GSL, A, B, M", "context": "form_labels"}, "form_enter_coach_names": "Enter coach names separated by commas", "@form_enter_coach_names": {"description": "Text from form_labels: Enter coach names separated by commas", "context": "form_labels"}, "form_select_days": "Select Days", "@form_select_days": {"description": "Text from form_labels: Select Days", "context": "form_labels"}, "form_add_stoppage": "Add Stoppage", "@form_add_stoppage": {"description": "Text from form_labels: Add Stoppage", "context": "form_labels"}, "form_type_a_station": "Type a station name and press Enter or Space", "@form_type_a_station": {"description": "Text from form_labels: Type a station name and press Enter or Space", "context": "form_labels"}, "form_search": "Search", "@form_search": {"description": "Text from form_labels: Search", "context": "form_labels"}, "form_stoppages_in_sequence": "Stoppages in Sequence", "@form_stoppages_in_sequence": {"description": "Text from form_labels: Stoppages in Sequence", "context": "form_labels"}, "form_type_a_station_1": "Type a Station and hit space or Enter", "@form_type_a_station_1": {"description": "Text from form_labels: Type a Station and hit space or Enter", "context": "form_labels"}, "form_frequency": "Frequency", "@form_frequency": {"description": "Text from form_labels: Frequency", "context": "form_labels"}, "form_enter_new_coach": "Enter new coach", "@form_enter_new_coach": {"description": "Text from form_labels: Enter new coach", "context": "form_labels"}, "form_use_comma_to": "Use comma to add multiple coaches", "@form_use_comma_to": {"description": "Text from form_labels: Use comma to add multiple coaches", "context": "form_labels"}, "form_add_your_comments": "Add your comments here (max 250 characters)...", "@form_add_your_comments": {"description": "Text from form_labels: Add your comments here (max 250 characters)...", "context": "form_labels"}, "form_search_by_name": "Search by name, email, phone or depot", "@form_search_by_name": {"description": "Text from form_labels: Search by name, email, phone or depot", "context": "form_labels"}, "form_train": "Train", "@form_train": {"description": "Text from form_labels: Train", "context": "form_labels"}, "form_complaint_type": "Complaint Type", "@form_complaint_type": {"description": "Form label for complaint type field", "context": "view_complaints"}, "form_status": "Status", "@form_status": {"description": "Form label for status field", "context": "view_complaints"}, "form_write_your_issue": "Write your issue", "@form_write_your_issue": {"description": "Label for issue description field", "context": "rail_sathi_write_complaint"}, "form_issue_status": "Issue Status", "@form_issue_status": {"description": "Label for issue status field", "context": "rail_sathi_write_complaint"}, "form_name": "Name", "@form_name": {"description": "Form label for name field", "context": "view_complaints"}, "form_search_by_train": "Search by train number or name", "@form_search_by_train": {"description": "Text from form_labels: Search by train number or name", "context": "form_labels"}, "form_train_selection": "Train Selection", "@form_train_selection": {"description": "Label for train selection field", "context": "rail_sathi_write_complaint"}, "form_journey_start_date": "Journey Start Date", "@form_journey_start_date": {"description": "Label for journey start date field", "context": "rail_sathi_write_complaint"}, "form_ddmmyyyy": "DD/MM/YYYY", "@form_ddmmyyyy": {"description": "Text from form_labels: DD/MM/YYYY", "context": "form_labels"}, "form_coach": "Coach", "@form_coach": {"description": "Form label for coach field", "context": "view_complaints"}, "form_berth": "<PERSON><PERSON>", "@form_berth": {"description": "Label for berth field", "context": "rail_sathi_write_complaint"}, "form_issue_name": "Issue Name", "@form_issue_name": {"description": "Label for issue name input field", "context": "issue_screen"}, "form_subissue_name": "Subissue Name", "@form_subissue_name": {"description": "Form label for subissue name", "context": "issue_screen"}, "form_search_1": "Search...", "@form_search_1": {"description": "Text from form_labels: Search...", "context": "form_labels"}, "form_widgetstatustype_by": "-- by", "@form_widgetstatustype_by": {"description": "Text from form_labels: -- by", "context": "form_labels"}, "form_select_date_time": "Select Date & Time", "@form_select_date_time": {"description": "Text from form_labels: Select Date & Time", "context": "form_labels"}, "form_add_your_feedback": "Add your feedback here...", "@form_add_your_feedback": {"description": "Text from form_labels: Add your feedback here...", "context": "form_labels"}, "form_task_status": "Task Status", "@form_task_status": {"description": "Form field label for task status", "context": "normal_review_feedback_dialogue"}, "form_search_train_number_1": "Search train number", "@form_search_train_number_1": {"description": "Text from form_labels: Search train number", "context": "form_labels"}, "form_train_number_1": "Train Number *", "@form_train_number_1": {"description": "Text from form_labels: Train Number *", "context": "form_labels"}, "form_pnr_number": "PNR Number", "@form_pnr_number": {"description": "Form field label for PNR number", "context": "normal_review_feedback_dialogue"}, "form_passenger_name": "Passenger Name", "@form_passenger_name": {"description": "Form field label for passenger name", "context": "normal_review_feedback_dialogue"}, "form_coach_no": "Coach No", "@form_coach_no": {"description": "Form field label for coach number", "context": "normal_review_feedback_dialogue"}, "form_berth_no": "Berth No", "@form_berth_no": {"description": "Form field label for berth number", "context": "normal_review_feedback_dialogue"}, "form_email_id": "Email ID", "@form_email_id": {"description": "Email ID form field label", "context": "normal_review_feedback_dialogue"}, "form_enter_otp": "Enter OTP", "@form_enter_otp": {"description": "Enter OTP form field label", "context": "normal_review_feedback_dialogue"}, "form_issue_type": "Issue Type", "@form_issue_type": {"description": "Label for issue type field", "context": "rm_review_feedback_dialogue"}, "form_sub_issue_type": "Sub Issue Type", "@form_sub_issue_type": {"description": "Label for sub issue type field", "context": "rm_review_feedback_dialogue"}, "form_resolved_yesno": "Resolved (Yes/No) *", "@form_resolved_yesno": {"description": "Text from form_labels: Resolved (Yes/No) *", "context": "form_labels"}, "form_crn_number": "CRN Number", "@form_crn_number": {"description": "Label for CRN number field", "context": "rm_review_feedback_dialogue"}, "form_train_no": "Train No *", "@form_train_no": {"description": "Train number form field label", "context": "rm_feedback_screen"}, "form_train_name_1": "Train Name *", "@form_train_name_1": {"description": "Text from form_labels: Train Name *", "context": "form_labels"}, "form_marks_1_to": "Marks (1 to 10) *", "@form_marks_1_to": {"description": "Text from form_labels: Marks (1 to 10) *", "context": "form_labels"}, "form_remarks_by_passenger": "Remarks by Passenger", "@form_remarks_by_passenger": {"description": "Text from form_labels: Remarks by Passenger", "context": "form_labels"}, "form_passenger_name_1": "Passenger Name", "@form_passenger_name_1": {"description": "Text from form_labels: Passenger Name", "context": "form_labels"}, "form_pnr_number_1": "PNR Number", "@form_pnr_number_1": {"description": "Text from form_labels: PNR Number", "context": "form_labels"}, "form_crn_number_1": "CRN Number", "@form_crn_number_1": {"description": "Text from form_labels: CRN Number", "context": "form_labels"}, "form_coach_no_1": "Coach No", "@form_coach_no_1": {"description": "Text from form_labels: Coach No", "context": "form_labels"}, "form_berth_no_1": "Berth No", "@form_berth_no_1": {"description": "Text from form_labels: Berth No", "context": "form_labels"}, "form_remarks": "Remarks", "@form_remarks": {"description": "Label for remarks field", "context": "rm_review_feedback_dialogue"}, "form_task_status_1": "Task Status", "@form_task_status_1": {"description": "Text from form_labels: Task Status", "context": "form_labels"}, "form_feedback": "<PERSON><PERSON><PERSON>", "@form_feedback": {"description": "Feedback form field label", "context": "normal_review_feedback_dialogue"}, "form_amount_in_hand": "Amount in Hand (₹)", "@form_amount_in_hand": {"description": "Text from form_labels: Amount in Hand (₹)", "context": "form_labels"}, "form_select_user": "Select User", "@form_select_user": {"description": "Text from form_labels: Select User", "context": "form_labels"}, "form_enter_email_otp": "Enter Email OTP", "@form_enter_email_otp": {"description": "Text from form_labels: Enter Email OTP", "context": "form_labels"}, "form_enter_phone_otp": "Enter Phone OTP", "@form_enter_phone_otp": {"description": "Text from form_labels: Enter Phone OTP", "context": "form_labels"}, "form_select_coaches_optional": "Select Coaches (optional)", "@form_select_coaches_optional": {"description": "Text from form_labels: Select Coaches (optional)", "context": "form_labels"}, "screen_manage_issues_subissues": "Manage Issues & Subissues", "@screen_manage_issues_subissues": {"description": "Screen title for IssueScreen", "context": "issue_screen"}, "error_failed_to_load_issues": "Failed to load issues: {error}", "@error_failed_to_load_issues": {"description": "Error message when loading issues fails", "context": "obhs_to_mcc_handover_screen", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "error_authentication_required": "Authentication required. Please log in.", "@error_authentication_required": {"description": "Error message when authentication is required", "context": "issue_screen"}, "error_failed_to_add_issue": "Failed to add issue", "@error_failed_to_add_issue": {"description": "Error message when adding issue fails", "context": "issue_screen"}, "error_failed_to_update_issue": "Failed to update issue", "@error_failed_to_update_issue": {"description": "Error message when updating issue fails", "context": "issue_screen"}, "error_failed_to_delete_issue": "Failed to delete issue", "@error_failed_to_delete_issue": {"description": "Error message when deleting issue fails", "context": "issue_screen"}, "error_deleting_issue": "Error deleting issue: {error}", "@error_deleting_issue": {"description": "Error message when deleting issue fails with details", "context": "issue_screen", "placeholders": {"error": {"type": "String"}}}, "error_failed_to_add_subissue": "Failed to add subissue", "@error_failed_to_add_subissue": {"description": "Error message when adding subissue fails", "context": "issue_screen"}, "error_failed_to_delete_subissue": "Failed to delete subissue", "@error_failed_to_delete_subissue": {"description": "Error message when deleting subissue fails", "context": "issue_screen"}, "error_deleting_subissue": "Error deleting subissue: {error}", "@error_deleting_subissue": {"description": "Error message when deleting subissue fails with details", "context": "issue_screen", "placeholders": {"error": {"type": "String"}}}, "text_issues": "Issues", "@text_issues": {"description": "Tab label for issues", "context": "issue_screen"}, "text_subissues": "Subissues", "@text_subissues": {"description": "Tab label for subissues", "context": "issue_screen"}, "text_edit_issue": "Edit Issue", "@text_edit_issue": {"description": "Title for editing issue form", "context": "issue_screen"}, "text_add_new_issue": "Add New Issue", "@text_add_new_issue": {"description": "Title for adding new issue form", "context": "issue_screen"}, "error_enter_issue_name": "Please enter an issue name", "@error_enter_issue_name": {"description": "Validation error for empty issue name", "context": "issue_screen"}, "btn_cancel": "Cancel", "@btn_cancel": {"description": "Cancel button text", "context": "normal_review_feedback_dialogue"}, "btn_update": "Update", "@btn_update": {"description": "Update button text", "context": "issue_screen"}, "btn_add_issue": "Add Issue", "@btn_add_issue": {"description": "Add issue button text", "context": "issue_screen"}, "text_no_issues_found": "No issues found", "@text_no_issues_found": {"description": "Message when no issues are found", "context": "issue_screen"}, "text_subissues_count": "{count} subissues", "@text_subissues_count": {"description": "Count of subissues for an issue", "context": "issue_screen", "placeholders": {"count": {"type": "int"}}}, "text_edit_issue_tooltip": "Edit issue", "@text_edit_issue_tooltip": {"description": "Tooltip for edit issue button", "context": "issue_screen"}, "text_delete_issue_tooltip": "Delete issue", "@text_delete_issue_tooltip": {"description": "Tooltip for delete issue button", "context": "issue_screen"}, "text_view_subissues_tooltip": "View subissues", "@text_view_subissues_tooltip": {"description": "Tooltip for view subissues button", "context": "issue_screen"}, "text_select_parent_issue": "Select Parent Issue", "@text_select_parent_issue": {"description": "Label for parent issue selection", "context": "issue_screen"}, "text_edit_subissue": "Edit Subissue", "@text_edit_subissue": {"description": "Title for editing subissue", "context": "issue_screen"}, "text_add_new_subissue": "Add New Subissue", "@text_add_new_subissue": {"description": "Title for adding new subissue", "context": "issue_screen"}, "error_enter_subissue_name": "Please enter a subissue name", "@error_enter_subissue_name": {"description": "Validation error for empty subissue name", "context": "issue_screen"}, "btn_add_subissue": "Add Subissue", "@btn_add_subissue": {"description": "Add subissue button text", "context": "issue_screen"}, "text_subissues_for": "Subissues for \"{issueName}\"", "@text_subissues_for": {"description": "Title showing subissues for selected issue", "context": "issue_screen", "placeholders": {"issueName": {"type": "String"}}}, "text_please_select_issue_first": "Please select an issue first", "@text_please_select_issue_first": {"description": "Message when no issue is selected", "context": "issue_screen"}, "text_no_subissues_found": "No subissues found for this issue", "@text_no_subissues_found": {"description": "Message when no subissues are found for selected issue", "context": "issue_screen"}, "text_edit_subissue_tooltip": "Edit subissue", "@text_edit_subissue_tooltip": {"description": "Tooltip for edit subissue button", "context": "issue_screen"}, "text_delete_subissue_tooltip": "Delete subissue", "@text_delete_subissue_tooltip": {"description": "Tooltip for delete subissue button", "context": "issue_screen"}, "text_confirm_delete_issue": "Are you sure you want to delete \"{issueName}\" and all its subissues?", "@text_confirm_delete_issue": {"description": "Confirmation message for deleting issue", "context": "issue_screen", "placeholders": {"issueName": {"type": "String"}}}, "text_confirm_delete_subissue": "Are you sure you want to delete \"{subissueName}\"?", "@text_confirm_delete_subissue": {"description": "Confirmation message for deleting subissue", "context": "issue_screen", "placeholders": {"subissueName": {"type": "String"}}}, "btn_delete": "Delete", "@btn_delete": {"description": "Delete button text", "context": "view_complaints"}, "btn_deny": "<PERSON><PERSON>", "@btn_deny": {"description": "Text from button_labels: <PERSON>y", "context": "button_labels"}, "btn_enable": "Enable", "@btn_enable": {"description": "Text from button_labels: Enable", "context": "button_labels"}, "btn_decline": "Decline", "@btn_decline": {"description": "Text from button_labels: Decline", "context": "button_labels"}, "btn_accept": "Accept", "@btn_accept": {"description": "Text from button_labels: Accept", "context": "button_labels"}, "btn_self": "Self", "@btn_self": {"description": "Text from button_labels: Self", "context": "button_labels"}, "btn_other_ca": "Other CA", "@btn_other_ca": {"description": "Text from button_labels: Other CA", "context": "button_labels"}, "btn_other_ehkobhs": "Other EHK/OBHS", "@btn_other_ehkobhs": {"description": "Text from button_labels: Other EHK/OBHS", "context": "button_labels"}, "btn_close": "Close", "@btn_close": {"description": "Close button text", "context": "custom_app_bar"}, "btn_no_attendance_found": "No attendance found.", "@btn_no_attendance_found": {"description": "Text from button_labels: No attendance found.", "context": "button_labels"}, "btn_error_snapshoterror": "Error: --", "@btn_error_snapshoterror": {"description": "Text from button_labels: Error: --", "context": "button_labels"}, "attendance_screen_title": "Attendance", "@attendance_screen_title": {"description": "Title for attendance screen", "context": "attendance_screen"}, "attendance_select_date": "Select date", "@attendance_select_date": {"description": "Label for date selection dialog", "context": "attendance_screen"}, "attendance_today": "Today", "@attendance_today": {"description": "Today button in date picker", "context": "attendance_screen"}, "attendance_cancel": "Cancel", "@attendance_cancel": {"description": "Cancel button in dialogs", "context": "attendance_screen"}, "attendance_ok": "OK", "@attendance_ok": {"description": "OK button in dialogs", "context": "attendance_screen"}, "attendance_train_not_running": "Train Not Running", "@attendance_train_not_running": {"description": "Title for train not running dialog", "context": "attendance_screen"}, "attendance_train_not_running_message": "Train {trainNumber} is NOT running on {dayOfWeek}\nRunning Days: {runningDays}", "@attendance_train_not_running_message": {"description": "Message for train not running dialog", "context": "attendance_screen", "placeholders": {"trainNumber": {"type": "String"}, "dayOfWeek": {"type": "String"}, "runningDays": {"type": "String"}}}, "attendance_update_required": "Update Required", "@attendance_update_required": {"description": "Title for app update dialog", "context": "attendance_screen"}, "attendance_update_message": "A new version of the app is available. You must update to continue using the app.", "@attendance_update_message": {"description": "Message for app update dialog", "context": "attendance_screen"}, "attendance_update_now": "Update Now", "@attendance_update_now": {"description": "Button to update app", "context": "attendance_screen"}, "attendance_journey_status_updated": "Journey status updated to {status}", "@attendance_journey_status_updated": {"description": "Message when journey status is updated", "context": "attendance_screen", "placeholders": {"status": {"type": "String"}}}, "attendance_select_train_first": "Please select a train first", "@attendance_select_train_first": {"description": "Error message when no train is selected", "context": "attendance_screen"}, "attendance_inside_train": "You are now marked as inside the train", "@attendance_inside_train": {"description": "Message when marked inside train", "context": "attendance_screen"}, "attendance_outside_train": "You are now marked as outside the train", "@attendance_outside_train": {"description": "Message when marked outside train", "context": "attendance_screen"}, "attendance_failed_update_status": "Failed to update train status", "@attendance_failed_update_status": {"description": "Error message when train status update fails", "context": "attendance_screen"}, "attendance_select_train_date": "Please select a train number and date.", "@attendance_select_train_date": {"description": "Error message when train or date not selected", "context": "attendance_screen"}, "attendance_details_updated": "All details updated successfully.", "@attendance_details_updated": {"description": "Success message when details are updated", "context": "attendance_screen"}, "attendance_coaches": "Coaches: {coaches}", "@attendance_coaches": {"description": "Display assigned coaches", "context": "attendance_screen", "placeholders": {"coaches": {"type": "String"}}}, "attendance_coach_label": "🚃 Coach: {coach}", "@attendance_coach_label": {"description": "Label for coach in passenger details", "context": "attendance_screen", "placeholders": {"coach": {"type": "String"}}}, "attendance_onboarding": "🟢 Onboarding:", "@attendance_onboarding": {"description": "Label for onboarding passengers", "context": "attendance_screen"}, "attendance_onboarding_none": "🟢 Onboarding: None", "@attendance_onboarding_none": {"description": "Label when no onboarding passengers", "context": "attendance_screen"}, "attendance_deboarding": "🔴 Deboarding:", "@attendance_deboarding": {"description": "Label for deboarding passengers", "context": "attendance_screen"}, "attendance_deboarding_none": "🔴 Deboarding: None", "@attendance_deboarding_none": {"description": "Label when no deboarding passengers", "context": "attendance_screen"}, "attendance_timings": "Tim<PERSON>", "@attendance_timings": {"description": "Label for timings column", "context": "attendance_screen"}, "attendance_stoppages": "Stoppages", "@attendance_stoppages": {"description": "Label for stoppages column", "context": "attendance_screen"}, "attendance_passenger_chart": "Passenger Chart   Atten..", "@attendance_passenger_chart": {"description": "Label for passenger chart and attendance column", "context": "attendance_screen"}, "attendance_update_journey_status": "Update Journey Status", "@attendance_update_journey_status": {"description": "Title for journey status update section", "context": "attendance_screen"}, "attendance_update": "Update", "@attendance_update": {"description": "Update button text", "context": "attendance_screen"}, "attendance_inside": "Inside", "@attendance_inside": {"description": "Text for inside train status", "context": "attendance_screen"}, "attendance_go": "Go", "@attendance_go": {"description": "Text for go inside train action", "context": "attendance_screen"}, "attendance_train": "Train", "@attendance_train": {"description": "Text for train label", "context": "attendance_screen"}, "attendance_not_inside_train": "You are not inside the train. Please go inside the train first.", "@attendance_not_inside_train": {"description": "Error message when trying to update location while not inside train", "context": "attendance_screen"}, "attendance_self": "Self", "@attendance_self": {"description": "Menu option for self attendance", "context": "attendance_screen"}, "attendance_other_ca": "Other CA", "@attendance_other_ca": {"description": "Menu option for other coach attendant", "context": "attendance_screen"}, "attendance_other_ehk": "Other EHK/OBHS", "@attendance_other_ehk": {"description": "Menu option for other EHK/OBHS", "context": "attendance_screen"}, "attendance_chart_not_prepared": "Chart has not been prepared for this station", "@attendance_chart_not_prepared": {"description": "Error message when chart is not prepared", "context": "attendance_screen"}, "attendance_click_more": "click for more...", "@attendance_click_more": {"description": "Text to click for more details", "context": "attendance_screen"}, "attendance_count_label": "A: {count}", "@attendance_count_label": {"description": "Attendance count label", "context": "attendance_screen", "placeholders": {"count": {"type": "String"}}}, "attendance_failed_load_data": "Failed to load detailed data: {error}", "@attendance_failed_load_data": {"description": "Error message when loading detailed data fails", "context": "attendance_screen", "placeholders": {"error": {"type": "String"}}}, "attendance_coach_occupancy": "Coach Occupancy Details", "@attendance_coach_occupancy": {"description": "Title for coach occupancy section", "context": "attendance_screen"}, "attendance_api_summary": "API Summary Details", "@attendance_api_summary": {"description": "Title for API summary section", "context": "attendance_screen"}, "attendance_onboarding_section": "Onboarding", "@attendance_onboarding_section": {"description": "Title for onboarding section", "context": "attendance_screen"}, "attendance_offboarding_section": "Offboarding", "@attendance_offboarding_section": {"description": "Title for offboarding section", "context": "attendance_screen"}, "attendance_available_section": "Available", "@attendance_available_section": {"description": "Title for available berths section", "context": "attendance_screen"}, "attendance_berths_count": "{count} berths", "@attendance_berths_count": {"description": "Count of berths", "context": "attendance_screen", "placeholders": {"count": {"type": "String"}}}, "attendance_on_label": "On: {count}", "@attendance_on_label": {"description": "Onboard count label", "context": "attendance_screen", "placeholders": {"count": {"type": "String"}}}, "attendance_off_label": "Off: {count}", "@attendance_off_label": {"description": "Offboard count label", "context": "attendance_screen", "placeholders": {"count": {"type": "String"}}}, "attendance_available_label": "Available: {count}", "@attendance_available_label": {"description": "Available berths count label", "context": "attendance_screen", "placeholders": {"count": {"type": "String"}}}, "attendance_station_details": "Station Details - {stationCode}", "@attendance_station_details": {"description": "Title for station details popup", "context": "attendance_screen", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_train_date": "Train {trainNo} - {date}", "@attendance_train_date": {"description": "Subtitle for station details popup", "context": "attendance_screen", "placeholders": {"trainNo": {"type": "String"}, "date": {"type": "String"}}}, "attendance_coach_type": "Coach Type:", "@attendance_coach_type": {"description": "Label for coach type selection", "context": "attendance_screen"}, "attendance_sleeper": "Sleeper", "@attendance_sleeper": {"description": "Sleeper coach type", "context": "attendance_screen"}, "attendance_non_sleeper": "Non-Sleeper", "@attendance_non_sleeper": {"description": "Non-sleeper coach type", "context": "attendance_screen"}, "attendance_detailed_view": "Detailed View", "@attendance_detailed_view": {"description": "Detailed view option", "context": "attendance_screen"}, "attendance_concise_view": "Concise View", "@attendance_concise_view": {"description": "Concise view option", "context": "attendance_screen"}, "attendance_loading": "Loading...", "@attendance_loading": {"description": "Loading text", "context": "attendance_screen"}, "attendance_in_route": "In-route", "@attendance_in_route": {"description": "In-route status for berths", "context": "attendance_screen"}, "attendance_cancelled": "Cancelled", "@attendance_cancelled": {"description": "Cancelled status for berths", "context": "attendance_screen"}, "attendance_expected_charting": "Expected Charting Time: {time}", "@attendance_expected_charting": {"description": "Expected charting time label", "context": "attendance_screen", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_started": "Charting started at: {time}", "@attendance_charting_started": {"description": "Charting started time label", "context": "attendance_screen", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_refreshed": "Charting refreshed at: {time}", "@attendance_charting_refreshed": {"description": "Charting refreshed time label", "context": "attendance_screen", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_time": "Charting Time: {time}", "@attendance_charting_time": {"description": "Charting time label", "context": "attendance_screen", "placeholders": {"time": {"type": "String"}}}, "attendance_train_depot": "Train Depot:{depot}", "@attendance_train_depot": {"description": "Train depot label", "context": "attendance_screen", "placeholders": {"depot": {"type": "String"}}}, "attendance_ehk_assigned": "EHK Assigned for train:{ehkName}", "@attendance_ehk_assigned": {"description": "EHK assigned label", "context": "attendance_screen", "placeholders": {"ehkName": {"type": "String"}}}, "attendance_user_location": "User Location:{locationStatus}", "@attendance_user_location": {"description": "User location status label", "context": "attendance_screen", "placeholders": {"locationStatus": {"type": "String"}}}, "attendance_not_attendance_station": "Attendance cannot be marked for station {stationCode} as it is not an attendance station.", "@attendance_not_attendance_station": {"description": "Error message when station is not an attendance station", "context": "attendance_screen", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_too_far_from_station": "You're over 50 KM away from the selected station {stationCode}. Attendance can only be marked when you're within the allowed range.\nFor Now You can only mark attendance for stations: {nearbyStations}", "@attendance_too_far_from_station": {"description": "Error message when too far from station", "context": "attendance_screen", "placeholders": {"stationCode": {"type": "String"}, "nearbyStations": {"type": "String"}}}, "attendance_buffer_time_restriction": "The time between the current time and the train's arrival time is not within the 2-hour buffer.\nIf you're unable to see the arrival time, please contact the admin or add the arrival time for this train.", "@attendance_buffer_time_restriction": {"description": "Error message for buffer time restriction", "context": "attendance_screen"}, "attendance_location_not_fetched": "Train Location is not fetched yet, please try again later", "@attendance_location_not_fetched": {"description": "Error message when train location is not fetched", "context": "attendance_screen"}, "attendance_last_fetched": "Last fetched: {time}\nFrom User: {user}", "@attendance_last_fetched": {"description": "Last fetched information display", "context": "attendance_screen", "placeholders": {"time": {"type": "String"}, "user": {"type": "String"}}}, "attendance_daily": "Daily", "@attendance_daily": {"description": "Daily frequency label", "context": "attendance_screen"}, "attendance_na": "N/A", "@attendance_na": {"description": "Not available label", "context": "attendance_screen"}, "attendance_details_title": "Attendance Details", "@attendance_details_title": {"description": "Title for attendance details screen", "context": "attendance_details"}, "attendance_details_train_number": "Train Number:", "@attendance_details_train_number": {"description": "Label for train number in details", "context": "attendance_details"}, "attendance_details_journey_date": "Journey Date:", "@attendance_details_journey_date": {"description": "Label for journey date in details", "context": "attendance_details"}, "attendance_details_station_code": "Station Code:", "@attendance_details_station_code": {"description": "Label for station code in details", "context": "attendance_details"}, "attendance_details_no_attendance": "No attendance found.", "@attendance_details_no_attendance": {"description": "Message when no attendance records found", "context": "attendance_details"}, "attendance_details_error": "Error: {error}", "@attendance_details_error": {"description": "Error message in attendance details", "context": "attendance_details", "placeholders": {"error": {"type": "String"}}}, "attendance_details_no_data": "No data available.", "@attendance_details_no_data": {"description": "Message when no data is available", "context": "attendance_details"}, "attendance_details_username": "Username: {username}", "@attendance_details_username": {"description": "Username label in attendance entry", "context": "attendance_details", "placeholders": {"username": {"type": "String"}}}, "attendance_details_latitude": "latitude: {latitude}", "@attendance_details_latitude": {"description": "Latitude label in attendance entry", "context": "attendance_details", "placeholders": {"latitude": {"type": "String"}}}, "attendance_details_longitude": "longitude: {longitude}", "@attendance_details_longitude": {"description": "Longitude label in attendance entry", "context": "attendance_details", "placeholders": {"longitude": {"type": "String"}}}, "attendance_details_nearest_station": "nearest Station: {station}", "@attendance_details_nearest_station": {"description": "Nearest station label in attendance entry", "context": "attendance_details", "placeholders": {"station": {"type": "String"}}}, "attendance_details_distance": "Distance: {distance} km", "@attendance_details_distance": {"description": "Distance label in attendance entry", "context": "attendance_details", "placeholders": {"distance": {"type": "String"}}}, "attendance_details_updated_by": "Updated By: {updatedBy}", "@attendance_details_updated_by": {"description": "Updated by label in attendance entry", "context": "attendance_details", "placeholders": {"updatedBy": {"type": "String"}}}, "attendance_details_updated_at": "Updated At: {updatedAt}", "@attendance_details_updated_at": {"description": "Updated at label in attendance entry", "context": "attendance_details", "placeholders": {"updatedAt": {"type": "String"}}}, "attendance_details_match_percentage": "Match Percentage: {percentage}", "@attendance_details_match_percentage": {"description": "Match percentage label in attendance entry", "context": "attendance_details", "placeholders": {"percentage": {"type": "String"}}}, "attendance_details_no_human_detected": "No human detected", "@attendance_details_no_human_detected": {"description": "Message when no human is detected", "context": "attendance_details"}, "attendance_details_status": "Status: {status}", "@attendance_details_status": {"description": "Status label in attendance entry", "context": "attendance_details", "placeholders": {"status": {"type": "String"}}}, "attendance_details_status_marked": "Marked", "@attendance_details_status_marked": {"description": "Status marked text", "context": "attendance_details"}, "attendance_details_status_pending": "Pending", "@attendance_details_status_pending": {"description": "Status pending text", "context": "attendance_details"}, "btn_no_data_available": "No data available.", "@btn_no_data_available": {"description": "Text from button_labels: No data available.", "context": "button_labels"}, "btn_your_current_location": "Your current location", "@btn_your_current_location": {"description": "Text from button_labels: Your current location", "context": "button_labels"}, "btn_no_location_data": "No location data available.", "@btn_no_location_data": {"description": "Text from button_labels: No location data available.", "context": "button_labels"}, "btn_no_data_available_1": "No data available", "@btn_no_data_available_1": {"description": "Text from button_labels: No data available", "context": "button_labels"}, "btn_add_configuration": "Add Configuration", "@btn_add_configuration": {"description": "Text from button_labels: Add Configuration", "context": "button_labels"}, "btn_select_charting_day": "Select Charting Day", "@btn_select_charting_day": {"description": "Text from button_labels: Select Charting Day", "context": "button_labels"}, "btn_edit_configuration": "Edit Configuration", "@btn_edit_configuration": {"description": "Text from button_labels: Edit Configuration", "context": "button_labels"}, "btn_no_coaches_available": "No coaches available", "@btn_no_coaches_available": {"description": "Text from button_labels: No coaches available", "context": "button_labels"}, "btn_coach_handover_report": "Coach Handover Report", "@btn_coach_handover_report": {"description": "Text from button_labels: Coach Handover Report", "context": "button_labels"}, "btn_statustype_by": "$statusType By", "@btn_statustype_by": {"description": "Text from button_labels: $statusType By", "context": "button_labels"}, "btn_traintrainno_traintrainname": "-- - --", "@btn_traintrainno_traintrainname": {"description": "Text from button_labels: -- - --", "context": "button_labels"}, "btn_other": "Other", "@btn_other": {"description": "Text from button_labels: Other", "context": "button_labels"}, "btn_no_complaints_found": "No complaints found", "@btn_no_complaints_found": {"description": "Text from button_labels: No complaints found", "context": "button_labels"}, "btn_pending": "Pending", "@btn_pending": {"description": "Text from button_labels: Pending", "context": "button_labels"}, "btn_completed": "Completed", "@btn_completed": {"description": "Text from button_labels: Completed", "context": "button_labels"}, "btn_upload_json_data": "Upload Json Data", "@btn_upload_json_data": {"description": "Text from button_labels: Upload Json Data", "context": "button_labels"}, "btn_save_selection": "Save Selection", "@btn_save_selection": {"description": "Button to save issue selection", "context": "coach_handover_image_upload"}, "btn_coach_issue_status": "Coach Issue Status", "@btn_coach_issue_status": {"description": "Text from button_labels: Coach Issue Status", "context": "button_labels"}, "btn_rake_deficiency_report": "Rake Deficiency Report Issues", "@btn_rake_deficiency_report": {"description": "Text from button_labels: Rake Deficiency Report Issues", "context": "button_labels"}, "btn_no_feedback_available": "No feedback available for this train.", "@btn_no_feedback_available": {"description": "Text from button_labels: No feedback available for this train.", "context": "button_labels"}, "btn_save": "Save", "@btn_save": {"description": "Text from button_labels: Save", "context": "button_labels"}, "snackbar_please_select_a": "Please select a train first", "@snackbar_please_select_a": {"description": "Text from snackbar_messages: Please select a train first", "context": "snackbar_messages"}, "snackbar_data_refreshed_successfully": "Data refreshed successfully", "@snackbar_data_refreshed_successfully": {"description": "Text from snackbar_messages: Data refreshed successfully", "context": "snackbar_messages"}, "snackbar_train_location_saved": "Train Location Saved Successfully", "@snackbar_train_location_saved": {"description": "Text from snackbar_messages: Train Location Saved Successfully", "context": "snackbar_messages"}, "snackbar_error_fetching_images": "Error fetching images: $e", "@snackbar_error_fetching_images": {"description": "Text from snackbar_messages: Error fetching images: $e", "context": "snackbar_messages"}, "snackbar_download_started": "Download Started!", "@snackbar_download_started": {"description": "Text from snackbar_messages: Download Started!", "context": "snackbar_messages"}, "snackbar_pdf_downloaded_successfully": "PDF downloaded successfully to --", "@snackbar_pdf_downloaded_successfully": {"description": "Text from snackbar_messages: PDF downloaded successfully to --", "context": "snackbar_messages"}, "snackbar_could_not_launch": "Could not launch the link", "@snackbar_could_not_launch": {"description": "Text from snackbar_messages: Could not launch the link", "context": "snackbar_messages"}, "snackbar_refresh_failed_e": "Refresh failed: --", "@snackbar_refresh_failed_e": {"description": "Text from snackbar_messages: Refresh failed: --", "context": "snackbar_messages"}, "snackbar_return_gap_updated": "Return gap updated successfully", "@snackbar_return_gap_updated": {"description": "Text from snackbar_messages: Return gap updated successfully", "context": "snackbar_messages"}, "snackbar_data_not_refreshed": "Data not refreshed: $e", "@snackbar_data_not_refreshed": {"description": "Text from snackbar_messages: Data not refreshed: $e", "context": "snackbar_messages"}, "snackbar_invalid_response_format": "Invalid response format from server", "@snackbar_invalid_response_format": {"description": "Text from snackbar_messages: Invalid response format from server", "context": "snackbar_messages"}, "snackbar_please_select_a_1": "Please select a train and date first", "@snackbar_please_select_a_1": {"description": "Text from snackbar_messages: Please select a train and date first", "context": "snackbar_messages"}, "snackbar_please_select_images": "Please select images to upload", "@snackbar_please_select_images": {"description": "Text from snackbar_messages: Please select images to upload", "context": "snackbar_messages"}, "snackbar_please_select_at": "Please select at least one issue", "@snackbar_please_select_at": {"description": "Text from snackbar_messages: Please select at least one issue", "context": "snackbar_messages"}, "snackbar_failed_to_upload": "Failed to upload images", "@snackbar_failed_to_upload": {"description": "Text from snackbar_messages: Failed to upload images", "context": "snackbar_messages"}, "snackbar_error_updating_issue": "Error updating issue: $e", "@snackbar_error_updating_issue": {"description": "Text from snackbar_messages: Error updating issue: $e", "context": "snackbar_messages"}, "snackbar_no_images_available": "No images available", "@snackbar_no_images_available": {"description": "Text from snackbar_messages: No images available", "context": "snackbar_messages"}, "snackbar_failed_to_fetch": "Failed to fetch complaints", "@snackbar_failed_to_fetch": {"description": "Text from snackbar_messages: Failed to fetch complaints", "context": "snackbar_messages"}, "snackbar_error_fetching_trains": "Error fetching trains: $e", "@snackbar_error_fetching_trains": {"description": "Text from snackbar_messages: Error fetching trains: $e", "context": "snackbar_messages"}, "snackbar_complaint_updated": "<PERSON><PERSON><PERSON><PERSON> updated", "@snackbar_complaint_updated": {"description": "Success message when complaint is updated", "context": "view_complaints"}, "snackbar_update_failed": "Update failed", "@snackbar_update_failed": {"description": "Error message when update fails", "context": "view_complaints"}, "snackbar_complaint_deleted_successfully": "<PERSON><PERSON><PERSON><PERSON> deleted successfully", "@snackbar_complaint_deleted_successfully": {"description": "Success message when complaint is deleted", "context": "view_complaints"}, "snackbar_failed_to_delete": "Failed to delete complaint", "@snackbar_failed_to_delete": {"description": "Text from snackbar_messages: Failed to delete complaint", "context": "snackbar_messages"}, "snackbar_failed_to_submit": "Failed to submit complaint", "@snackbar_failed_to_submit": {"description": "Text from snackbar_messages: Failed to submit complaint", "context": "snackbar_messages"}, "snackbar_error_e": "Error: $e", "@snackbar_error_e": {"description": "Text from snackbar_messages: Error: $e", "context": "snackbar_messages"}, "snackbar_issues_saved_upload": "Issues saved upload Images/Videos", "@snackbar_issues_saved_upload": {"description": "Text from snackbar_messages: Issues saved upload Images/Videos", "context": "snackbar_messages"}, "snackbar_feedback_deleted_successfully": "Feedback deleted successfully", "@snackbar_feedback_deleted_successfully": {"description": "Text from snackbar_messages: Feedback deleted successfully", "context": "snackbar_messages"}, "snackbar_error_deleting_feedback": "Error deleting feedback: $e", "@snackbar_error_deleting_feedback": {"description": "Text from snackbar_messages: Error deleting feedback: $e", "context": "snackbar_messages"}, "snackbar_job_chart_status": "Job Chart Status Added", "@snackbar_job_chart_status": {"description": "Text from snackbar_messages: Job Chart Status Added", "context": "snackbar_messages"}, "snackbar_no_image_url": "No image URL provided", "@snackbar_no_image_url": {"description": "Text from snackbar_messages: No image URL provided", "context": "snackbar_messages"}, "snackbar_image_downloaded_successfully": "Image downloaded successfully!", "@snackbar_image_downloaded_successfully": {"description": "Text from snackbar_messages: Image downloaded successfully!", "context": "snackbar_messages"}, "snackbar_failed_to_download": "Failed to download image", "@snackbar_failed_to_download": {"description": "Text from snackbar_messages: Failed to download image", "context": "snackbar_messages"}, "snackbar_failed_to_download_1": "Failed to download image: $error", "@snackbar_failed_to_download_1": {"description": "Text from snackbar_messages: Failed to download image: $error", "context": "snackbar_messages"}, "snackbar_otp_sent_successfully": "OTP sent successfully!", "@snackbar_otp_sent_successfully": {"description": "Text from snackbar_messages: OTP sent successfully!", "context": "snackbar_messages"}, "snackbar_failed_to_send": "Failed to send OTP: $e", "@snackbar_failed_to_send": {"description": "Text from snackbar_messages: Failed to send OTP: $e", "context": "snackbar_messages"}, "snackbar_please_enter_the": "Please enter the OTP", "@snackbar_please_enter_the": {"description": "Text from snackbar_messages: Please enter the OTP", "context": "snackbar_messages"}, "snackbar_email_saved_successfully": "<PERSON><PERSON> saved successfully!", "@snackbar_email_saved_successfully": {"description": "Text from snackbar_messages: <PERSON>ail saved successfully!", "context": "snackbar_messages"}, "snackbar_failed_to_verify": "Failed to verify OTP: $e", "@snackbar_failed_to_verify": {"description": "Text from snackbar_messages: Failed to verify OTP: $e", "context": "snackbar_messages"}, "snackbar_jobchart_deleted_successfully": "Job<PERSON>hart deleted successfully", "@snackbar_jobchart_deleted_successfully": {"description": "Text from snackbar_messages: <PERSON><PERSON><PERSON> deleted successfully", "context": "snackbar_messages"}, "snackbar_failed_to_delete_1": "Failed to delete JobChart", "@snackbar_failed_to_delete_1": {"description": "Text from snackbar_messages: Failed to delete JobChart", "context": "snackbar_messages"}, "snackbar_failed_to_load": "Failed to load train numbers: $e", "@snackbar_failed_to_load": {"description": "Text from snackbar_messages: Failed to load train numbers: $e", "context": "snackbar_messages"}, "text_welcome_to_railops": "Welcome to RailOps", "@text_welcome_to_railops": {"description": "Welcome message on login screen", "context": "login_screen"}, "text_sign_up_to_railops": "Sign Up to RailOps", "@text_sign_up_to_railops": {"description": "Sign up screen title", "context": "sign_up_screen"}, "text_login_using_fingerprint": "Login using fingerprint", "@text_login_using_fingerprint": {"description": "Biometric authentication prompt", "context": "authentication"}, "text_logging_in_please_wait": "Logging in... Please wait.", "@text_logging_in_please_wait": {"description": "Loading message during login", "context": "authentication"}, "text_log_in": "Log in", "@text_log_in": {"description": "Login button text", "context": "authentication"}, "text_home": "Home", "@text_home": {"description": "Home navigation title", "context": "navigation"}, "text_home_screen": "Home Screen", "@text_home_screen": {"description": "Home screen title text", "context": "home_screen"}, "error_enter_mobile_number": "Please enter your mobile number", "@error_enter_mobile_number": {"description": "Mobile number validation error", "context": "login_form"}, "error_mobile_number_digits": "Mobile number must be 10 digits", "@error_mobile_number_digits": {"description": "Mobile number length validation error", "context": "login_form"}, "error_enter_password": "Please enter your password", "@error_enter_password": {"description": "Password validation error", "context": "login_form"}, "btn_log_in_with_mobile": "Log in with Mobile Number", "@btn_log_in_with_mobile": {"description": "Mobile login button text", "context": "login_form"}, "btn_new_user_sign_up": "New User? Sign Up Here", "@btn_new_user_sign_up": {"description": "Sign up button text", "context": "login_form"}, "text_privacy_policy": "Privacy Policy", "@text_privacy_policy": {"description": "Privacy policy link text", "context": "login_form"}, "text_terms_conditions": "Term & Condition", "@text_terms_conditions": {"description": "Terms and conditions link text", "context": "login_form"}, "text_know_about_app": "Know about this app", "@text_know_about_app": {"description": "App information link text", "context": "login_form"}, "msg_login_successful": "Login Successful", "@msg_login_successful": {"description": "Login success message", "context": "authentication"}, "msg_invalid_pin": "Invalid PIN", "@msg_invalid_pin": {"description": "Invalid PIN error message", "context": "authentication"}, "text_train_details": "Train Details", "@text_train_details": {"description": "Title for train details screen", "context": "train_details_screen"}, "text_please_enable_location_services": "Please enable location services to proceed.", "@text_please_enable_location_services": {"description": "Alert dialog content for enabling location services", "context": "train_details_screen"}, "text_location_permissions_required": "Location permissions are required to fetch upcoming station details. Please allow location access.", "@text_location_permissions_required": {"description": "Alert dialog content for location permissions", "context": "train_details_screen"}, "text_location_permission_denied_forever": "Location Permission Denied Forever", "@text_location_permission_denied_forever": {"description": "Alert dialog title when location permission is permanently denied", "context": "train_details_screen"}, "text_location_permissions_permanently_denied": "Location permissions are permanently denied. Please go to app settings to allow location access.", "@text_location_permissions_permanently_denied": {"description": "Alert dialog content for permanently denied location permissions", "context": "train_details_screen"}, "text_select_all": "Select All", "@text_select_all": {"description": "Select all coaches option", "context": "add_train_form"}, "text_location_permissions_error": "Location permissions are permanently denied, we cannot request permissions.", "@text_location_permissions_error": {"description": "Error message when location permissions are permanently denied", "context": "train_details_screen"}, "text_refresh_failed": "Refresh failed: {error}", "@text_refresh_failed": {"description": "Error message when refresh fails", "context": "train_details_screen", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "text_turn_on_location_services": "Please turn on location services", "@text_turn_on_location_services": {"description": "Message to turn on location services", "context": "train_details_screen"}, "text_no_location_data_available": "No location data available", "@text_no_location_data_available": {"description": "Message when location data is not available", "context": "train_details_screen"}, "text_edit_train_details": "Edit Train Details", "@text_edit_train_details": {"description": "Edit train details screen title", "context": "edit_train_screen"}, "text_search_train_number": "Search Train Number", "@text_search_train_number": {"description": "Hint text for train number search field", "context": "edit_train_screen"}, "text_select_train_number": "Select Train Number", "@text_select_train_number": {"description": "Train number dropdown label", "context": "add_train_form"}, "text_monday": "Monday", "@text_monday": {"description": "Monday day name", "context": "edit_train_screen"}, "text_tuesday": "Tuesday", "@text_tuesday": {"description": "Tuesday day name", "context": "edit_train_screen"}, "text_wednesday": "Wednesday", "@text_wednesday": {"description": "Wednesday day name", "context": "edit_train_screen"}, "text_thursday": "Thursday", "@text_thursday": {"description": "Thursday day name", "context": "edit_train_screen"}, "text_friday": "Friday", "@text_friday": {"description": "Friday day name", "context": "edit_train_screen"}, "text_saturday": "Saturday", "@text_saturday": {"description": "Saturday day name", "context": "edit_train_screen"}, "text_sunday": "Sunday", "@text_sunday": {"description": "Sunday day name", "context": "edit_train_screen"}, "text_updating_data": "Updating Data...", "@text_updating_data": {"description": "Loading text when updating data", "context": "edit_train_screen"}, "text_return_gap_updated_successfully": "Return gap updated successfully", "@text_return_gap_updated_successfully": {"description": "Success message for return gap update", "context": "edit_train_screen"}, "text_train_details_updated_successfully": "Train details updated successfully", "@text_train_details_updated_successfully": {"description": "Success message for train details update", "context": "edit_train_screen"}, "text_restrictions": "Restrictions", "@text_restrictions": {"description": "Restrictions section title", "context": "edit_train_screen"}, "text_location": "Location", "@text_location": {"description": "Location restriction label", "context": "edit_train_screen"}, "text_time": "Time", "@text_time": {"description": "Time restriction label", "context": "edit_train_screen"}, "text_enable_media_upload": "Enable Media Upload", "@text_enable_media_upload": {"description": "Media upload toggle label", "context": "edit_train_screen"}, "text_failed_to_load_zones": "Failed to load zones: {error}", "@text_failed_to_load_zones": {"description": "Error message when zones fail to load", "context": "edit_train_screen", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "text_failed_to_load_divisions": "Failed to load divisions: {error}", "@text_failed_to_load_divisions": {"description": "Error message when divisions fail to load", "context": "edit_train_screen", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "text_failed_to_load_depots": "Failed to load depots: {error}", "@text_failed_to_load_depots": {"description": "Error message when depots fail to load", "context": "edit_train_screen", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "text_failed_to_load_train_details": "Failed to load train details: {error}", "@text_failed_to_load_train_details": {"description": "Error message when train details fail to load", "context": "edit_train_screen", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "text_error_loading_train_data": "Error loading train data: {error}", "@text_error_loading_train_data": {"description": "Error message when train data fails to load", "context": "edit_train_screen", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "text_edit_profile": "Edit Profile", "@text_edit_profile": {"description": "Title for edit profile screen", "context": "edit_profile_screen"}, "text_error_loading_authentication_state": "Error loading authentication state", "@text_error_loading_authentication_state": {"description": "Error message when authentication state fails to load", "context": "edit_profile_screen"}, "text_verify_email_button": "<PERSON><PERSON><PERSON>", "@text_verify_email_button": {"description": "Button text for email verification", "context": "edit_profile_form"}, "text_first_name": "First Name", "@text_first_name": {"description": "Label for first name field", "context": "edit_profile_form"}, "text_middle_name": "Middle Name", "@text_middle_name": {"description": "Label for middle name field", "context": "edit_profile_form"}, "text_last_name": "Last Name", "@text_last_name": {"description": "Label for last name field", "context": "edit_profile_form"}, "text_employee_number": "Employee Number", "@text_employee_number": {"description": "Label for employee number field", "context": "edit_profile_form"}, "text_role": "Role", "@text_role": {"description": "Label for role field", "context": "enable_disable_user"}, "text_depot": "Depot", "@text_depot": {"description": "Label for depot field", "context": "enable_disable_user"}, "text_phone_number": "Phone Number", "@text_phone_number": {"description": "Label for phone number field", "context": "edit_profile_form"}, "text_whatsapp_number": "WhatsApp Number", "@text_whatsapp_number": {"description": "Label for WhatsApp number field", "context": "edit_profile_form"}, "text_secondary_phone_number": "Secondary Phone Number", "@text_secondary_phone_number": {"description": "Label for secondary phone number field", "context": "edit_profile_form"}, "text_email": "Email", "@text_email": {"description": "Label for email field", "context": "enable_disable_user"}, "text_phone_number_must_be_10_digits": "Phone number must be 10 digits", "@text_phone_number_must_be_10_digits": {"description": "Validation message for phone number length", "context": "edit_profile_form"}, "text_phone_number_and_secondary_phone_number_must_be_different": "Phone number and secondary phone number must be different", "@text_phone_number_and_secondary_phone_number_must_be_different": {"description": "Validation message for different phone numbers", "context": "edit_profile_form"}, "text_change_email": "Change Email", "@text_change_email": {"description": "Button text for changing email", "context": "edit_profile_form"}, "text_change_password": "Change Password", "@text_change_password": {"description": "Button text for changing password", "context": "edit_profile_form"}, "text_change_mobile_number": "Change Mobile Number", "@text_change_mobile_number": {"description": "Button text for changing mobile number", "context": "edit_profile_form"}, "text_change_whatsapp_number": "Change WhatsApp Number", "@text_change_whatsapp_number": {"description": "Button text for changing WhatsApp number", "context": "edit_profile_form"}, "text_update_profile": "Update Profile", "@text_update_profile": {"description": "Button text for updating profile", "context": "edit_profile_form"}, "text_add_trains": "Add Trains", "@text_add_trains": {"description": "Button text for adding trains", "context": "edit_profile_form"}, "text_up": "Up", "@text_up": {"description": "Direction option: Up", "context": "add_train_screen"}, "text_down": "Down", "@text_down": {"description": "Direction option: Down", "context": "add_train_screen"}, "text_today": "Today", "@text_today": {"description": "Charting day option: Today", "context": "add_train_screen"}, "text_yesterday": "Yesterday", "@text_yesterday": {"description": "Charting day option: Yesterday", "context": "add_train_screen"}, "text_failed_to_load_zones_add": "Failed to load zones: {error}", "@text_failed_to_load_zones_add": {"description": "Error message when zones fail to load", "context": "add_train_screen", "placeholders": {"error": {"type": "String", "description": "Error message"}}}, "text_failed_to_load_divisions_add": "Failed to load divisions: {error}", "@text_failed_to_load_divisions_add": {"description": "Error message when divisions fail to load", "context": "add_train_screen", "placeholders": {"error": {"type": "String", "description": "Error message"}}}, "text_failed_to_load_depots_add": "Failed to load depots: {error}", "@text_failed_to_load_depots_add": {"description": "Error message when depots fail to load", "context": "add_train_screen", "placeholders": {"error": {"type": "String", "description": "Error message"}}}, "text_please_enter_train_number": "Please enter train number", "@text_please_enter_train_number": {"description": "Validation message for train number field", "context": "add_train_screen"}, "text_train_number_min_digits": "Train number must be at least 4 digits", "@text_train_number_min_digits": {"description": "Validation message for train number minimum length", "context": "add_train_screen"}, "text_please_select_return_gap": "Please select return gap", "@text_please_select_return_gap": {"description": "Validation message for return gap field", "context": "add_train_screen"}, "text_coaches_sequence": "Coaches Sequence", "@text_coaches_sequence": {"description": "Label for coaches sequence section", "context": "add_train_screen"}, "text_please_enter_train_name": "Please enter train name", "@text_please_enter_train_name": {"description": "Validation message for train name field", "context": "add_train_screen"}, "text_please_select_charting_day": "Please select charting day", "@text_please_select_charting_day": {"description": "Validation message for charting day field", "context": "add_train_screen"}, "text_please_enter_from_station": "Please enter from station", "@text_please_enter_from_station": {"description": "Validation message for from station field", "context": "add_train_screen"}, "text_please_enter_to_station": "Please enter to station", "@text_please_enter_to_station": {"description": "Validation message for to station field", "context": "add_train_screen"}, "text_please_select_direction": "Please select direction", "@text_please_select_direction": {"description": "Validation message for direction field", "context": "add_train_screen"}, "text_please_enter_start_time": "Please enter start time", "@text_please_enter_start_time": {"description": "Validation message for start time field", "context": "add_train_screen"}, "text_please_enter_end_time": "Please enter end time", "@text_please_enter_end_time": {"description": "Validation message for end time field", "context": "add_train_screen"}, "text_please_enter_charting_time": "Please enter charting time", "@text_please_enter_charting_time": {"description": "Validation message for charting time field", "context": "add_train_screen"}, "text_please_enter_train_type": "Please enter train type", "@text_please_enter_train_type": {"description": "Validation message for train type field", "context": "add_train_screen"}, "text_add_new_user": "Add New User", "@text_add_new_user": {"description": "Title for add new user screen", "context": "add_user_screen"}, "text_phone_and_secondary_phone_must_be_different": "Phone and secondary phone must be different", "@text_phone_and_secondary_phone_must_be_different": {"description": "Validation error for duplicate phone numbers", "context": "add_user_form"}, "text_validation_error": "Validation Error", "@text_validation_error": {"description": "Title for validation error dialog", "context": "add_user_form"}, "text_submitting_data_please_wait": "Submitting data, please wait...", "@text_submitting_data_please_wait": {"description": "Loading message while submitting user data", "context": "add_user_form"}, "text_please_complete_all_required_fields": "Please complete all required fields", "@text_please_complete_all_required_fields": {"description": "Error message for incomplete form", "context": "add_user_form"}, "text_form_incomplete": "Form Incomplete", "@text_form_incomplete": {"description": "Title for incomplete form dialog", "context": "add_user_form"}, "text_enter_first_name": "Enter first name", "@text_enter_first_name": {"description": "Hint text for first name field", "context": "add_user_form"}, "text_please_enter_first_name": "Please enter first name", "@text_please_enter_first_name": {"description": "Validation message for first name field", "context": "add_user_form"}, "text_middle_name_optional": "Middle Name (Optional)", "@text_middle_name_optional": {"description": "Label for optional middle name field", "context": "add_user_form"}, "text_enter_middle_name": "Enter middle name", "@text_enter_middle_name": {"description": "Hint text for middle name field", "context": "add_user_form"}, "text_enter_last_name": "Enter last name", "@text_enter_last_name": {"description": "Hint text for last name field", "context": "add_user_form"}, "text_please_enter_last_name": "Please enter last name", "@text_please_enter_last_name": {"description": "Validation message for last name field", "context": "add_user_form"}, "text_secondary_phone_number_optional": "Secondary Phone Number (Optional)", "@text_secondary_phone_number_optional": {"description": "Label for optional secondary phone field", "context": "add_user_form"}, "text_whatsapp_number_same_as_phone": "WhatsApp number same as phone", "@text_whatsapp_number_same_as_phone": {"description": "Label for WhatsApp number checkbox", "context": "add_user_form"}, "text_use_same_number_for_whatsapp": "Use same number for WhatsApp", "@text_use_same_number_for_whatsapp": {"description": "Checkbox label for using same number", "context": "add_user_form"}, "text_enter_10_digit_whatsapp_number": "Enter 10-digit WhatsApp number", "@text_enter_10_digit_whatsapp_number": {"description": "Hint text for WhatsApp number field", "context": "add_user_form"}, "text_please_enter_whatsapp_number": "Please enter WhatsApp number", "@text_please_enter_whatsapp_number": {"description": "Validation message for WhatsApp number", "context": "add_user_form"}, "text_whatsapp_number_must_be_10_digits": "WhatsApp number must be 10 digits", "@text_whatsapp_number_must_be_10_digits": {"description": "Validation message for WhatsApp number length", "context": "add_user_form"}, "text_please_enter_only_numbers": "Please enter only numbers", "@text_please_enter_only_numbers": {"description": "Validation message for numeric fields", "context": "add_user_form"}, "text_request_for_add_user": "Request for Add User", "@text_request_for_add_user": {"description": "Title for add user request", "context": "add_user_form"}, "text_information_dialog_title": "Information", "@text_information_dialog_title": {"description": "Title for information dialog", "context": "add_user_form"}, "text_please_complete_fields_in_order": "Please complete fields in order", "@text_please_complete_fields_in_order": {"description": "Message for completing fields in sequence", "context": "add_user_form"}, "text_ok": "OK", "@text_ok": {"description": "OK button text", "context": "add_train_form"}, "text_personal_information": "Personal Information", "@text_personal_information": {"description": "Section title for personal information", "context": "add_user_form"}, "text_contact_details": "Contact Details", "@text_contact_details": {"description": "Section title for contact details", "context": "add_user_form"}, "text_account_settings": "Account <PERSON><PERSON>", "@text_account_settings": {"description": "Section title for account settings", "context": "add_user_form"}, "text_authentication_check_failed": "Authentication check failed", "@text_authentication_check_failed": {"description": "Error message for authentication check failure", "context": "update_user_screen"}, "text_enter_a_valid_mobile_number": "Enter a valid mobile number", "@text_enter_a_valid_mobile_number": {"description": "Validation message for mobile number", "context": "update_user_screen"}, "text_user_data_not_found": "User data not found", "@text_user_data_not_found": {"description": "Error message when user data is not found", "context": "update_user_screen"}, "text_access_token_not_found": "Access token not found", "@text_access_token_not_found": {"description": "Error message when access token is missing", "context": "update_user_screen"}, "text_update_user": "Update User", "@text_update_user": {"description": "Title for update user screen", "context": "update_user_screen"}, "text_enter_mobile_number": "Enter mobile number", "@text_enter_mobile_number": {"description": "Label for mobile number input field", "context": "update_user_screen"}, "text_search_for_a_user_to_update_their_details": "Search for a user to update their details", "@text_search_for_a_user_to_update_their_details": {"description": "Instructions text for searching users", "context": "update_user_screen"}, "text_user_not_found_please_check_the_mobile_number_and_try_again": "User not found. Please check the mobile number and try again.", "@text_user_not_found_please_check_the_mobile_number_and_try_again": {"description": "Error message when user is not found", "context": "update_user_screen"}, "text_error_with_details": "Error: {details}", "@text_error_with_details": {"description": "Generic error message with details placeholder", "context": "train_details_screen", "placeholders": {"details": {"type": "String", "description": "Error details"}}}, "text_change_mobile": "Change Mobile", "@text_change_mobile": {"description": "Title for change mobile screen", "context": "change_mobile_screen"}, "text_change_whatsapp": "Change Whatsapp Number", "@text_change_whatsapp": {"description": "Title for change whatsapp screen", "context": "change_whatsapp_screen"}, "text_change_your_email": "Change Your Email", "@text_change_your_email": {"description": "Change email form title", "context": "change_email_form"}, "text_current_email": "Current Email", "@text_current_email": {"description": "Current email field label", "context": "change_email_form"}, "text_new_email": "New Email", "@text_new_email": {"description": "New email field label", "context": "change_email_form"}, "text_please_enter_new_email": "Please enter a new email", "@text_please_enter_new_email": {"description": "Validation message for new email field", "context": "change_email_form"}, "text_otp": "OTP", "@text_otp": {"description": "OTP field label", "context": "profile_forms"}, "text_resend_in_seconds": "Resend in {seconds} s", "@text_resend_in_seconds": {"description": "Resend OTP countdown message", "context": "change_email_modal", "placeholders": {"seconds": {"type": "int", "example": "30"}}}, "text_failed_to_send_otp": "Failed to send OTP: {error}", "@text_failed_to_send_otp": {"description": "Error message when OTP sending fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "text_failed_to_verify_otp": "Failed to verify OTP: {error}", "@text_failed_to_verify_otp": {"description": "Error message when OTP verification fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "example": "Invalid OTP"}}}, "text_please_enter_your_email": "Please Enter Your Email", "@text_please_enter_your_email": {"description": "Modal title for email entry", "context": "change_email_modal"}, "text_please_enter_valid_email": "Please enter a valid email", "@text_please_enter_valid_email": {"description": "Validation message for email field", "context": "change_email_modal"}, "text_invalid_email_format": "Invalid email format", "@text_invalid_email_format": {"description": "Validation message for invalid email format", "context": "change_email_modal"}, "text_change_your_password": "Change Your Password", "@text_change_your_password": {"description": "Change password form title", "context": "change_password_form"}, "text_old_password": "Old Password", "@text_old_password": {"description": "Old password field label", "context": "change_password_form"}, "text_new_password": "New Password", "@text_new_password": {"description": "New password field label", "context": "change_password_form"}, "text_confirm_new_password": "Confirm New Password", "@text_confirm_new_password": {"description": "Confirm new password field label", "context": "change_password_form"}, "text_please_enter_otp": "Please enter the OTP", "@text_please_enter_otp": {"description": "Validation message for OTP field", "context": "change_mobile_form"}, "text_please_enter_value": "Please enter a value", "@text_please_enter_value": {"description": "Generic validation message", "context": "change_password_form"}, "text_please_enter_valid_mobile": "Please enter a valid mobile number", "@text_please_enter_valid_mobile": {"description": "Validation message for mobile number field", "context": "profile_forms"}, "text_change_your_mobile_number": "Change Your Mobile Number", "@text_change_your_mobile_number": {"description": "Title for change mobile form", "context": "change_mobile_form"}, "text_current_mobile_number": "Current Mobile Number", "@text_current_mobile_number": {"description": "Label for current mobile number field", "context": "change_mobile_form"}, "text_new_mobile_number": "New Mobile Number", "@text_new_mobile_number": {"description": "Label for new mobile number field", "context": "change_mobile_form"}, "text_please_enter_new_mobile": "Please enter a new mobile number", "@text_please_enter_new_mobile": {"description": "Validation message for new mobile number field", "context": "change_mobile_form"}, "text_change_your_whatsapp_number": "Change Your Whatsapp Number", "@text_change_your_whatsapp_number": {"description": "Change whatsapp form title", "context": "change_whatsapp_form"}, "text_current_whatsapp_number": "Current Whatsapp Number", "@text_current_whatsapp_number": {"description": "Current whatsapp number field label", "context": "change_whatsapp_form"}, "text_new_whatsapp_number": "New Whatsapp Number", "@text_new_whatsapp_number": {"description": "New whatsapp number field label", "context": "change_whatsapp_form"}, "text_please_enter_new_whatsapp": "Please enter a new whatsapp mobile number", "@text_please_enter_new_whatsapp": {"description": "Validation message for new whatsapp number field", "context": "change_whatsapp_form"}, "text_please_enter_valid_mobile_number": "Please enter a valid mobile number", "@text_please_enter_valid_mobile_number": {"description": "Validation message for mobile number", "context": "change_mobile_form"}, "text_please_enter_new_mobile_number": "Please enter a new mobile number", "@text_please_enter_new_mobile_number": {"description": "Validation message for new mobile number", "context": "change_mobile_form"}, "text_train": "Train", "@text_train": {"description": "Table column header for train", "context": "add_train_screen"}, "text_coaches": "Coaches", "@text_coaches": {"description": "Table column header for coaches", "context": "add_train_screen"}, "text_origin_date": "Origin Date", "@text_origin_date": {"description": "Table column header for origin date", "context": "add_train_screen"}, "text_na": "NA", "@text_na": {"description": "Not available abbreviation", "context": "passenger_feedback_screen"}, "text_do_you_want_to_logout": "Do you want to logout now?", "@text_do_you_want_to_logout": {"description": "Logout confirmation message", "context": "edit_profile_form"}, "text_yes": "Yes", "@text_yes": {"description": "Yes button text", "context": "edit_profile_form"}, "text_no": "No", "@text_no": {"description": "No button text", "context": "edit_profile_form"}, "text_inside_train": "Inside Train", "@text_inside_train": {"description": "Inside train toggle label", "context": "edit_profile_form"}, "text_need_alarm": "Need Alarm", "@text_need_alarm": {"description": "Need alarm toggle label", "context": "edit_profile_form"}, "text_deactivate": "Deactivate", "@text_deactivate": {"description": "Deactivate button text", "context": "edit_profile_form"}, "text_logout": "Logout", "@text_logout": {"description": "Logout button text", "context": "edit_profile_form"}, "text_email_otp_error": "Email OTP Error: {error}", "@text_email_otp_error": {"description": "Email OTP verification error message", "context": "edit_profile_form", "placeholders": {"error": {"type": "String", "example": "Invalid OTP"}}}, "text_phone_otp_error": "Phone OTP Error: {error}", "@text_phone_otp_error": {"description": "Phone OTP verification error message", "context": "edit_profile_form", "placeholders": {"error": {"type": "String", "example": "Invalid OTP"}}}, "text_please_select_a_train_number": "Please select a train number", "@text_please_select_a_train_number": {"description": "Validation message for train number selection", "context": "add_train_form"}, "text_add_update": "Add/Update", "@text_add_update": {"description": "Add/Update button text", "context": "add_train_form"}, "text_an_error_occurred_while_adding": "An error occurred while adding train details: {error}", "@text_an_error_occurred_while_adding": {"description": "Error message when adding train details fails", "context": "add_train_form", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "text_select_coaches_optional": "Select Coaches (optional)", "@text_select_coaches_optional": {"description": "Coach selection hint text", "context": "add_train_form"}, "text_select_coaches_from_list": "Select coaches from the list", "@text_select_coaches_from_list": {"description": "Coach selection header text", "context": "add_train_form"}, "text_please_select_date": "Please select a date", "@text_please_select_date": {"description": "Date validation message", "context": "add_train_form"}, "text_need_valid_email_before_deactivation": "You need valid email before deactivation.", "@text_need_valid_email_before_deactivation": {"description": "Message when user needs email for deactivation", "context": "edit_profile_form"}, "text_send_otps_for_verification": "We will send OTPs to your email and phone for verification.", "@text_send_otps_for_verification": {"description": "Deactivation verification process message", "context": "edit_profile_form"}, "text_failed_to_send_otps": "Failed to send OTPs: {error}", "@text_failed_to_send_otps": {"description": "Error message when sending OTPs fails", "context": "edit_profile_form", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "text_enter_email_otp": "Enter Email OTP", "@text_enter_email_otp": {"description": "Email OTP input label", "context": "edit_profile_form"}, "text_enter_phone_otp": "Enter Phone OTP", "@text_enter_phone_otp": {"description": "Phone OTP input label", "context": "edit_profile_form"}, "text_please_enter_field": "Please enter {field}", "@text_please_enter_field": {"description": "Generic field validation message", "context": "edit_profile_form", "placeholders": {"field": {"type": "String", "example": "first name"}}}, "text_enable_fingerprint_login": "Enable Fingerprint Login", "@text_enable_fingerprint_login": {"description": "Fingerprint login toggle label", "context": "edit_profile_form"}, "text_deactivate_account": "Deactivate Account", "@text_deactivate_account": {"description": "Deactivate account button text", "context": "edit_profile_form"}, "rm_feedback_app_bar_title": "Railmadad Passenger Feedback", "@rm_feedback_app_bar_title": {"description": "App bar title for RM feedback screen", "context": "rm_feedback_screen"}, "rm_feedback_main_title": "Railmadad <PERSON>", "@rm_feedback_main_title": {"description": "Main title for RM feedback form", "context": "rm_feedback_screen"}, "form_resolved_status": "Resolved (Yes/No) *", "@form_resolved_status": {"description": "Resolved status dropdown label", "context": "rm_feedback_screen"}, "form_marks": "Marks (1 to 10) *", "@form_marks": {"description": "Marks dropdown label", "context": "rm_feedback_screen"}, "btn_validate": "Validate", "@btn_validate": {"description": "Button text for validation", "context": "rail_sathi_write_complaint"}, "btn_verified": "Verified", "@btn_verified": {"description": "Verified status text", "context": "rm_feedback_screen"}, "btn_verify_email": "<PERSON><PERSON><PERSON>", "@btn_verify_email": {"description": "Verify email button text", "context": "normal_review_feedback_dialogue"}, "btn_verify_otp": "Verify OTP", "@btn_verify_otp": {"description": "Verify OTP button text", "context": "normal_review_feedback_dialogue"}, "btn_submit_feedback": "Submit <PERSON>", "@btn_submit_feedback": {"description": "Submit feedback button text", "context": "rm_feedback_screen"}, "btn_upload_pnr_image": "Upload PNR Image", "@btn_upload_pnr_image": {"description": "Upload PNR image button text", "context": "rm_feedback_screen"}, "btn_pick_media": "Pick Images/Videos for Feedback", "@btn_pick_media": {"description": "Pick media button text", "context": "rm_feedback_screen"}, "btn_camera": "Camera", "@btn_camera": {"description": "Camera option in image picker dialog", "context": "image_upload"}, "btn_gallery": "Gallery", "@btn_gallery": {"description": "Gallery option in image picker dialog", "context": "image_upload"}, "btn_image": "Image", "@btn_image": {"description": "Image option in media type dialog", "context": "coach_handover_image_upload"}, "btn_video": "Video", "@btn_video": {"description": "Video option in media type dialog", "context": "coach_handover_image_upload"}, "btn_i_understand": "I Understand", "@btn_i_understand": {"description": "I understand button text", "context": "normal_review_feedback_dialogue"}, "btn_ok": "OK", "@btn_ok": {"description": "OK button text", "context": "assign_obhs_screen"}, "status_verified": "verified", "@status_verified": {"description": "Verified status text", "context": "rm_review_feedback_dialogue"}, "status_pending": "Pending", "@status_pending": {"description": "Pending status text", "context": "normal_review_feedback_dialogue"}, "status_completed": "Completed", "@status_completed": {"description": "Completed status text", "context": "normal_review_feedback_dialogue"}, "text_email_verification_optional": "Email Verification (Optional)", "@text_email_verification_optional": {"description": "Email verification section title", "context": "rm_feedback_screen"}, "text_verified": "verified", "@text_verified": {"description": "Verified status text", "context": "normal_review_feedback_dialogue"}, "text_email_verification_info": "Email Verification Info", "@text_email_verification_info": {"description": "Email verification info dialog title", "context": "normal_review_feedback_dialogue"}, "text_otp_sent_message": "OTP sent to your email. Please check both inbox and spam folders.", "@text_otp_sent_message": {"description": "OTP sent notification message", "context": "rm_feedback_screen"}, "validation_issue_type_required": "Please select an issue type", "@validation_issue_type_required": {"description": "Validation message for issue type requirement", "context": "rm_feedback_screen"}, "error_berth_number_invalid": "Berth number must be a valid number", "@error_berth_number_invalid": {"description": "Berth number validation error", "context": "rm_feedback_screen"}, "error_form_validation_failed": "Form validation failed", "@error_form_validation_failed": {"description": "Form validation error message", "context": "rm_feedback_screen"}, "error_max_videos": "You can only select up to 3 videos", "@error_max_videos": {"description": "Maximum videos limit error", "context": "rm_feedback_screen"}, "error_max_videos_short": "Maximum 3 videos allowed", "@error_max_videos_short": {"description": "Maximum videos limit short error", "context": "rm_feedback_screen"}, "error_max_feedback_images": "You can only select up to 3 feedback images", "@error_max_feedback_images": {"description": "Maximum feedback images limit error", "context": "rm_feedback_screen"}, "error_max_feedback_images_short": "Maximum 3 feedback images allowed", "@error_max_feedback_images_short": {"description": "Maximum feedback images limit short error", "context": "rm_feedback_screen"}, "error_max_images_reached": "Only added {count} images. Maximum limit of 3 reached.", "@error_max_images_reached": {"description": "Maximum images reached with count", "context": "rm_feedback_screen", "placeholders": {"count": {"type": "int"}}}, "error_picking_media": "Error picking media: {error}", "@error_picking_media": {"description": "Error message for media picking failure", "context": "passenger_feedback_screen", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "error_email_required": "Please enter a valid Email ID.", "@error_email_required": {"description": "Email required validation message", "context": "rm_feedback_screen"}, "msg_email_verification_initiated": "Email verification initiated. Please check your inbox.", "@msg_email_verification_initiated": {"description": "Success message for email verification initiation", "context": "normal_review_feedback_dialogue"}, "error_otp_required": "Please enter the OTP.", "@error_otp_required": {"description": "OTP required validation message", "context": "rm_feedback_screen"}, "msg_otp_verified": "OTP verified successfully.", "@msg_otp_verified": {"description": "Success message for OTP verification", "context": "rm_feedback_screen"}, "error_something_wrong": "Something went wrong", "@error_something_wrong": {"description": "Generic error message", "context": "passenger_feedback_screen"}, "status_yes": "Yes", "@status_yes": {"description": "Yes option", "context": "rm_review_feedback_dialogue"}, "text_daily": "Daily", "@text_daily": {"description": "Daily frequency text", "context": "passenger_feedback_screen"}, "text_yes_lowercase": "yes", "@text_yes_lowercase": {"description": "Yes in lowercase", "context": "passenger_feedback_screen"}, "text_no_lowercase": "no", "@text_no_lowercase": {"description": "No in lowercase", "context": "passenger_feedback_screen"}, "error_berth_number_invalid_passenger": "Berth number must be a valid number", "@error_berth_number_invalid_passenger": {"description": "Berth number validation error for passenger feedback", "context": "passenger_feedback_screen"}, "error_no_train_selected": "No train number selected", "@error_no_train_selected": {"description": "Error when no train is selected", "context": "passenger_feedback_screen"}, "error_fetching_charting_time": "Error fetching charting time: {error}", "@error_fetching_charting_time": {"description": "Error message for charting time fetch failure", "context": "passenger_feedback_screen", "placeholders": {"error": {"type": "String"}}}, "dialog_train_not_running_content": "Train *{trainNumber}* is *NOT* running on *{dayOfWeek}*\nRunning Days: {runningDays}", "@dialog_train_not_running_content": {"description": "Train not running dialog content", "context": "passenger_feedback_screen", "placeholders": {"trainNumber": {"type": "String"}, "dayOfWeek": {"type": "String"}, "runningDays": {"type": "String"}}}, "error_validating_pnr": "Error validating PNR: {error}", "@error_validating_pnr": {"description": "PNR validation error message", "context": "passenger_feedback_screen", "placeholders": {"error": {"type": "String"}}}, "error_feedback_submission_passenger": "Error in feedback submission: {error}", "@error_feedback_submission_passenger": {"description": "Feedback submission error message", "context": "passenger_feedback_screen", "placeholders": {"error": {"type": "String"}}}, "error_max_images_reached_passenger": "Only added {count} images. Maximum limit of 3 reached.", "@error_max_images_reached_passenger": {"description": "Maximum images reached error for passenger feedback", "context": "passenger_feedback_screen", "placeholders": {"count": {"type": "int"}}}, "error_picking_media_passenger": "Error picking media: {error}", "@error_picking_media_passenger": {"description": "Media picking error for passenger feedback", "context": "passenger_feedback_screen", "placeholders": {"error": {"type": "String"}}}, "error_generic_passenger": "Error: {error}", "@error_generic_passenger": {"description": "Generic error message for passenger feedback", "context": "passenger_feedback_screen", "placeholders": {"error": {"type": "String"}}}, "error_something_wrong_passenger": "Something went wrong", "@error_something_wrong_passenger": {"description": "Generic something went wrong message", "context": "passenger_feedback_screen"}, "text_check_inbox": "Check your inbox first", "@text_check_inbox": {"description": "Email verification instruction", "context": "passenger_feedback_screen"}, "text_check_spam": "If not found, check spam/junk folder", "@text_check_spam": {"description": "Email verification spam folder instruction", "context": "passenger_feedback_screen"}, "text_add_safe_sender": "• Add our domain to your safe sender list", "@text_add_safe_sender": {"description": "Add safe sender instruction", "context": "normal_review_feedback_dialogue"}, "status_no": "No", "@status_no": {"description": "No option", "context": "rm_review_feedback_dialogue"}, "status_select": "Select", "@status_select": {"description": "Select placeholder text", "context": "rm_review_feedback_dialogue"}, "section_email_verification": "Email Verification (Optional)", "@section_email_verification": {"description": "Email verification section header", "context": "rm_feedback_screen"}, "section_selected_images": "Selected Images:", "@section_selected_images": {"description": "Selected images section header", "context": "rm_feedback_screen"}, "section_selected_videos": "Selected Videos:", "@section_selected_videos": {"description": "Selected videos section header", "context": "rm_feedback_screen"}, "dialog_email_verification_info": "Email Verification Info", "@dialog_email_verification_info": {"description": "Email verification info dialog title", "context": "rm_feedback_screen"}, "dialog_select_media_type": "Select Media Type", "@dialog_select_media_type": {"description": "Select media type dialog title", "context": "rm_feedback_screen"}, "validation_fill_all_fields": "Please fill all fields with valid information.", "@validation_fill_all_fields": {"description": "Validation message for empty fields", "context": "rm_feedback_screen"}, "validation_pnr_digits": "Pnr number should be 8 or 10 digits", "@validation_pnr_digits": {"description": "Validation message for PNR number format", "context": "rm_feedback_screen"}, "validation_berth_number": "Berth number must be a valid number", "@validation_berth_number": {"description": "Validation message for berth number", "context": "rm_feedback_screen"}, "validation_feedback_length": "Feedback cannot exceed 100 characters", "@validation_feedback_length": {"description": "Validation message for feedback length", "context": "rm_feedback_screen"}, "validation_email_required": "Please enter a valid Email ID.", "@validation_email_required": {"description": "Validation message for email requirement", "context": "rm_feedback_screen"}, "validation_otp_required": "Please enter the OTP.", "@validation_otp_required": {"description": "Validation message for OTP requirement", "context": "rm_feedback_screen"}, "validation_train_no_required": "Train No is required", "@validation_train_no_required": {"description": "Validation message for train number requirement", "context": "rm_feedback_screen"}, "validation_train_name_required": "Train Name is required", "@validation_train_name_required": {"description": "Validation message for train name requirement", "context": "rm_feedback_screen"}, "validation_passenger_name_required": "Passenger Name is required", "@validation_passenger_name_required": {"description": "Validation message for passenger name requirement", "context": "rm_feedback_screen"}, "validation_mobile_required": "Mobile Number is required", "@validation_mobile_required": {"description": "Validation message for mobile number requirement", "context": "rm_feedback_screen"}, "validation_mobile_digits": "Mobile Number must be 10 digits", "@validation_mobile_digits": {"description": "Validation message for mobile number format", "context": "rm_feedback_screen"}, "validation_sub_issue_required": "Please select a sub-issue type", "@validation_sub_issue_required": {"description": "Validation message for sub-issue type requirement", "context": "rm_feedback_screen"}, "validation_resolved_required": "Please select resolved status", "@validation_resolved_required": {"description": "Validation message for resolved status requirement", "context": "rm_feedback_screen"}, "validation_marks_required": "Please select marks", "@validation_marks_required": {"description": "Validation message for marks requirement", "context": "rm_feedback_screen"}, "msg_pnr_images_limit": "You can only select up to 3 PNR images", "@msg_pnr_images_limit": {"description": "Message for PNR images limit", "context": "rm_feedback_screen"}, "msg_feedback_images_limit": "Maximum 3 feedback images allowed", "@msg_feedback_images_limit": {"description": "Message for feedback images limit", "context": "rm_feedback_screen"}, "msg_images_added_limit": "Only added {count} images. Maximum limit of 3 reached.", "@msg_images_added_limit": {"description": "Message for images added with limit", "context": "rm_feedback_screen", "placeholders": {"count": {"type": "int", "example": "2"}}}, "msg_error_picking_media": "Error picking media: {error}", "@msg_error_picking_media": {"description": "Error message for media picking", "context": "rm_feedback_screen", "placeholders": {"error": {"type": "String", "example": "Permission denied"}}}, "msg_failed_fetch_train_name": "Failed to fetch train name", "@msg_failed_fetch_train_name": {"description": "Error message for train name fetch failure", "context": "rm_feedback_screen"}, "msg_invalid_pnr": "Invalid PNR number.", "@msg_invalid_pnr": {"description": "Error message for invalid PNR", "context": "rm_feedback_screen"}, "msg_pnr_success": "PNR details fetched successfully.", "@msg_pnr_success": {"description": "Success message for PNR fetch", "context": "rm_feedback_screen"}, "msg_pnr_validation_failed": "PNR validation failed", "@msg_pnr_validation_failed": {"description": "Message when PNR validation fails", "context": "rail_sathi_write_complaint"}, "msg_email_verification_sent": "Email verification initiated. Please check both your inbox and spam folders..", "@msg_email_verification_sent": {"description": "Message for email verification sent", "context": "rm_feedback_screen"}, "msg_feedback_submitted": "<PERSON><PERSON><PERSON> submitted successfully!", "@msg_feedback_submitted": {"description": "Success message for feedback submission", "context": "rm_feedback_screen"}, "msg_feedback_failed": "Failed to submit feedback", "@msg_feedback_failed": {"description": "Error message for feedback submission failure", "context": "rm_feedback_screen"}, "msg_unexpected_error": "An unexpected error occurred. Please try again.", "@msg_unexpected_error": {"description": "Generic error message", "context": "rm_feedback_screen"}, "info_spam_folder_note": "Please note that verification emails may sometimes be delivered to your spam/junk folder.", "@info_spam_folder_note": {"description": "Info about spam folder for emails", "context": "rm_feedback_screen"}, "info_after_requesting_otp": "After requesting OTP:", "@info_after_requesting_otp": {"description": "Info header for OTP instructions", "context": "rm_feedback_screen"}, "info_check_inbox": "Check your inbox first", "@info_check_inbox": {"description": "Instruction to check inbox", "context": "rm_feedback_screen"}, "info_check_spam": "If not found, check spam/junk folder", "@info_check_spam": {"description": "Instruction to check spam folder", "context": "rm_feedback_screen"}, "info_add_safe_sender": "Add our domain to your safe sender list", "@info_add_safe_sender": {"description": "Instruction to add to safe sender list", "context": "rm_feedback_screen"}, "text_no_feedback_images": "No feedback images selected", "@text_no_feedback_images": {"description": "Text when no feedback images selected", "context": "rm_feedback_screen"}, "text_no_pnr_images": "No PNR images selected", "@text_no_pnr_images": {"description": "Text when no PNR images selected", "context": "rm_feedback_screen"}, "text_character_count": "{count}/100 characters", "@text_character_count": {"description": "Character count display", "context": "normal_review_feedback_dialogue", "placeholders": {"count": {"type": "int", "description": "Current character count"}}}, "loading_sending_otp": "sending otp", "@loading_sending_otp": {"description": "Loading message for sending OTP", "context": "rm_review_feedback_dialogue"}, "loading_verifying_otp": "verifying otp", "@loading_verifying_otp": {"description": "Loading message for verifying OTP", "context": "rm_review_feedback_dialogue"}, "loading_submitting_feedback": "submitting <PERSON><PERSON><PERSON>", "@loading_submitting_feedback": {"description": "Loading message for feedback submission", "context": "passenger_feedback_screen"}, "msg_otp_sent_check_folders": "OTP sent to your email. Please check both inbox and spam folders.", "@msg_otp_sent_check_folders": {"description": "Information message about OTP email delivery", "context": "normal_review_feedback_dialogue"}, "msg_error_generic": "Error: {error}", "@msg_error_generic": {"description": "Generic error message with error details", "context": "rm_feedback_screen", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "msg_something_went_wrong": "Something went wrong", "@msg_something_went_wrong": {"description": "Generic error message", "context": "normal_review_feedback_dialogue"}, "status_yes_lowercase": "yes", "@status_yes_lowercase": {"description": "Yes status in lowercase", "context": "rm_feedback_screen"}, "status_no_lowercase": "no", "@status_no_lowercase": {"description": "No status in lowercase", "context": "rm_feedback_screen"}, "passenger_feedback_app_bar_title": "Passenger Feedback", "@passenger_feedback_app_bar_title": {"description": "App bar title for passenger feedback screen", "context": "passenger_feedback_screen"}, "passenger_feedback_main_title": "Passenger Feedback", "@passenger_feedback_main_title": {"description": "Main title for passenger feedback form", "context": "passenger_feedback_screen"}, "form_train_name_readonly": "Train Name", "@form_train_name_readonly": {"description": "Train name form field label (readonly)", "context": "passenger_feedback_screen"}, "hint_search_train_number": "Search train number", "@hint_search_train_number": {"description": "Hint text for train number search", "context": "passenger_feedback_screen"}, "category_ac": "AC", "@category_ac": {"description": "AC category label", "context": "passenger_feedback_screen"}, "category_nonac": "NONAC", "@category_nonac": {"description": "NONAC category label", "context": "passenger_feedback_screen"}, "category_tt": "TT", "@category_tt": {"description": "TT category label", "context": "passenger_feedback_screen"}, "category_rm": "RM", "@category_rm": {"description": "RM category label", "context": "passenger_feedback_screen"}, "rating_excellent": "Excellent", "@rating_excellent": {"description": "Excellent rating label", "context": "passenger_feedback_screen"}, "rating_very_good": "Very Good", "@rating_very_good": {"description": "Very Good rating label", "context": "passenger_feedback_screen"}, "rating_good": "Good", "@rating_good": {"description": "Good rating label", "context": "passenger_feedback_screen"}, "rating_average": "Average", "@rating_average": {"description": "Average rating label", "context": "passenger_feedback_screen"}, "rating_poor": "Poor", "@rating_poor": {"description": "Poor rating label", "context": "passenger_feedback_screen"}, "feedback_table_instruction": "Please tick (✓) in appropriate column", "@feedback_table_instruction": {"description": "Instruction text for feedback table", "context": "passenger_feedback_screen"}, "feedback_table_item_header": "ITEM", "@feedback_table_item_header": {"description": "Header for feedback table item column", "context": "passenger_feedback_screen"}, "dialog_train_not_running": "Train Not Running", "@dialog_train_not_running": {"description": "Dialog title for train not running", "context": "passenger_feedback_screen"}, "msg_train_not_running_details": "Train *{trainNumber}* is *NOT* running on *{dayOfWeek}*\nRunning Days: {runningDays}", "@msg_train_not_running_details": {"description": "Message for train not running with details", "context": "passenger_feedback_screen", "placeholders": {"trainNumber": {"type": "String", "example": "12345"}, "dayOfWeek": {"type": "String", "example": "Monday"}, "runningDays": {"type": "String", "example": "Mon, Wed, Fri"}}}, "msg_running_days_daily": "Daily", "@msg_running_days_daily": {"description": "Text for daily running trains", "context": "passenger_feedback_screen"}, "msg_running_days_na": "N/A", "@msg_running_days_na": {"description": "Text for unavailable running days", "context": "passenger_feedback_screen"}, "validation_provide_rating": "Please provide rating for at least one feedback item", "@validation_provide_rating": {"description": "Validation message for missing ratings", "context": "passenger_feedback_screen"}, "msg_rm_fields_required": "Please fill all required fields before proceeding to RM feedback.", "@msg_rm_fields_required": {"description": "Message for RM fields requirement", "context": "passenger_feedback_screen"}, "msg_upload_complete_wait": "Please wait until the upload is complete", "@msg_upload_complete_wait": {"description": "Message to wait for upload completion", "context": "passenger_feedback_screen"}, "msg_videos_limit": "You can only select up to 3 videos", "@msg_videos_limit": {"description": "Message for video selection limit", "context": "passenger_feedback_screen"}, "msg_videos_limit_reached": "Maximum 3 videos allowed", "@msg_videos_limit_reached": {"description": "Message for video limit reached", "context": "passenger_feedback_screen"}, "feedback_ac_item_1": "Cleaning of toilets (including tollet floor, commode pan, wall panels, shelf, miror, wash basin. Disinfectionnand provision of deodorant etc", "@feedback_ac_item_1": {"description": "AC feedback item 1", "context": "passenger_feedback_screen"}, "feedback_ac_item_2": "Cleaning of Passenger Compartment (including cleaning or passenger aisle, Vestibule area,, Doorway area and doorway wash basin, spraying of air freshene and cleaning of dust bin)", "@feedback_ac_item_2": {"description": "AC feedback item 2", "context": "passenger_feedback_screen"}, "feedback_ac_item_3": "Collection of gargage from the coach compartments and clearance of dustbins.", "@feedback_ac_item_3": {"description": "AC feedback item 3", "context": "passenger_feedback_screen"}, "feedback_ac_item_4": "Spraying of Mosquito/Cockroach/Fly Repellent and Providing Glue Board Whenever required or on demand by passengers", "@feedback_ac_item_4": {"description": "AC feedback item 4", "context": "passenger_feedback_screen"}, "feedback_ac_item_5": "Behavior/Response of Janitors / Supervisor (Including hygiene & cleanliness of Janitor/", "@feedback_ac_item_5": {"description": "AC feedback item 5", "context": "passenger_feedback_screen"}, "feedback_nonac_item_1": "Cleaning of toilets, Wash Basin and other fittings (including Disinfection and provision of deodorant etc.", "@feedback_nonac_item_1": {"description": "NONAC feedback item 1", "context": "passenger_feedback_screen"}, "feedback_nonac_item_2": "Complete Cleaning of Passenger Compartment (Including spraying of air freshener and cleaning of dust bin", "@feedback_nonac_item_2": {"description": "NONAC feedback item 2", "context": "passenger_feedback_screen"}, "feedback_nonac_item_3": "Behavior of Janitors / Supervisor (Including hygiene & cleanliness of", "@feedback_nonac_item_3": {"description": "NONAC feedback item 3", "context": "passenger_feedback_screen"}, "button_ok": "OK", "@button_ok": {"description": "OK button text", "context": "passenger_feedback_screen"}, "error_fetch_train_name": "Failed to fetch train name", "@error_fetch_train_name": {"description": "Error message when train name fetch fails", "context": "passenger_feedback_screen"}, "error_invalid_pnr": "Invalid PNR number.", "@error_invalid_pnr": {"description": "Error message for invalid PNR", "context": "passenger_feedback_screen"}, "success_pnr_fetched": "PNR details fetched successfully.", "@success_pnr_fetched": {"description": "Success message for PNR fetch", "context": "passenger_feedback_screen"}, "error_pnr_validation": "Failed to validate PNR Details\n\n Invalid PNR Number", "@error_pnr_validation": {"description": "Error message for PNR validation failure", "context": "passenger_feedback_screen"}, "success_feedback_submitted": "<PERSON><PERSON><PERSON> submitted successfully!", "@success_feedback_submitted": {"description": "Success message for feedback submission", "context": "passenger_feedback_screen"}, "error_feedback_submission": "Failed to submit feedback", "@error_feedback_submission": {"description": "Error message for feedback submission failure", "context": "passenger_feedback_screen"}, "error_unexpected": "An unexpected error occurred. Please try again.", "@error_unexpected": {"description": "Generic unexpected error message", "context": "passenger_feedback_screen"}, "email_verification_info_title": "Email Verification Info", "@email_verification_info_title": {"description": "Title for email verification info dialog", "context": "passenger_feedback_screen"}, "email_verification_spam_notice": "Please note that verification emails may sometimes be delivered to your spam/junk folder.", "@email_verification_spam_notice": {"description": "Notice about emails in spam folder", "context": "passenger_feedback_screen"}, "email_verification_after_otp": "After requesting OTP:", "@email_verification_after_otp": {"description": "Instructions after OTP request", "context": "passenger_feedback_screen"}, "email_verification_understand": "I Understand", "@email_verification_understand": {"description": "Button text for understanding email verification", "context": "passenger_feedback_screen"}, "success_email_verification": "Email verification initiated. Please check both your inbox and spam folders..", "@success_email_verification": {"description": "Success message for email verification initiation", "context": "passenger_feedback_screen"}, "error_prefix": "Error: {error}", "@error_prefix": {"description": "Error message with prefix", "context": "passenger_feedback_screen", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "success_otp_verified": "OTP verified successfully.", "@success_otp_verified": {"description": "Success message for OTP verification", "context": "passenger_feedback_screen"}, "upload_pnr_image": "Upload PNR Image", "@upload_pnr_image": {"description": "Button text for uploading PNR image", "context": "passenger_feedback_screen"}, "selected_images": "Selected Images:", "@selected_images": {"description": "Label for selected images section", "context": "passenger_feedback_screen"}, "no_pnr_images_selected": "No PNR images selected", "@no_pnr_images_selected": {"description": "Message when no PNR images are selected", "context": "passenger_feedback_screen"}, "upload_limit_pnr_3": "You can only select up to 3 PNR images", "@upload_limit_pnr_3": {"description": "Error message for PNR image upload limit", "context": "passenger_feedback_screen"}, "media_source_camera": "Camera", "@media_source_camera": {"description": "Camera option in media source dialog", "context": "passenger_feedback_screen"}, "media_source_gallery": "Gallery", "@media_source_gallery": {"description": "Gallery option in media source dialog", "context": "passenger_feedback_screen"}, "upload_limit_pnr_max": "Maximum 3 PNR images allowed", "@upload_limit_pnr_max": {"description": "Error message for maximum PNR images", "context": "passenger_feedback_screen"}, "upload_limit_reached_message": "Only added {count} images. Maximum limit of 3 reached.", "@upload_limit_reached_message": {"description": "Message when upload limit is reached", "context": "passenger_feedback_screen", "placeholders": {"count": {"type": "int", "description": "Number of images added"}}}, "error_picking_media_simple": "Error picking media: {error}", "@error_picking_media_simple": {"description": "Simple error message for media picking failure", "context": "passenger_feedback_screen", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "pick_images_videos_feedback": "Pick Images/Videos for Feedback", "@pick_images_videos_feedback": {"description": "Button text for picking feedback media", "context": "passenger_feedback_screen"}, "no_feedback_images_selected": "No feedback images selected", "@no_feedback_images_selected": {"description": "Message when no feedback images are selected", "context": "passenger_feedback_screen"}, "selected_videos": "Selected Videos:", "@selected_videos": {"description": "Label for selected videos section", "context": "passenger_feedback_screen"}, "select_media_type": "Select Media Type", "@select_media_type": {"description": "Title for media type selection dialog", "context": "passenger_feedback_screen"}, "media_type_image": "Image", "@media_type_image": {"description": "Image option in media type dialog", "context": "passenger_feedback_screen"}, "media_type_video": "Video", "@media_type_video": {"description": "Video option in media type dialog", "context": "passenger_feedback_screen"}, "upload_limit_videos_3": "You can only select up to 3 videos", "@upload_limit_videos_3": {"description": "Error message for video upload limit", "context": "passenger_feedback_screen"}, "upload_limit_videos_max": "Maximum 3 videos allowed", "@upload_limit_videos_max": {"description": "Error message for maximum videos", "context": "passenger_feedback_screen"}, "upload_limit_feedback_images_3": "You can only select up to 3 feedback images", "@upload_limit_feedback_images_3": {"description": "Error message for feedback image upload limit", "context": "passenger_feedback_screen"}, "upload_limit_feedback_images_max": "Maximum 3 feedback images allowed", "@upload_limit_feedback_images_max": {"description": "Error message for maximum feedback images", "context": "passenger_feedback_screen"}, "text_pnr_document": "PNR Document:", "@text_pnr_document": {"description": "PNR document label", "context": "normal_review_feedback_dialogue"}, "text_feedback_media": "Feedback Media:", "@text_feedback_media": {"description": "Feedback media label", "context": "normal_review_feedback_dialogue"}, "loading_deleting_feedback": "Deleting feedback...", "@loading_deleting_feedback": {"description": "Loading message for deleting feedback", "context": "review_feedback"}, "success_feedback_deleted": "Feedback deleted successfully", "@success_feedback_deleted": {"description": "Success message for feedback deletion", "context": "review_feedback"}, "error_deleting_feedback": "Error deleting feedback", "@error_deleting_feedback": {"description": "Error message for feedback deletion", "context": "review_feedback"}, "text_train_no": "Train No: {trainNumber}", "@text_train_no": {"description": "Display text for train number", "context": "view_complaints", "placeholders": {"trainNumber": {"type": "String"}}}, "text_not_verified": "Not Verified", "@text_not_verified": {"description": "Text for not verified status", "context": "review_feedback"}, "btn_review": "Review", "@btn_review": {"description": "Review button text", "context": "review_feedback"}, "btn_retry": "Retry", "@btn_retry": {"description": "Retry button text", "context": "review_feedback"}, "msg_no_feedback_available": "No feedback available for this train.", "@msg_no_feedback_available": {"description": "Message when no feedback is available", "context": "review_feedback"}, "dialog_title_review_feedback": "Review Feedback", "@dialog_title_review_feedback": {"description": "Title for review feedback dialog", "context": "rm_review_feedback_dialogue"}, "text_status_pending_editable": "Status Pending - Editable", "@text_status_pending_editable": {"description": "Status pending editable text", "context": "normal_review_feedback_dialogue"}, "text_status_completed_not_editable": "Status Completed - Not Editable", "@text_status_completed_not_editable": {"description": "Status completed not editable text", "context": "normal_review_feedback_dialogue"}, "text_pnr_documents": "PNR Documents:", "@text_pnr_documents": {"description": "Label for PNR documents section", "context": "rm_review_feedback_dialogue"}, "text_failed_to_load_image": "Failed to load image", "@text_failed_to_load_image": {"description": "Error message when image fails to load", "context": "rm_review_feedback_dialogue"}, "text_tap_to_play_video": "Tap to play video", "@text_tap_to_play_video": {"description": "Text shown on video thumbnail", "context": "rm_review_feedback_dialogue"}, "form_remarks_hint": "Add your railmadad feedback(remarks) here (max 100 Characters)...", "@form_remarks_hint": {"description": "Hint text for remarks field", "context": "rm_review_feedback_dialogue"}, "form_resolved_yes_no": "Resolved (Yes/No) *", "@form_resolved_yes_no": {"description": "Label for resolved status field", "context": "rm_review_feedback_dialogue"}, "form_marks_1_to_10": "Marks (1 to 10) *", "@form_marks_1_to_10": {"description": "Label for marks field", "context": "rm_review_feedback_dialogue"}, "status_none_selected": "None selected", "@status_none_selected": {"description": "None selected placeholder text", "context": "rm_review_feedback_dialogue"}, "validation_select_issue_type": "Please select an issue type", "@validation_select_issue_type": {"description": "Validation message for issue type selection", "context": "rm_review_feedback_dialogue"}, "validation_select_sub_issue_type": "Please select a sub-issue type", "@validation_select_sub_issue_type": {"description": "Validation message for sub issue type selection", "context": "rm_review_feedback_dialogue"}, "validation_select_resolved_status": "Please select resolved status", "@validation_select_resolved_status": {"description": "Validation message for resolved status selection", "context": "rm_review_feedback_dialogue"}, "validation_select_marks": "Please select marks", "@validation_select_marks": {"description": "Validation message for marks selection", "context": "rm_review_feedback_dialogue"}, "validation_enter_valid_email": "Please enter a valid Email ID.", "@validation_enter_valid_email": {"description": "Validation message for email requirement", "context": "rm_review_feedback_dialogue"}, "validation_enter_otp": "Please enter the OTP.", "@validation_enter_otp": {"description": "Validation message for OTP requirement", "context": "rm_review_feedback_dialogue"}, "validation_remarks_max_100": "Remarks cannot exceed 100 characters", "@validation_remarks_max_100": {"description": "Validation message for remarks length", "context": "rm_review_feedback_dialogue"}, "msg_cannot_verify_email_completed": "Cannot verify email for completed feedback.", "@msg_cannot_verify_email_completed": {"description": "Error message for email verification on completed feedback", "context": "normal_review_feedback_dialogue"}, "msg_cannot_verify_otp_completed": "Cannot verify OTP for completed feedback.", "@msg_cannot_verify_otp_completed": {"description": "Error message for OTP verification on completed feedback", "context": "normal_review_feedback_dialogue"}, "msg_otp_verified_successfully": "OTP verified successfully.", "@msg_otp_verified_successfully": {"description": "Success message for OTP verification", "context": "normal_review_feedback_dialogue"}, "msg_feedback_updated_successfully": "Feedback updated successfully", "@msg_feedback_updated_successfully": {"description": "Success message for feedback update", "context": "normal_review_feedback_dialogue"}, "msg_failed_to_update_feedback": "Failed to update feedback", "@msg_failed_to_update_feedback": {"description": "Error message for feedback update failure", "context": "rm_review_feedback_dialogue"}, "msg_failed_to_load_video": "Failed to load video: {error}", "@msg_failed_to_load_video": {"description": "Error message for video loading failure", "context": "rm_review_feedback_dialogue", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "msg_error_with_details": "Error: {error}", "@msg_error_with_details": {"description": "Generic error message with details", "context": "rm_review_feedback_dialogue", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "msg_error_or_something_wrong": "Error: {error}", "@msg_error_or_something_wrong": {"description": "Error message with fallback for empty errors", "context": "rm_review_feedback_dialogue", "placeholders": {"error": {"type": "String", "description": "The error message or 'Something went wrong'"}}}, "loading_updating_feedback": "Updating feedback...", "@loading_updating_feedback": {"description": "Loading message for updating feedback", "context": "rm_review_feedback_dialogue"}, "text_character_count_100": "{count}/100 characters", "@text_character_count_100": {"description": "Character count display for 100 character limit", "context": "rm_review_feedback_dialogue", "placeholders": {"count": {"type": "int", "description": "Current character count"}}}, "dialog_email_verification_info_title": "Email Verification Info", "@dialog_email_verification_info_title": {"description": "Title for email verification info dialog", "context": "rm_review_feedback_dialogue"}, "dialog_email_verification_spam_notice": "Please note that verification emails may sometimes be delivered to your spam/junk folder.", "@dialog_email_verification_spam_notice": {"description": "Notice about emails in spam folder", "context": "rm_review_feedback_dialogue"}, "dialog_after_requesting_otp": "After requesting OTP:", "@dialog_after_requesting_otp": {"description": "Header for OTP instructions", "context": "rm_review_feedback_dialogue"}, "dialog_check_inbox_first": "• Check your inbox first", "@dialog_check_inbox_first": {"description": "First instruction for checking email", "context": "rm_review_feedback_dialogue"}, "dialog_check_spam_folder": "• If not found, check spam/junk folder", "@dialog_check_spam_folder": {"description": "Second instruction for checking spam folder", "context": "rm_review_feedback_dialogue"}, "dialog_add_safe_sender": "• Add our domain to your safe sender list", "@dialog_add_safe_sender": {"description": "Third instruction for adding to safe sender list", "context": "rm_review_feedback_dialogue"}, "text_otp_sent_check_folders": "OTP sent to your email. Please check both inbox and spam folders.", "@text_otp_sent_check_folders": {"description": "Message shown when OTP is sent to email", "context": "rm_review_feedback_dialogue"}, "form_issue_type_label": "Issue Type", "@form_issue_type_label": {"description": "Label for issue type dropdown", "context": "rm_review_feedback_dialogue"}, "form_sub_issue_type_label": "Sub Issue Type", "@form_sub_issue_type_label": {"description": "Label for sub issue type dropdown", "context": "rm_review_feedback_dialogue"}, "form_task_status_label": "Task Status", "@form_task_status_label": {"description": "Label for task status dropdown", "context": "rm_review_feedback_dialogue"}, "status_pending_option": "Pending", "@status_pending_option": {"description": "Pending option in status dropdown", "context": "rm_review_feedback_dialogue"}, "status_completed_option": "Completed", "@status_completed_option": {"description": "Completed option in status dropdown", "context": "rm_review_feedback_dialogue"}, "text_need_help_reach_out": "Need help? Reach out to our team below", "@text_need_help_reach_out": {"description": "Help message on customer care screen", "context": "customer_care_screen"}, "error_could_not_launch_phone": "Could not launch {phoneNumber}", "@error_could_not_launch_phone": {"description": "Error message when phone call cannot be launched", "context": "customer_care_screen", "placeholders": {"phoneNumber": {"type": "String", "example": "7903375271"}}}, "text_notifications": "Notifications", "@text_notifications": {"description": "Notifications screen title", "context": "notification_center_screen"}, "tooltip_test_notifications": "Test Notifications", "@tooltip_test_notifications": {"description": "Tooltip for test notifications button", "context": "notification_center_screen"}, "tooltip_notification_settings": "Notification Settings", "@tooltip_notification_settings": {"description": "Tooltip for notification settings button", "context": "notification_center_screen"}, "tooltip_mark_all_as_read": "Mark all as read", "@tooltip_mark_all_as_read": {"description": "Tooltip for mark all as read button", "context": "notification_center_screen"}, "tooltip_clear_all_notifications": "Clear all notifications", "@tooltip_clear_all_notifications": {"description": "Tooltip for clear all notifications button", "context": "notification_center_screen"}, "text_no_notifications": "No notifications", "@text_no_notifications": {"description": "Message when no notifications are available", "context": "notification_center_screen"}, "dialog_title_clear_notifications": "Clear Notifications", "@dialog_title_clear_notifications": {"description": "Title for clear notifications confirmation dialog", "context": "notification_center_screen"}, "dialog_content_clear_notifications": "Are you sure you want to clear all notifications?", "@dialog_content_clear_notifications": {"description": "Content for clear notifications confirmation dialog", "context": "notification_center_screen"}, "btn_clear": "Clear", "@btn_clear": {"description": "Button to clear file selection", "context": "upload_widget"}, "text_notification_fallback": "Notification", "@text_notification_fallback": {"description": "Fallback title for notifications without a title", "context": "notification_center_screen"}, "text_coach_details": "Coach <PERSON>", "@text_coach_details": {"description": "Header for coach details table", "context": "notification_center_screen"}, "table_header_station": "Station", "@table_header_station": {"description": "Station column header in notification table", "context": "notification_center_screen"}, "table_header_coach": "Coach", "@table_header_coach": {"description": "Coach column header in notification table", "context": "notification_center_screen"}, "table_header_onboard": "Onboard", "@table_header_onboard": {"description": "Onboard column header in notification table", "context": "notification_center_screen"}, "table_header_deboard": "Deboard", "@table_header_deboard": {"description": "Deboard column header in notification table", "context": "notification_center_screen"}, "table_header_vacant": "Vacant", "@table_header_vacant": {"description": "Vacant column header in notification table", "context": "notification_center_screen"}, "text_notification_system_tests": "Notification System Tests", "@text_notification_system_tests": {"description": "App bar title for notification test screen", "context": "notification_test_screen"}, "text_notification_system_testing": "Notification System Testing", "@text_notification_system_testing": {"description": "Main heading for notification testing section", "context": "notification_test_screen"}, "text_notification_test_description": "This will test the complete notification flow:\n• FCM token generation and storage\n• Firestore token synchronization\n• Cloud Function endpoint connectivity\n• End-to-end notification delivery", "@text_notification_test_description": {"description": "Description of what the notification tests will do", "context": "notification_test_screen"}, "text_ready_to_run_notification_tests": "Ready to run notification tests", "@text_ready_to_run_notification_tests": {"description": "Initial status message for notification tests", "context": "notification_test_screen"}, "btn_run_all_tests": "Run All Tests", "@btn_run_all_tests": {"description": "Button text to run all notification tests", "context": "notification_test_screen"}, "btn_running_tests": "Running Tests...", "@btn_running_tests": {"description": "Button text when tests are running", "context": "notification_test_screen"}, "btn_individual_tests": "Individual Tests", "@btn_individual_tests": {"description": "But<PERSON> text for individual test selection", "context": "notification_test_screen"}, "text_test_results": "Test Results", "@text_test_results": {"description": "Header for test results section", "context": "notification_test_screen"}, "text_no_test_results_yet": "No test results yet", "@text_no_test_results_yet": {"description": "Message when no test results are available", "context": "notification_test_screen"}, "text_run_tests_to_see_results": "Run tests to see results here", "@text_run_tests_to_see_results": {"description": "Instruction message for running tests", "context": "notification_test_screen"}, "text_running_comprehensive_tests": "Running comprehensive notification tests...", "@text_running_comprehensive_tests": {"description": "Status message when running all tests", "context": "notification_test_screen"}, "text_all_tests_completed_successfully": "All tests completed successfully!", "@text_all_tests_completed_successfully": {"description": "Success message when all tests pass", "context": "notification_test_screen"}, "text_some_tests_failed": "Some tests failed. Check results below.", "@text_some_tests_failed": {"description": "Message when some tests fail", "context": "notification_test_screen"}, "text_test_execution_failed": "Test execution failed: {error}", "@text_test_execution_failed": {"description": "Error message when test execution fails", "context": "notification_test_screen", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "dialog_title_select_test": "Select Test", "@dialog_title_select_test": {"description": "Title for test selection dialog", "context": "notification_test_screen"}, "test_fcm_token_generation": "FCM Token Generation", "@test_fcm_token_generation": {"description": "Test option for FCM token generation", "context": "notification_test_screen"}, "test_firestore_token_storage": "Firestore Token Storage", "@test_firestore_token_storage": {"description": "Test option for Firestore token storage", "context": "notification_test_screen"}, "test_complete_notification_sync": "Complete Notification Sync", "@test_complete_notification_sync": {"description": "Test option for complete notification sync", "context": "notification_test_screen"}, "test_cloud_function_endpoint": "Cloud Function Endpoint", "@test_cloud_function_endpoint": {"description": "Test option for cloud function endpoint", "context": "notification_test_screen"}, "test_enhanced_notification": "Enhanced Notification Test", "@test_enhanced_notification": {"description": "Test option for enhanced notifications", "context": "notification_test_screen"}, "test_debug_firestore_token_storage": "Debug Firestore Token Storage", "@test_debug_firestore_token_storage": {"description": "Test option for debugging Firestore token storage", "context": "notification_test_screen"}, "test_force_token_refresh": "Force Token Refresh", "@test_force_token_refresh": {"description": "Test option for forcing token refresh", "context": "notification_test_screen"}, "test_quick_token_check": "Quick Token Check", "@test_quick_token_check": {"description": "Test option for quick token check", "context": "notification_test_screen"}, "text_running_test": "Running {testName} test...", "@text_running_test": {"description": "Status message when running a specific test", "context": "notification_test_screen", "placeholders": {"testName": {"type": "String", "description": "The name of the test being run"}}}, "text_test_completed_successfully": "{testName} completed successfully!", "@text_test_completed_successfully": {"description": "Success message when a test completes successfully", "context": "notification_test_screen", "placeholders": {"testName": {"type": "String", "description": "The name of the test that completed"}}}, "text_test_failed": "{testName} failed. Check results below.", "@text_test_failed": {"description": "Failure message when a test fails", "context": "notification_test_screen", "placeholders": {"testName": {"type": "String", "description": "The name of the test that failed"}}}, "text_no_test_data_available": "No test data available", "@text_no_test_data_available": {"description": "Message when no test data is available to display", "context": "notification_test_screen"}, "text_test_passed": "PASSED", "@text_test_passed": {"description": "Label for passed tests", "context": "notification_test_screen"}, "text_test_failed_label": "FAILED", "@text_test_failed_label": {"description": "Label for failed tests", "context": "notification_test_screen"}, "rail_sathi_app_bar_title": "Rail Sathi", "@rail_sathi_app_bar_title": {"description": "App bar title for Rail Sathi screen", "context": "rail_sathi_screen"}, "tab_write_complaint": "<PERSON><PERSON> Complaint", "@tab_write_complaint": {"description": "Tab text for writing complaints", "context": "rail_sathi_screen"}, "tab_view_complaints": "<PERSON> Complaints", "@tab_view_complaints": {"description": "Tab text for viewing complaints", "context": "rail_sathi_screen"}, "error_could_not_launch_link": "Could not launch the link", "@error_could_not_launch_link": {"description": "Error message when link cannot be launched", "context": "rail_sathi_qr_screen"}, "error_invalid_response_format": "Invalid response format from server", "@error_invalid_response_format": {"description": "Error message for invalid server response", "context": "obhs_to_mcc_handover_screen"}, "error_failed_to_load_trip_report": "Failed to load trip report: {error}", "@error_failed_to_load_trip_report": {"description": "Error message when loading trip report fails", "context": "obhs_to_mcc_handover_screen", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "msg_data_refreshed_successfully": "Data refreshed successfully", "@msg_data_refreshed_successfully": {"description": "Success message for data refresh", "context": "obhs_to_mcc_handover_screen"}, "error_refresh_failed": "Refresh failed: {error}", "@error_refresh_failed": {"description": "Error message when refresh fails", "context": "obhs_to_mcc_handover_screen", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "error_select_train_date_first": "Please select a train and date first", "@error_select_train_date_first": {"description": "Error message when train and date not selected", "context": "trip_report_screen"}, "text_no_coaches_found": "No coaches found.", "@text_no_coaches_found": {"description": "Message when no coaches are available", "context": "obhs_to_mcc_handover_screen"}, "text_please_select_train_date": "Please select a train and date.", "@text_please_select_train_date": {"description": "Instruction to select train and date", "context": "trip_report_screen"}, "text_coach_label": "Coach {coach}", "@text_coach_label": {"description": "Label for coach with coach number", "context": "trip_report_screen", "placeholders": {"coach": {"type": "String", "description": "Coach number or identifier"}}}, "text_no_issues_reported": "No issues reported", "@text_no_issues_reported": {"description": "Message when no issues are reported for a coach", "context": "obhs_to_mcc_handover_screen"}, "text_reported_issues": "Reported Issues:", "@text_reported_issues": {"description": "Label for reported issues section", "context": "obhs_to_mcc_handover_screen"}, "text_unknown_issue": "Unknown Issue", "@text_unknown_issue": {"description": "Fallback text for unknown issues", "context": "obhs_to_mcc_handover_screen"}, "btn_manage_issues": "Manage Issues", "@btn_manage_issues": {"description": "<PERSON><PERSON> text for managing issues", "context": "obhs_to_mcc_handover_screen"}, "text_train_rake_deficiency_report": "Train Rake Deficiency Report", "@text_train_rake_deficiency_report": {"description": "App bar title for trip report screen", "context": "trip_report_screen"}, "text_rake_deficiency_report_issues": "Rake Deficiency Report Issues", "@text_rake_deficiency_report_issues": {"description": "Header text for rake deficiency report issues", "context": "trip_report_screen"}, "tooltip_upload_coach_image": "Upload Coach Image", "@tooltip_upload_coach_image": {"description": "Tooltip for upload coach image button", "context": "obhs_to_mcc_handover_screen"}, "btn_upload_image_video": "Upload Image/Video", "@btn_upload_image_video": {"description": "Button text for uploading media", "context": "rail_sathi_write_complaint"}, "btn_submit_issue": "Submit Issue", "@btn_submit_issue": {"description": "Button text for submitting issue", "context": "rail_sathi_write_complaint"}, "form_pnr": "PNR", "@form_pnr": {"description": "Label for PNR field", "context": "rail_sathi_write_complaint"}, "form_search_by_train_number_or_name": "Search by train number or name", "@form_search_by_train_number_or_name": {"description": "Hint text for train search field", "context": "rail_sathi_write_complaint"}, "form_dd_mm_yyyy": "DD/MM/YYYY", "@form_dd_mm_yyyy": {"description": "Date format hint", "context": "rail_sathi_write_complaint"}, "btn_next": "Next", "@btn_next": {"description": "<PERSON><PERSON> text for next action", "context": "rail_sathi_write_complaint"}, "tab_cleaning_issues": "Cleaning Issues", "@tab_cleaning_issues": {"description": "Tab text for cleaning issues", "context": "rail_sathi_write_complaint"}, "tab_linen_related_issues": "Linen Related Issues", "@tab_linen_related_issues": {"description": "Tab text for linen related issues", "context": "rail_sathi_write_complaint"}, "msg_please_fill_all_required_details": "Please fill in all required details.", "@msg_please_fill_all_required_details": {"description": "Message for incomplete form", "context": "rail_sathi_write_complaint"}, "text_ehk_not_assigned": "EHK Not Assigned", "@text_ehk_not_assigned": {"description": "Text when EHK is not assigned", "context": "rail_sathi_write_complaint"}, "msg_pnr_validation_error_e": "PNR validation error: $e", "@msg_pnr_validation_error_e": {"description": "Message when PNR validation encounters an error", "context": "rail_sathi_write_complaint"}, "msg_are_you_sure_delete_image": "Are you sure you want to delete this image?", "@msg_are_you_sure_delete_image": {"description": "Confirmation message for deleting image", "context": "rail_sathi_view_complaints"}, "text_selected_date": "Selected Date: {day}/{month}/{year}", "@text_selected_date": {"description": "Display text for selected date", "context": "view_complaints", "placeholders": {"day": {"type": "int"}, "month": {"type": "int"}, "year": {"type": "int"}}}, "text_date": "Date: {date}", "@text_date": {"description": "Display text for date", "context": "view_complaints", "placeholders": {"date": {"type": "String"}}}, "text_pnr": "PNR: {pnr}", "@text_pnr": {"description": "Text showing PNR", "context": "rail_sathi_view_complaints", "placeholders": {"pnr": {"type": "String"}}}, "msg_are_you_sure_delete_complaint": "Are you sure you want to delete this complaint?", "@msg_are_you_sure_delete_complaint": {"description": "Confirmation message for deleting complaint", "context": "rail_sathi_view_complaints"}, "reports_app_bar_title": "Reports", "@reports_app_bar_title": {"description": "App bar title for Reports screen", "context": "pdf_screen"}, "btn_attendance_report": "Attendance Report", "@btn_attendance_report": {"description": "Button text for attendance report", "context": "pdf_screen"}, "btn_round_rake_deficiency_report": "Round Rake Deficiency Report", "@btn_round_rake_deficiency_report": {"description": "Button text for round rake deficiency report", "context": "pdf_screen"}, "btn_train_report": "Train Report", "@btn_train_report": {"description": "Button text for train report", "context": "pdf_screen"}, "btn_detailed_attendance_report": "Detailed Attendance Report", "@btn_detailed_attendance_report": {"description": "Button text for detailed attendance report", "context": "pdf_screen"}, "btn_detailed_round_trip_attendance_report": "Detailed Round Trip Attendance Report", "@btn_detailed_round_trip_attendance_report": {"description": "Button text for detailed round trip attendance report", "context": "pdf_screen"}, "btn_obhs_to_mcc_handover": "OBHS to MCC Handover", "@btn_obhs_to_mcc_handover": {"description": "Button text for OBHS to MCC handover", "context": "pdf_screen"}, "btn_mcc_to_obhs_handover": "MCC to OBHS Handover", "@btn_mcc_to_obhs_handover": {"description": "Button text for MCC to OBHS handover", "context": "pdf_screen"}, "btn_monthly_attendance_report": "Monthly Attendance Report", "@btn_monthly_attendance_report": {"description": "Button text for monthly attendance report", "context": "pdf_screen"}, "btn_monthly_round_trip_attendance_report": "Monthly Round Trip Attendance Report", "@btn_monthly_round_trip_attendance_report": {"description": "Button text for monthly round trip attendance report", "context": "pdf_screen"}, "text_daily_reports": "Daily Reports", "@text_daily_reports": {"description": "Section title for daily reports", "context": "pdf_screen"}, "text_monthly_reports": "Monthly Reports", "@text_monthly_reports": {"description": "Section title for monthly reports", "context": "pdf_screen"}, "btn_show_all": "Show All", "@btn_show_all": {"description": "Button text to show all items", "context": "pdf_screen"}, "btn_show_less": "Show Less", "@btn_show_less": {"description": "Button text to show fewer items", "context": "pdf_screen"}, "section_daily_reports": "Daily Reports", "@section_daily_reports": {"description": "Section header for daily reports", "context": "pdf_screen"}, "section_monthly_reports": "Monthly Reports", "@section_monthly_reports": {"description": "Section header for monthly reports", "context": "pdf_screen"}, "section_attendance_for": "Attendance For", "@section_attendance_for": {"description": "Section header for attendance selection", "context": "pdf_screen"}, "report_attendance": "Attendance Report", "@report_attendance": {"description": "Attendance report button text", "context": "pdf_screen"}, "report_detailed_attendance": "Detailed Attendance Report", "@report_detailed_attendance": {"description": "Detailed attendance report button text", "context": "pdf_screen"}, "report_detailed_round_trip": "Detailed Round Trip Attendance Report", "@report_detailed_round_trip": {"description": "Detailed round trip attendance report button text", "context": "pdf_screen"}, "report_obhs_to_mcc_handover": "OBHS to MCC Handover", "@report_obhs_to_mcc_handover": {"description": "OBHS to MCC handover report button text", "context": "pdf_screen"}, "report_monthly_attendance": "Monthly Attendance Report", "@report_monthly_attendance": {"description": "Monthly attendance report button text", "context": "pdf_screen"}, "report_monthly_round_trip": "Monthly Round Trip Attendance Report", "@report_monthly_round_trip": {"description": "Monthly round trip attendance report button text", "context": "pdf_screen"}, "report_monthly_with_mobile": "Monthly With Mobile", "@report_monthly_with_mobile": {"description": "Monthly report with mobile button text", "context": "pdf_screen"}, "text_notification_settings": "Notification Settings", "@text_notification_settings": {"description": "Notification settings screen title", "context": "notification_settings_screen"}, "text_save_settings": "Save Settings", "@text_save_settings": {"description": "Save settings button text and tooltip", "context": "notification_settings_screen"}, "text_onboarding_notifications": "Onboarding Notifications", "@text_onboarding_notifications": {"description": "Main onboarding notifications section title", "context": "notification_settings_screen"}, "text_enable_onboarding_notifications": "Enable Onboarding Notifications", "@text_enable_onboarding_notifications": {"description": "Toggle for enabling onboarding notifications", "context": "notification_settings_screen"}, "text_station_approach_alerts": "Station Approach Alerts", "@text_station_approach_alerts": {"description": "Toggle for station approach alerts", "context": "notification_settings_screen"}, "text_boarding_alerts": "Boarding Alerts", "@text_boarding_alerts": {"description": "Toggle for boarding alerts", "context": "notification_settings_screen"}, "text_off_boarding_alerts": "Off-boarding Alerts", "@text_off_boarding_alerts": {"description": "Toggle for off-boarding alerts", "context": "notification_settings_screen"}, "text_proximity_alerts": "Proximity Alerts", "@text_proximity_alerts": {"description": "Toggle for proximity alerts", "context": "notification_settings_screen"}, "text_timing_settings": "Timing <PERSON>s", "@text_timing_settings": {"description": "Timing settings section title", "context": "notification_settings_screen"}, "text_advance_notice_minutes": "Advance Notice (minutes)", "@text_advance_notice_minutes": {"description": "Slider setting for advance notice in minutes", "context": "notification_settings_screen"}, "text_proximity_threshold_km": "Proximity Threshold (km)", "@text_proximity_threshold_km": {"description": "Slider setting for proximity threshold in kilometers", "context": "notification_settings_screen"}, "text_coach_filters": "Coach <PERSON><PERSON>", "@text_coach_filters": {"description": "Coach filters section title", "context": "notification_settings_screen"}, "text_enable_coach_specific_filtering": "Enable Coach-specific Filtering", "@text_enable_coach_specific_filtering": {"description": "Toggle for enabling coach-specific filtering", "context": "notification_settings_screen"}, "text_enabled_coach_types": "Enabled Coach Types:", "@text_enabled_coach_types": {"description": "Label for enabled coach types section", "context": "notification_settings_screen"}, "text_sound_vibration": "Sound & Vibration", "@text_sound_vibration": {"description": "Sound and vibration settings section title", "context": "notification_settings_screen"}, "text_enable_sound": "Enable Sound", "@text_enable_sound": {"description": "Toggle for enabling notification sound", "context": "notification_settings_screen"}, "text_enable_vibration": "Enable Vibration", "@text_enable_vibration": {"description": "Toggle for enabling notification vibration", "context": "notification_settings_screen"}, "text_advanced_settings": "Advanced Settings", "@text_advanced_settings": {"description": "Advanced settings section title", "context": "notification_settings_screen"}, "text_background_notifications": "Background Notifications", "@text_background_notifications": {"description": "Toggle for background notifications", "context": "notification_settings_screen"}, "text_location_based_notifications": "Location-based Notifications", "@text_location_based_notifications": {"description": "Toggle for location-based notifications", "context": "notification_settings_screen"}, "text_max_notifications_per_hour": "Max Notifications per Hour", "@text_max_notifications_per_hour": {"description": "Slider setting for maximum notifications per hour", "context": "notification_settings_screen"}, "msg_notification_preferences_saved": "Notification preferences saved successfully", "@msg_notification_preferences_saved": {"description": "Success message when notification preferences are saved", "context": "notification_settings_screen"}, "msg_error_saving_preferences": "Error saving preferences: {error}", "@msg_error_saving_preferences": {"description": "Error message when saving preferences fails", "context": "notification_settings_screen", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "error_fetching_trains": "Error fetching trains: {error}", "@error_fetching_trains": {"description": "Error message when fetching trains fails", "context": "view_complaints", "placeholders": {"error": {"type": "String"}}}, "form_mobile": "Mobile", "@form_mobile": {"description": "Form label for mobile field", "context": "view_complaints"}, "form_enter_train_number": "Enter Train Number", "@form_enter_train_number": {"description": "Form label for manual train number entry", "context": "view_complaints"}, "form_description": "Description", "@form_description": {"description": "Form label for description field", "context": "view_complaints"}, "dropdown_other": "Other", "@dropdown_other": {"description": "Dropdown option for other/custom selection", "context": "view_complaints"}, "dropdown_cleaning": "cleaning", "@dropdown_cleaning": {"description": "Complaint type option for cleaning", "context": "view_complaints"}, "dropdown_linen": "linen", "@dropdown_linen": {"description": "Complaint type option for linen", "context": "view_complaints"}, "dropdown_pending": "pending", "@dropdown_pending": {"description": "Status option for pending", "context": "view_complaints"}, "dropdown_completed": "completed", "@dropdown_completed": {"description": "Status option for completed", "context": "view_complaints"}, "dialog_delete_image": "Delete Image", "@dialog_delete_image": {"description": "Title for delete image dialog", "context": "view_complaints"}, "dialog_delete_image_confirm": "Are you sure you want to delete this image?", "@dialog_delete_image_confirm": {"description": "Confirmation message for deleting image", "context": "view_complaints"}, "dialog_delete_complaint": "Delete Co<PERSON>t", "@dialog_delete_complaint": {"description": "Title for delete complaint dialog", "context": "view_complaints"}, "dialog_delete_complaint_confirm": "Are you sure you want to delete this complaint?", "@dialog_delete_complaint_confirm": {"description": "Confirmation message for deleting complaint", "context": "view_complaints"}, "btn_add_image": "Add Image", "@btn_add_image": {"description": "Add image button text", "context": "view_complaints"}, "btn_save_changes": "Save Changes", "@btn_save_changes": {"description": "Save changes button text", "context": "view_complaints"}, "btn_edit": "Edit", "@btn_edit": {"description": "Edit button text", "context": "view_complaints"}, "btn_select_date": "Select Date", "@btn_select_date": {"description": "Select date button text", "context": "view_complaints"}, "text_edit_complaint": "Edit Complaint #{complainId}", "@text_edit_complaint": {"description": "Title for edit complaint dialog", "context": "view_complaints", "placeholders": {"complainId": {"type": "int"}}}, "form_train_label": "Train", "@form_train_label": {"description": "Form label for train field", "context": "view_complaints"}, "error_failed_to_fetch_complaints": "Failed to fetch complaints", "@error_failed_to_fetch_complaints": {"description": "Error message when fetching complaints fails", "context": "view_complaints"}, "text_berth_no": "Berth No", "@text_berth_no": {"description": "Label for berth number field", "context": "view_complaints"}, "msg_image_deleted": "Image deleted", "@msg_image_deleted": {"description": "Success message when image is deleted", "context": "view_complaints"}, "msg_complaint_updated": "<PERSON><PERSON><PERSON><PERSON> updated", "@msg_complaint_updated": {"description": "Success message when complaint is updated", "context": "view_complaints"}, "msg_update_failed": "Update failed", "@msg_update_failed": {"description": "Error message when update fails", "context": "view_complaints"}, "msg_complaint_deleted_success": "<PERSON><PERSON><PERSON><PERSON> deleted successfully", "@msg_complaint_deleted_success": {"description": "Success message when complaint is deleted", "context": "view_complaints"}, "msg_failed_to_delete_complaint": "Failed to delete complaint", "@msg_failed_to_delete_complaint": {"description": "Error message when deleting complaint fails", "context": "view_complaints"}, "text_pnr_view_complaints": "PNR: {pnrNumber}", "@text_pnr_view_complaints": {"description": "Display text for PNR number", "context": "view_complaints", "placeholders": {"pnrNumber": {"type": "String"}}}, "snackbar_image_deleted": "Image deleted", "@snackbar_image_deleted": {"description": "Success message when image is deleted", "context": "view_complaints"}, "snackbar_failed_to_delete_image": "Failed to delete image: {error}", "@snackbar_failed_to_delete_image": {"description": "Error message when image deletion fails", "context": "view_complaints", "placeholders": {"error": {"type": "String"}}}, "snackbar_failed_to_delete_complaint": "Failed to delete complaint", "@snackbar_failed_to_delete_complaint": {"description": "Error message when complaint deletion fails", "context": "view_complaints"}, "snackbar_failed_to_fetch_complaints": "Failed to fetch complaints", "@snackbar_failed_to_fetch_complaints": {"description": "Error message when fetching complaints fails", "context": "view_complaints"}, "btn_rake_deficiency_report_simple": "Rake Deficiency Report", "@btn_rake_deficiency_report_simple": {"description": "Button text for rake deficiency report", "context": "pdf_screen"}, "btn_monthly_with_mobile": "Monthly With Mobile", "@btn_monthly_with_mobile": {"description": "Button text for monthly report with mobile", "context": "pdf_screen"}, "btn_monthly_without_mobile": "Monthly Without Mobile", "@btn_monthly_without_mobile": {"description": "Button text for monthly report without mobile", "context": "pdf_screen"}, "text_info": "Info", "@text_info": {"description": "Info dialog title", "context": "pdf_screen"}, "text_reports_for": "Reports For", "@text_reports_for": {"description": "Label for reports selection", "context": "pdf_screen"}, "text_trains_not_running": "Trains Not Running", "@text_trains_not_running": {"description": "Title for trains not running dialog", "context": "pdf_screen"}, "text_train_not_running": "Train Not Running", "@text_train_not_running": {"description": "Title for single train not running dialog", "context": "pdf_screen"}, "text_please_select_relevant_trains": "Please select only relevant Trains to generate report. The more the trains the more time it will take to generate the report.", "@text_please_select_relevant_trains": {"description": "Warning message about selecting relevant trains", "context": "pdf_screen"}, "text_following_trains_not_running": "The following trains are NOT running on *{dayOfWeek}*:", "@text_following_trains_not_running": {"description": "Message about trains not running on specific day", "context": "pdf_screen"}, "text_train_label": "Train: {trainNo}", "@text_train_label": {"description": "Label for train number", "context": "pdf_screen"}, "text_running_days_label": "Running Days: {runningDays}", "@text_running_days_label": {"description": "Label for running days", "context": "pdf_screen"}, "text_running_trains_on": "Running trains on *{dayOfWeek}* ({count}): {trainList}", "@text_running_trains_on": {"description": "Message about running trains on specific day", "context": "pdf_screen"}, "text_train_not_running_message": "Train {trainNumber} is NOT running on {dayOfWeek} \nRunning Days: {runningDays}", "@text_train_not_running_message": {"description": "Message for single train not running", "context": "pdf_screen"}, "pnr_status_title": "PNR Status", "@pnr_status_title": {"description": "Title for PNR status screen", "context": "pnr_status"}, "map_screen_title": "Map Screen", "@map_screen_title": {"description": "Title for map screen", "context": "map_screen"}, "map_select_date": "Select date", "@map_select_date": {"description": "Label for date selection", "context": "map_screen"}, "map_date_hint": "e.g. Jun 27", "@map_date_hint": {"description": "Hint text for date input", "context": "map_screen"}, "btn_today": "Today", "@btn_today": {"description": "Today button text", "context": "map_screen"}, "error_location_services": "Please turn on location services", "@error_location_services": {"description": "Error message for location services", "context": "map_screen"}, "error_occurred": "An error occurred: {error}", "@error_occurred": {"description": "Generic error message", "context": "assign_ehk_ca_screen", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "error_select_train_number": "Please select a train number", "@error_select_train_number": {"description": "Validation error for train number", "context": "map_screen"}, "error_select_date": "Please select a date", "@error_select_date": {"description": "Validation error for date selection", "context": "map_screen"}, "btn_submit": "Submit", "@btn_submit": {"description": "Submit button text", "context": "map_screen"}, "map_current_location": "Your current location", "@map_current_location": {"description": "Text for current location marker", "context": "map_screen"}, "map_openstreetmap_contributors": "OpenStreetMap contributors", "@map_openstreetmap_contributors": {"description": "Attribution text for OpenStreetMap", "context": "map_screen"}, "map_train_number": "Train Number: {trainNumber}", "@map_train_number": {"description": "Train number display in popup", "context": "map_screen", "placeholders": {"trainNumber": {"type": "String"}}}, "map_date": "Date: {date}", "@map_date": {"description": "Date display in popup", "context": "map_screen", "placeholders": {"date": {"type": "String"}}}, "map_onboarding": "Onboarding: {count}", "@map_onboarding": {"description": "Onboarding count in popup", "context": "map_screen", "placeholders": {"count": {"type": "String"}}}, "map_offboarding": "Offboarding: {count}", "@map_offboarding": {"description": "Offboarding count in popup", "context": "map_screen", "placeholders": {"count": {"type": "String"}}}, "hint_enter_pnr_number": "Enter PNR Number", "@hint_enter_pnr_number": {"description": "Hint text for PNR number input field", "context": "pnr_status"}, "label_pnr_number": "PNR Number", "@label_pnr_number": {"description": "Label for PNR number input field", "context": "pnr_status"}, "btn_check_pnr": "Check PNR", "@btn_check_pnr": {"description": "Button text to check PNR status", "context": "pnr_status"}, "text_no_pnr_data_found": "No PNR Data Found", "@text_no_pnr_data_found": {"description": "Message when no PNR data is found", "context": "pnr_status"}, "error_invalid_pnr_number": "Please enter a valid 10-digit PNR number", "@error_invalid_pnr_number": {"description": "Error message for invalid PNR number", "context": "pnr_status"}, "error_failed_fetch_pnr": "Failed to fetch PNR data. Please try again.", "@error_failed_fetch_pnr": {"description": "Error message when PNR fetch fails", "context": "pnr_status"}, "label_train_number": "Train Number", "@label_train_number": {"description": "Label for train number in PNR table", "context": "pnr_status"}, "label_train_name": "Train Name", "@label_train_name": {"description": "Label for train name in PNR table", "context": "pnr_status"}, "label_boarding_date": "Boarding Date", "@label_boarding_date": {"description": "Label for boarding date in PNR table", "context": "pnr_status"}, "label_from": "From", "@label_from": {"description": "Label for departure station in PNR table", "context": "pnr_status"}, "label_to": "To", "@label_to": {"description": "Label for destination station in PNR table", "context": "pnr_status"}, "label_class": "Class", "@label_class": {"description": "Label for travel class in PNR table", "context": "pnr_status"}, "label_departure": "Departure", "@label_departure": {"description": "Label for departure time in PNR table", "context": "pnr_status"}, "label_arrival": "Arrival", "@label_arrival": {"description": "Label for arrival time in PNR table", "context": "pnr_status"}, "label_overall_status": "Overall Status", "@label_overall_status": {"description": "Label for overall status in PNR table", "context": "pnr_status"}, "label_booking_date": "Booking Date", "@label_booking_date": {"description": "Label for booking date in PNR table", "context": "pnr_status"}, "label_passenger": "Passenger", "@label_passenger": {"description": "Label for passenger column in PNR table", "context": "pnr_status"}, "label_coach_berth": "Coach / <PERSON><PERSON>", "@label_coach_berth": {"description": "Label for coach/berth column in PNR table", "context": "pnr_status"}, "label_ehk_users": "EHK Users", "@label_ehk_users": {"description": "Label for EHK users in PNR table", "context": "pnr_status"}, "label_ca_users": "CA Users", "@label_ca_users": {"description": "Label for CA users in PNR table", "context": "pnr_status"}, "label_obhs_users": "OBHS Users", "@label_obhs_users": {"description": "Label for OBHS users in PNR table", "context": "pnr_status"}, "form_first_name_required": "First Name *", "@form_first_name_required": {"description": "Required first name field label", "context": "signup_form"}, "form_first_name_hint": "Enter your first name", "@form_first_name_hint": {"description": "Hint text for first name field", "context": "signup_form"}, "form_middle_name_hint": "Enter your middle name", "@form_middle_name_hint": {"description": "Hint text for middle name field", "context": "signup_form"}, "form_last_name_required": "Last Name *", "@form_last_name_required": {"description": "Required last name field label", "context": "signup_form"}, "form_last_name_hint": "Enter your last name", "@form_last_name_hint": {"description": "Hint text for last name field", "context": "signup_form"}, "form_whatsapp_hint": "Enter 10-digit WhatsApp number", "@form_whatsapp_hint": {"description": "Hint text for WhatsApp number field", "context": "signup_form"}, "form_secondary_phone_optional": "Secondary Phone Number (Optional)", "@form_secondary_phone_optional": {"description": "Optional secondary phone number field label", "context": "signup_form"}, "form_secondary_phone_hint": "Enter 10-digit secondary phone number", "@form_secondary_phone_hint": {"description": "Hint text for secondary phone number field", "context": "signup_form"}, "btn_no_email": "I don't have an email", "@btn_no_email": {"description": "Button text for users without email", "context": "signup_form"}, "btn_request_signup": "Request For Sign Up", "@btn_request_signup": {"description": "Submit button text for signup form", "context": "signup_form"}, "btn_already_have_account": "Already have an account? login", "@btn_already_have_account": {"description": "Link text to login screen", "context": "signup_form"}, "text_whatsapp_same_as_phone": "WhatsApp number is same as phone number", "@text_whatsapp_same_as_phone": {"description": "Text when WhatsApp number matches phone number", "context": "signup_form"}, "text_use_same_whatsapp": "Use same number for WhatsApp?", "@text_use_same_whatsapp": {"description": "Question text for using same number for WhatsApp", "context": "signup_form"}, "msg_submitting_data": "Submitting Data. Please Wait!", "@msg_submitting_data": {"description": "Loading message during form submission", "context": "signup_form"}, "msg_success": "Success", "@msg_success": {"description": "Success message title", "context": "signup_form"}, "msg_error": "Error", "@msg_error": {"description": "Error message title", "context": "signup_form"}, "msg_form_incomplete": "Form Incomplete", "@msg_form_incomplete": {"description": "Form validation error title", "context": "signup_form"}, "msg_complete_required_fields": "Please complete all required fields correctly", "@msg_complete_required_fields": {"description": "Form validation error message", "context": "signup_form"}, "msg_information": "Information", "@msg_information": {"description": "Information dialog title", "context": "signup_form"}, "msg_complete_fields_order": "Please complete all the fields in order. Each field will be enabled only after the previous field is properly filled and validated.", "@msg_complete_fields_order": {"description": "Information message about field completion order", "context": "signup_form"}, "error_enter_first_name": "Please enter your first name", "@error_enter_first_name": {"description": "Validation error for empty first name", "context": "signup_form"}, "error_enter_last_name": "Please enter your last name", "@error_enter_last_name": {"description": "Validation error for empty last name", "context": "signup_form"}, "error_enter_whatsapp": "Please enter WhatsApp number", "@error_enter_whatsapp": {"description": "Validation error for empty WhatsApp number", "context": "signup_form"}, "error_whatsapp_10_digits": "WhatsApp number must be exactly 10 digits", "@error_whatsapp_10_digits": {"description": "Validation error for WhatsApp number length", "context": "signup_form"}, "error_enter_only_numbers": "Please enter only numbers", "@error_enter_only_numbers": {"description": "Validation error for non-numeric input", "context": "signup_form"}, "error_secondary_phone_10_digits": "Secondary phone number must be exactly 10 digits", "@error_secondary_phone_10_digits": {"description": "Validation error for secondary phone number length", "context": "signup_form"}, "error_phone_numbers_different": "Phone number and Secondary phone number must be different", "@error_phone_numbers_different": {"description": "Validation error when phone numbers are the same", "context": "signup_form"}, "error_request_error": "Request Error", "@error_request_error": {"description": "Error dialog title for signup request errors", "context": "signup_error"}, "error_phone_number": "Phone Number", "@error_phone_number": {"description": "Label for phone number in error dialog", "context": "signup_error"}, "error_whatsapp_number": "WhatsApp Number", "@error_whatsapp_number": {"description": "Label for WhatsApp number in error dialog", "context": "signup_error"}, "error_email_id": "Email ID", "@error_email_id": {"description": "Label for email ID in error dialog", "context": "signup_error"}, "error_emp_number": "Emp Number", "@error_emp_number": {"description": "Label for employee number in error dialog", "context": "signup_error"}, "error_update_marked_info": "Please update the information marked with a red cross and try again.", "@error_update_marked_info": {"description": "Instruction text in error dialog", "context": "signup_error"}, "status_new": "New", "@status_new": {"description": "Status text for new entries", "context": "signup_error"}, "status_already_taken": "Already Taken", "@status_already_taken": {"description": "Status text for taken entries", "context": "signup_error"}, "status_already_requested": "Already Requested", "@status_already_requested": {"description": "Status text for requested entries", "context": "signup_error"}, "status_unknown": "Unknown", "@status_unknown": {"description": "Status text for unknown entries", "context": "signup_error"}, "text_forgot_password_instruction": "Forgotten your password? Enter your email address below, and we'll email instructions for setting a new one.", "@text_forgot_password_instruction": {"description": "Instruction text for forgot password form", "context": "forgot_password_form"}, "form_email_label": "Email", "@form_email_label": {"description": "Label for email input field", "context": "forgot_password_form"}, "btn_send_verification_mail": "Send Verification Mail", "@btn_send_verification_mail": {"description": "Button text for sending verification email", "context": "forgot_password_form"}, "msg_password_reset_email_sent": "Password reset email sent successfully", "@msg_password_reset_email_sent": {"description": "Success message when password reset email is sent", "context": "forgot_password_form"}, "error_enter_valid_email_address": "Enter a valid email address", "@error_enter_valid_email_address": {"description": "Error message for invalid email address", "context": "forgot_password_form"}, "text_compressing": "Compressing...", "@text_compressing": {"description": "Status message when compressing files", "context": "custom_app_bar"}, "text_upload_singular": "upload", "@text_upload_singular": {"description": "Singular form of upload", "context": "custom_app_bar"}, "text_upload_plural": "uploads", "@text_upload_plural": {"description": "Plural form of upload", "context": "custom_app_bar"}, "text_preparing": "Preparing...", "@text_preparing": {"description": "Status message when preparing uploads", "context": "custom_app_bar"}, "text_loading": "Loading...", "@text_loading": {"description": "Default loading text", "context": "assign_ehk_ca_screen"}, "text_not_available": "N/A", "@text_not_available": {"description": "Not available abbreviation", "context": "custom_app_bar"}, "text_upload_progress": "Upload {uploadId}...", "@text_upload_progress": {"description": "Upload progress text with ID", "context": "image_upload", "placeholders": {"uploadId": {"type": "String", "description": "Upload identifier"}}}, "text_progress_complete": "{progress}% complete", "@text_progress_complete": {"description": "Progress percentage message", "context": "custom_app_bar", "placeholders": {"progress": {"type": "String", "description": "Progress percentage"}}}, "screen_title_upload_image": "Upload Image", "@screen_title_upload_image": {"description": "AppBar title for image upload screen", "context": "image_upload"}, "error_failed_to_fetch_users": "Failed to fetch users: {error}", "@error_failed_to_fetch_users": {"description": "Error message when fetching users fails", "context": "image_upload", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "error_fetching_image_for_user": "Error fetching image for user {user}: {error}", "@error_fetching_image_for_user": {"description": "Console error message for user image fetch failure", "context": "image_upload", "placeholders": {"user": {"type": "String", "description": "Username"}, "error": {"type": "String", "description": "Error details"}}}, "error_enable_location_service": "Please Enable the location service!", "@error_enable_location_service": {"description": "Error message for location service", "context": "CoachIssueImageUpload"}, "error_failed_to_decode_image": "Failed to decode image", "@error_failed_to_decode_image": {"description": "Error message for image decoding failure", "context": "image_upload"}, "error_image_resizing": "Image resizing error: {error}", "@error_image_resizing": {"description": "Console error message for image resizing", "context": "image_upload", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "msg_image_uploaded_background_verification": "Image uploaded! Background verification in progress. Status update soon.", "@msg_image_uploaded_background_verification": {"description": "Success message for image upload with background processing", "context": "image_upload"}, "error_upload_failed": "Error: {message}", "@error_upload_failed": {"description": "Error message for upload failure", "context": "image_upload", "placeholders": {"message": {"type": "String", "description": "Error message"}}}, "error_failed_to_get_location": "Failed to get location: {error}", "@error_failed_to_get_location": {"description": "Error message for location fetch failure", "context": "image_upload", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_processing_background_tasks": "Processing {count} tasks in background...", "@text_processing_background_tasks": {"description": "Status message for background task processing", "context": "image_upload", "placeholders": {"count": {"type": "String", "description": "Number of background tasks"}}}, "error_fetching_images": "Error fetching images: {error}", "@error_fetching_images": {"description": "Error message for fetching images", "placeholders": {"error": {"type": "String"}}, "context": "CoachIssueImageUpload"}, "dialog_title_attendance_already_submitted": "Attendance Already Submitted", "@dialog_title_attendance_already_submitted": {"description": "Dialog title for attendance already submitted", "context": "image_upload"}, "dialog_content_attendance_already_submitted": "You have already submitted attendance for this station. You cannot submit another one.", "@dialog_content_attendance_already_submitted": {"description": "Dialog content explaining attendance already submitted", "context": "image_upload"}, "text_user_id": "Id: {empNumber}", "@text_user_id": {"description": "User ID display format", "context": "image_upload", "placeholders": {"empNumber": {"type": "String", "description": "Employee number"}}}, "btn_back_to_all_users": "Back to all users", "@btn_back_to_all_users": {"description": "Button to return to user selection", "context": "image_upload"}, "btn_pick_photo": "Pick Photo", "@btn_pick_photo": {"description": "But<PERSON> text for picking a photo", "context": "image_upload"}, "btn_retake_photo": "Retake Photo", "@btn_retake_photo": {"description": "<PERSON><PERSON> text for retaking a photo", "context": "image_upload"}, "text_uploaded_images": "Uploaded Images", "@text_uploaded_images": {"description": "Section title for uploaded images", "context": "image_upload"}, "text_no_images_uploaded": "No images uploaded", "@text_no_images_uploaded": {"description": "Message when no images are uploaded", "context": "image_upload"}, "dialog_title_upload_status": "Upload Status", "@dialog_title_upload_status": {"description": "Dialog title for upload status", "context": "image_upload"}, "text_compressing_image_status": "Compressing image", "@text_compressing_image_status": {"description": "Status text for image compression", "context": "image_upload"}, "text_active_uploads": "Active Uploads:", "@text_active_uploads": {"description": "Label for active uploads section", "context": "image_upload"}, "text_upload_complete_percentage": "{percentage}% complete", "@text_upload_complete_percentage": {"description": "Upload completion percentage", "context": "image_upload", "placeholders": {"percentage": {"type": "String", "description": "Completion percentage"}}}, "dialog_title_image_uploading": "Image Uploading", "@dialog_title_image_uploading": {"description": "Dialog title for image uploading notification", "context": "image_upload"}, "dialog_content_image_uploading": "Your image is being uploaded in the background. Please refresh the page after some time to see the updated status.", "@dialog_content_image_uploading": {"description": "Dialog content explaining background upload process", "context": "image_upload"}, "form_journey_date": "Journey Date:", "@form_journey_date": {"description": "Label for journey date field", "context": "image_upload"}, "form_station_code": "Station Code:", "@form_station_code": {"description": "Label for station code field", "context": "image_upload"}, "form_station_name": "Station Name:", "@form_station_name": {"description": "Label for station name field", "context": "image_upload"}, "screen_title_upload_handover_images": "Upload Handover Images", "@screen_title_upload_handover_images": {"description": "AppBar title for handover image upload screen", "context": "coach_handover_image_upload"}, "status_reported": "Reported", "@status_reported": {"description": "Status option for reported issues", "context": "coach_handover_image_upload"}, "status_fixed": "Fixed", "@status_fixed": {"description": "Status option for fixed issues", "context": "coach_handover_image_upload"}, "status_resolved": "Resolved", "@status_resolved": {"description": "Status option for resolved issues", "context": "coach_handover_image_upload"}, "error_fetching_issues": "Error fetching issues: {error}", "@error_fetching_issues": {"description": "Console error message for issue fetching", "context": "coach_handover_image_upload", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_select_issues_for_coach": "Select Issues for Coach {coach}", "@text_select_issues_for_coach": {"description": "Dialog title for selecting issues for coach", "placeholders": {"coach": {"type": "String"}}, "context": "CoachIssueImageUpload"}, "text_no_issues": "No Issues", "@text_no_issues": {"description": "Text for admin users with no issues", "context": "coach_handover_image_upload"}, "text_issues_selected": "{issueCount} Issue & {subIssueCount} SubIssue Selected", "@text_issues_selected": {"description": "Text showing selected issue count", "context": "coach_handover_image_upload", "placeholders": {"issueCount": {"type": "String", "description": "Number of main issues selected"}, "subIssueCount": {"type": "String", "description": "Number of sub-issues selected"}}}, "text_select_issues": "Select Issues", "@text_select_issues": {"description": "Default text when no issues are selected", "context": "coach_handover_image_upload"}, "msg_please_select_images_to_upload": "Please select images to upload", "@msg_please_select_images_to_upload": {"description": "Error message when no images are selected", "context": "coach_handover_image_upload"}, "msg_please_select_at_least_one_issue": "Please select at least one issue", "@msg_please_select_at_least_one_issue": {"description": "Error message when no issues are selected", "context": "coach_handover_image_upload"}, "msg_images_uploaded_successfully": "Images uploaded successfully", "@msg_images_uploaded_successfully": {"description": "Default success message for image upload", "context": "coach_handover_image_upload"}, "msg_failed_to_upload_images": "Failed to upload images", "@msg_failed_to_upload_images": {"description": "Error message for upload failure", "context": "coach_handover_image_upload"}, "error_uploading_images": "Error uploading images: {error}", "@error_uploading_images": {"description": "Error message with details for upload failure", "context": "coach_handover_image_upload", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_unknown": "Unknown", "@text_unknown": {"description": "Fallback text for unknown values", "context": "coach_handover_image_upload"}, "text_status_by": "{status} By", "@text_status_by": {"description": "Button text for status assignment", "context": "coach_handover_image_upload", "placeholders": {"status": {"type": "String", "description": "Status type"}}}, "tooltip_revert_status": "Revert Status", "@tooltip_revert_status": {"description": "Tooltip for revert status button", "context": "CoachIssueImageUpload"}, "error_updating_issue": "Error updating issue: {error}", "@error_updating_issue": {"description": "Error message for updating issue", "placeholders": {"error": {"type": "String"}}, "context": "CoachIssueImageUpload"}, "msg_no_images_available": "No images available", "@msg_no_images_available": {"description": "Message when no images are available", "context": "CoachIssueImageUpload"}, "table_header_issue_subissue": "Issue/Subissue", "@table_header_issue_subissue": {"description": "Table header for issue/subissue column", "context": "coach_handover_image_upload"}, "table_header_status": "Status", "@table_header_status": {"description": "Table header for status column", "context": "coach_handover_image_upload"}, "table_header_submit_update": "Submit/Update", "@table_header_submit_update": {"description": "Table header for submit/update column", "context": "coach_handover_image_upload"}, "table_header_reported_by": "Reported_by", "@table_header_reported_by": {"description": "Table header for reported by column", "context": "coach_handover_image_upload"}, "table_header_fixed_by": "Fixed_by", "@table_header_fixed_by": {"description": "Table header for fixed by column", "context": "coach_handover_image_upload"}, "table_header_resolved_by": "Resolved_by", "@table_header_resolved_by": {"description": "Table header for resolved by column", "context": "coach_handover_image_upload"}, "text_no_images_selected": "No Images selected", "@text_no_images_selected": {"description": "Message when no images are selected", "context": "coach_handover_image_upload"}, "btn_pick_images": "Pick Images", "@btn_pick_images": {"description": "Button text for picking images", "context": "coach_handover_image_upload"}, "hint_add_comments": "Add your comments here (max 250 characters)...", "@hint_add_comments": {"description": "Hint text for comment input field", "context": "coach_handover_image_upload"}, "btn_uploading": "Uploading...", "@btn_uploading": {"description": "Button text during upload process", "context": "CoachIssueImageUpload"}, "btn_submit_upload": "Submit & Upload", "@btn_submit_upload": {"description": "Button text to submit and upload", "context": "CoachIssueImageUpload"}, "feedback_nonac_toilet_cleaning": "Cleaning of toilets, Wash Basin and other fittings (including Disinfection and provision of deodorant etc.", "@feedback_nonac_toilet_cleaning": {"description": "NONAC feedback item for toilet cleaning", "context": "normal_review_feedback_dialogue"}, "feedback_nonac_compartment_cleaning": "Complete Cleaning of Passenger Compartment (Including spraying of air freshener and cleaning of dust bin)", "@feedback_nonac_compartment_cleaning": {"description": "NONAC feedback item for compartment cleaning", "context": "normal_review_feedback_dialogue"}, "feedback_nonac_janitor_behavior": "Behavior of Janitors / Supervisor (Including hygiene & cleanliness of", "@feedback_nonac_janitor_behavior": {"description": "NONAC feedback item for janitor behavior", "context": "normal_review_feedback_dialogue"}, "feedback_tt_toilet_cleaning": "Cleaning of toilets (including tollet floor, commode pan, wall panels, shelf, miror, wash basin. Disinfectionnand provision of deodorant etc", "@feedback_tt_toilet_cleaning": {"description": "TT feedback item for toilet cleaning", "context": "normal_review_feedback_dialogue"}, "feedback_tt_compartment_cleaning": "Cleaning of Passenger Compartment (including cleaning or passenger aisle, Vestibule area,, Doorway area and doorway wash basin, spraying of air freshene and cleaning of dust bin)", "@feedback_tt_compartment_cleaning": {"description": "TT feedback item for compartment cleaning", "context": "normal_review_feedback_dialogue"}, "feedback_tt_garbage_collection": "Collection of gargage from the coach compartments and clearance of dustbins.", "@feedback_tt_garbage_collection": {"description": "TT feedback item for garbage collection", "context": "normal_review_feedback_dialogue"}, "feedback_tt_pest_control": "Spraying of Mosquito/Cockroach/Fly Repellent and Providing Glue Board Whenever required or on demand by passengers", "@feedback_tt_pest_control": {"description": "TT feedback item for pest control", "context": "normal_review_feedback_dialogue"}, "feedback_tt_janitor_behavior": "Behavior/Response of Janitors / Supervisor (Including hygiene & cleanliness of Janitor/", "@feedback_tt_janitor_behavior": {"description": "TT feedback item for janitor behavior", "context": "normal_review_feedback_dialogue"}, "feedback_tt_toilet_cleaning_alt": "Cleaning of toilets, Wash Basin and other fittings (including Disinfection and provision of deodorant etc.", "@feedback_tt_toilet_cleaning_alt": {"description": "TT feedback item for toilet cleaning (alternative)", "context": "normal_review_feedback_dialogue"}, "feedback_tt_compartment_cleaning_alt": "Complete Cleaning of Passenger Compartment (Including spraying of air freshener and cleaning of dust bin)", "@feedback_tt_compartment_cleaning_alt": {"description": "TT feedback item for compartment cleaning (alternative)", "context": "normal_review_feedback_dialogue"}, "feedback_tt_janitor_behavior_alt": "Behavior of Janitors / Supervisor (Including hygiene & cleanliness of", "@feedback_tt_janitor_behavior_alt": {"description": "TT feedback item for janitor behavior (alternative)", "context": "normal_review_feedback_dialogue"}, "text_item": "ITEM", "@text_item": {"description": "Table header for item column", "context": "normal_review_feedback_dialogue"}, "msg_enter_valid_email": "Please enter a valid Email ID.", "@msg_enter_valid_email": {"description": "Validation message for email field", "context": "normal_review_feedback_dialogue"}, "msg_sending_otp": "sending otp", "@msg_sending_otp": {"description": "Loading message for sending OTP", "context": "normal_review_feedback_dialogue"}, "msg_enter_otp": "Please enter the OTP.", "@msg_enter_otp": {"description": "Validation message for OTP field", "context": "normal_review_feedback_dialogue"}, "msg_verifying_otp": "verifying otp", "@msg_verifying_otp": {"description": "Loading message for verifying OTP", "context": "normal_review_feedback_dialogue"}, "msg_updating_feedback": "Updating feedback...", "@msg_updating_feedback": {"description": "Loading message for updating feedback", "context": "normal_review_feedback_dialogue"}, "msg_failed_update_feedback": "Failed to update feedback", "@msg_failed_update_feedback": {"description": "Error message for feedback update failure", "context": "normal_review_feedback_dialogue"}, "error_playing_video": "Error playing video: ", "@error_playing_video": {"description": "Error message prefix for video playback", "context": "normal_review_feedback_dialogue"}, "error_failed_load_video": "Failed to load video", "@error_failed_load_video": {"description": "Error message for video loading failure", "context": "normal_review_feedback_dialogue"}, "upload_screen_title": "Upload JSON File", "@upload_screen_title": {"description": "AppBar title for upload screen", "context": "upload_screen"}, "error_loading_auth_state": "Error loading authentication state", "@error_loading_auth_state": {"description": "Error message when authentication state fails to load", "context": "upload_screen"}, "upload_permission_title": "Storage Permission Required", "@upload_permission_title": {"description": "Dialog title for storage permission request", "context": "upload_widget"}, "upload_permission_content": "This app needs storage permission to pick files. Please grant permission in settings.", "@upload_permission_content": {"description": "Dialog content explaining storage permission requirement", "context": "upload_widget"}, "btn_open_settings": "Open Settings", "@btn_open_settings": {"description": "Button to open app settings", "context": "upload_widget"}, "msg_select_json_file": "Please select a JSON file to submit.", "@msg_select_json_file": {"description": "SnackBar message when no file is selected", "context": "upload_widget"}, "error_json_format": "Error: Json file is not in the correct format", "@error_json_format": {"description": "Error message for invalid JSON format", "context": "upload_widget"}, "msg_selection_cleared": "Selection cleared.", "@msg_selection_cleared": {"description": "SnackBar message when file selection is cleared", "context": "upload_widget"}, "upload_title": "Upload Json Data", "@upload_title": {"description": "Main title text for upload widget", "context": "upload_widget"}, "text_selected_file": "Selected file:", "@text_selected_file": {"description": "Label for selected file display", "context": "upload_widget"}, "text_no_file_selected": "No file selected", "@text_no_file_selected": {"description": "Text when no file is selected", "context": "upload_widget"}, "text_size": "Size:", "@text_size": {"description": "Label for file size display", "context": "upload_widget"}, "btn_pick_file": "Pick File", "@btn_pick_file": {"description": "Button to pick a file", "context": "upload_widget"}, "btn_submit_file": "Submit File", "@btn_submit_file": {"description": "<PERSON><PERSON> to submit the selected file", "context": "upload_widget"}, "text_loading_train_data": "Loading train data...", "@text_loading_train_data": {"description": "Loading message for train data", "context": "assign_obhs_screen"}, "text_loading_users": "Loading users...", "@text_loading_users": {"description": "Loading message for users", "context": "assign_obhs_screen"}, "text_loading_stations": "Loading stations...", "@text_loading_stations": {"description": "Loading message for stations", "context": "assign_ehk_ca_screen"}, "error_loading_data": "Error loading data: {error}", "@error_loading_data": {"description": "Error message when loading data fails", "context": "assign_obhs_screen", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "error_loading_users": "Error loading {userType} users: {error}", "@error_loading_users": {"description": "Error message when loading users fails", "context": "assign_obhs_screen", "placeholders": {"userType": {"type": "String", "description": "The type of user being loaded"}, "error": {"type": "String", "description": "The error message"}}}, "error_loading_stations": "Error loading stations: {error}", "@error_loading_stations": {"description": "Error message when loading stations fails", "context": "assign_ehk_ca_screen", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "text_return_gap_success": "Return Gap Data added/updated successfully", "@text_return_gap_success": {"description": "Success message for return gap data", "context": "assign_ehk_ca_screen"}, "btn_update_both_trains": "Update Both Trains", "@btn_update_both_trains": {"description": "Button to update both train assignments", "context": "assign_obhs_screen"}, "appbar_assign_multiple_users": "Assign Multiple Users", "@appbar_assign_multiple_users": {"description": "AppBar title for assign multiple users screen", "context": "assign_ehk_ca_screen"}, "text_loading_default": "Loading...", "@text_loading_default": {"description": "Default loading text", "context": "assign_obhs_screen"}, "text_loading_coaches": "Loading coaches...", "@text_loading_coaches": {"description": "Loading message for coaches", "context": "assign_obhs_screen"}, "text_updating_job_chart_status": "Updating job chart status...", "@text_updating_job_chart_status": {"description": "Loading message for job chart status update", "context": "assign_obhs_screen"}, "text_update_job_chart": "Update Job Chart...", "@text_update_job_chart": {"description": "Loading message for job chart update", "context": "assign_obhs_screen"}, "error_loading_coaches": "Error loading coaches: {error}", "@error_loading_coaches": {"description": "Error message when loading coaches fails", "context": "assign_obhs_screen", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "error_loading_train_data": "Error loading train data: {error}", "@error_loading_train_data": {"description": "Error message when loading train data fails", "context": "assign_obhs_screen", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "error_generic": "Error: {error}", "@error_generic": {"description": "Generic error message", "context": "assign_obhs_screen", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "error_updating_trains": "An error occurred while updating the trains: {error}", "@error_updating_trains": {"description": "Error message when updating trains fails", "context": "assign_obhs_screen", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "msg_job_chart_status_added": "Job Chart Status Added", "@msg_job_chart_status_added": {"description": "Success message for job chart status", "context": "assign_obhs_screen"}, "text_assign_obhs": "Assign OBHS", "@text_assign_obhs": {"description": "Title for assigning OBHS", "context": "assign_obhs_screen"}, "text_upload_job_chart_image": "Upload Job Chart Image", "@text_upload_job_chart_image": {"description": "Button text for uploading job chart image", "context": "assign_obhs_screen"}, "dialog_title_message": "Message", "@dialog_title_message": {"description": "Dialog title for message", "context": "assign_obhs_screen"}, "screen_title_train_rake_deficiency_report": "Train Rake Deficiency Report", "@screen_title_train_rake_deficiency_report": {"description": "AppBar title for OBHS to MCC handover screen", "context": "obhs_to_mcc_handover_screen"}, "text_obhs_to_mcc_handover": "OBHS to MCC Handover", "@text_obhs_to_mcc_handover": {"description": "Main heading for OBHS to MCC handover screen", "context": "obhs_to_mcc_handover_screen"}, "text_please_select_train_and_date": "Please select a train and date.", "@text_please_select_train_and_date": {"description": "Instruction to select train and date", "context": "obhs_to_mcc_handover_screen"}, "text_coach": "Coach: ", "@text_coach": {"description": "Label for coach", "context": "CoachIssueImageUpload"}, "error_please_select_train_and_date_first": "Please select a train and date first", "@error_please_select_train_and_date_first": {"description": "Error message when train and date are not selected", "context": "obhs_to_mcc_handover_screen"}, "screen_title_mcc_to_obhs_handover_report": "MCC to OBHS Handover Report", "@screen_title_mcc_to_obhs_handover_report": {"description": "AppBar title for MCC to OBHS handover screen", "context": "mcc_to_obhs_handover_screen"}, "error_failed_to_load_handover_items": "Failed to load handover items: {error}", "@error_failed_to_load_handover_items": {"description": "Error message when loading handover items fails", "context": "mcc_to_obhs_handover_screen", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "screen_title_requested_users": "Requested Users", "@screen_title_requested_users": {"description": "AppBar title for requested users screen", "context": "requested_user_screen"}, "text_request_summary": "Request Summary", "@text_request_summary": {"description": "Section heading for request summary", "context": "requested_user_screen"}, "text_new_user_requests": "New User Requests", "@text_new_user_requests": {"description": "Panel title for new user requests", "context": "requested_user_screen"}, "text_update_requests": "Update Requests", "@text_update_requests": {"description": "Panel title for update requests", "context": "requested_user_screen"}, "text_no_pending_requests": "No Pending Requests", "@text_no_pending_requests": {"description": "Message when no requests are pending", "context": "requested_user_screen"}, "text_all_user_requests_processed": "All user requests have been processed", "@text_all_user_requests_processed": {"description": "Subtitle message when no requests are pending", "context": "requested_user_screen"}, "error_failed_to_fetch_update_requests": "Failed to fetch update requests: {error}", "@error_failed_to_fetch_update_requests": {"description": "Error message when fetching update requests fails", "context": "requested_user_screen", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "error_unexpected_error_occurred": "An unexpected error occurred: {error}", "@error_unexpected_error_occurred": {"description": "Generic error message for unexpected errors", "context": "requested_user_screen", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "screen_title_user_management": "User Management", "@screen_title_user_management": {"description": "AppBar title for user management screen", "context": "enable_disable_user"}, "text_admin_permission_required": "Your user type needs to be admin to perform this action: {action}", "@text_admin_permission_required": {"description": "Permission denied message for admin actions", "context": "enable_disable_user", "placeholders": {"action": {"type": "String", "description": "The action that requires admin permission"}}}, "btn_requested_users": "Requested Users", "@btn_requested_users": {"description": "Button text for requested users", "context": "enable_disable_user"}, "btn_new_user": "New User", "@btn_new_user": {"description": "Button text for new user", "context": "enable_disable_user"}, "btn_request_for_new_user": "Request For New User", "@btn_request_for_new_user": {"description": "Button text for requesting new user", "context": "enable_disable_user"}, "form_search_users": "Search users...", "@form_search_users": {"description": "Placeholder text for user search field", "context": "enable_disable_user"}, "text_username": "Username", "@text_username": {"description": "Label for username field", "context": "enable_disable_user"}, "text_phone": "Phone", "@text_phone": {"description": "Label for phone field", "context": "enable_disable_user"}, "text_status_enabled": "Enabled", "@text_status_enabled": {"description": "Status option for enabled", "context": "enable_disable_user"}, "text_status_disabled": "Disabled", "@text_status_disabled": {"description": "Status option for disabled", "context": "enable_disable_user"}, "text_status_suspended": "Suspended", "@text_status_suspended": {"description": "Status option for suspended", "context": "enable_disable_user"}, "text_status_blocked": "Blocked", "@text_status_blocked": {"description": "Status option for blocked", "context": "enable_disable_user"}, "btn_update_status": "Update Status", "@btn_update_status": {"description": "Button text for updating status", "context": "enable_disable_user"}, "btn_request_for_update_status": "Request For Update Status", "@btn_request_for_update_status": {"description": "Button text for requesting status update", "context": "enable_disable_user"}, "btn_update_user_details": "Update User Details", "@btn_update_user_details": {"description": "Button text for updating user details", "context": "enable_disable_user"}, "btn_request_for_update_user_details": "Request For Update User Details", "@btn_request_for_update_user_details": {"description": "Button text for requesting user details update", "context": "enable_disable_user"}, "error_unable_to_open_email": "Unable to open email client: {error}", "@error_unable_to_open_email": {"description": "Error message when email client cannot be opened", "context": "enable_disable_user", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_manage_issues_subissues": "Manage Issues & Subissues", "@text_manage_issues_subissues": {"description": "AppBar title for issue management screen", "context": "issue_screen"}, "btn_ac": "AC", "@btn_ac": {"description": "AC button text", "context": "normal_review_feedback_dialogue"}, "btn_non_ac": "Non-AC", "@btn_non_ac": {"description": "Non-AC button text", "context": "normal_review_feedback_dialogue"}, "btn_pantry": "Pantry", "@btn_pantry": {"description": "Pantry button text", "context": "normal_review_feedback_dialogue"}, "text_rating_excellent": "Excellent", "@text_rating_excellent": {"description": "Excellent rating label", "context": "normal_review_feedback_dialogue"}, "text_rating_very_good": "Very Good", "@text_rating_very_good": {"description": "Very Good rating label", "context": "normal_review_feedback_dialogue"}, "text_rating_good": "Good", "@text_rating_good": {"description": "Good rating label", "context": "normal_review_feedback_dialogue"}, "text_rating_average": "Average", "@text_rating_average": {"description": "Average rating label", "context": "normal_review_feedback_dialogue"}, "text_rating_poor": "Poor", "@text_rating_poor": {"description": "Poor rating label", "context": "normal_review_feedback_dialogue"}, "text_please_tick_appropriate_column": "Please tick (✓) in appropriate column", "@text_please_tick_appropriate_column": {"description": "Instruction text for rating table", "context": "normal_review_feedback_dialogue"}, "text_email_verification_spam_notice": "Please note that verification emails may sometimes be delivered to your spam/junk folder.", "@text_email_verification_spam_notice": {"description": "Email verification spam notice", "context": "normal_review_feedback_dialogue"}, "text_after_requesting_otp": "After requesting OTP:", "@text_after_requesting_otp": {"description": "After requesting OTP header", "context": "normal_review_feedback_dialogue"}, "text_check_inbox_first": "• Check your inbox first", "@text_check_inbox_first": {"description": "Check inbox instruction", "context": "normal_review_feedback_dialogue"}, "text_check_spam_folder": "• If not found, check spam/junk folder", "@text_check_spam_folder": {"description": "Check spam folder instruction", "context": "normal_review_feedback_dialogue"}, "form_feedback_hint": "Add your feedback here (max 100 Characters)...", "@form_feedback_hint": {"description": "Feedback form field hint text", "context": "normal_review_feedback_dialogue"}, "error_feedback_max_characters": "Feedback cannot exceed 100 characters", "@error_feedback_max_characters": {"description": "Feedback character limit error message", "context": "normal_review_feedback_dialogue"}, "feedback_ac_toilet_cleaning": "Cleaning of toilets (including tollet floor, commode pan, wall panels, shelf, miror, wash basin. Disinfectionnand provision of deodorant etc", "@feedback_ac_toilet_cleaning": {"description": "AC feedback item - toilet cleaning", "context": "normal_review_feedback_dialogue"}, "feedback_ac_compartment_cleaning": "Cleaning of Passenger Compartment (including cleaning or passenger aisle, Vestibule area,, Doorway area and doorway wash basin, spraying of air freshene and cleaning of dust bin)", "@feedback_ac_compartment_cleaning": {"description": "AC feedback item - compartment cleaning", "context": "normal_review_feedback_dialogue"}, "feedback_ac_garbage_collection": "Collection of gargage from the coach compartments and clearance of dustbins.", "@feedback_ac_garbage_collection": {"description": "AC feedback item - garbage collection", "context": "normal_review_feedback_dialogue"}, "feedback_ac_pest_control": "Spraying of Mosquito/Cockroach/Fly Repellent and Providing Glue Board Whenever required or on demand by passengers", "@feedback_ac_pest_control": {"description": "AC feedback item - pest control", "context": "normal_review_feedback_dialogue"}, "feedback_ac_janitor_behavior": "Behavior/Response of Janitors / Supervisor (Including hygiene & cleanliness of Janitor/", "@feedback_ac_janitor_behavior": {"description": "AC feedback item - janitor behavior", "context": "normal_review_feedback_dialogue"}, "coach_issue_reported": "Reported", "@coach_issue_reported": {"description": "Status choice for reported issues", "context": "CoachIssueImageUpload"}, "coach_issue_fixed": "Fixed", "@coach_issue_fixed": {"description": "Status choice for fixed issues", "context": "CoachIssueImageUpload"}, "coach_issue_resolved": "Resolved", "@coach_issue_resolved": {"description": "Status choice for resolved issues", "context": "CoachIssueImageUpload"}, "msg_images_videos_upload_initiated": "Images or Videos upload initiated successfully", "@msg_images_videos_upload_initiated": {"description": "Success message for upload initiation", "context": "CoachIssueImageUpload"}, "error_upload_failed_limit": "Failed to upload image or video upload, upload only upto 5 photos or 3 videos", "@error_upload_failed_limit": {"description": "Error message for upload limit exceeded", "context": "CoachIssueImageUpload"}, "error_select_image_and_issue": "Please select an image and issue to upload", "@error_select_image_and_issue": {"description": "Error message when image or issue not selected", "context": "CoachIssueImageUpload"}, "error_failed_load_issues": "Failed to load issues: {error}", "@error_failed_load_issues": {"description": "Error message for loading issues", "placeholders": {"error": {"type": "String"}}, "context": "CoachIssueImageUpload"}, "btn_issue_subissue_selected": "{issueCount} Issue & {subIssueCount} SubIssue Selected ", "@btn_issue_subissue_selected": {"description": "Button text showing selected issues count", "placeholders": {"issueCount": {"type": "int"}, "subIssueCount": {"type": "int"}}, "context": "CoachIssueImageUpload"}, "btn_select_issues": "Select Issues", "@btn_select_issues": {"description": "Button text to select issues", "context": "CoachIssueImageUpload"}, "msg_issues_saved_upload": "Issues saved upload Images/Videos", "@msg_issues_saved_upload": {"description": "Message when issues are saved", "context": "CoachIssueImageUpload"}, "text_train_number": "Train Number: ", "@text_train_number": {"description": "Label for train number", "context": "CoachIssueImageUpload"}, "text_journey_date": "Journey Date: ", "@text_journey_date": {"description": "Label for journey date", "context": "CoachIssueImageUpload"}, "text_no_images_videos_selected": "No Images/Videos selected", "@text_no_images_videos_selected": {"description": "Text when no images or videos are selected", "context": "CoachIssueImageUpload"}, "btn_pick_images_videos": "Pick Images/Videos", "@btn_pick_images_videos": {"description": "Button text to pick images or videos", "context": "CoachIssueImageUpload"}, "form_add_comments_hint": "Add your comments here (max 250 characters)...", "@form_add_comments_hint": {"description": "Hint text for comments input field", "context": "CoachIssueImageUpload"}, "text_characters": "{count} characters", "@text_characters": {"description": "Character count display", "placeholders": {"count": {"type": "int"}}, "context": "CoachIssueImageUpload"}, "error_select_image_video_upload": "Please select image/video to upload", "@error_select_image_video_upload": {"description": "Error message when no image/video selected for upload", "context": "CoachIssueImageUpload"}, "text_issue_subissue": "Issue/Subissue", "@text_issue_subissue": {"description": "Column header for issue/subissue", "context": "CoachIssueImageUpload"}, "text_submit_update": "Submit/Update", "@text_submit_update": {"description": "Column header for submit/update", "context": "CoachIssueImageUpload"}, "text_reported_by": "Reported_by", "@text_reported_by": {"description": "Column header for reported by", "context": "CoachIssueImageUpload"}, "text_fixed_by": "Fixed_by", "@text_fixed_by": {"description": "Column header for fixed by", "context": "CoachIssueImageUpload"}, "text_resolved_by": "Resolved_by", "@text_resolved_by": {"description": "Column header for resolved by", "context": "CoachIssueImageUpload"}, "text_uploaded_images_videos": "Uploaded Images/Videos", "@text_uploaded_images_videos": {"description": "Title for uploaded images/videos section", "context": "CoachIssueImageUpload"}, "btn_status_by": "{status} By", "@btn_status_by": {"description": "Button text for status action", "placeholders": {"status": {"type": "String"}}, "context": "CoachIssueImageUpload"}, "text_upload_images_videos": "Upload Images/Videos", "@text_upload_images_videos": {"description": "App bar title for upload images/videos", "context": "CoachIssueImageUpload"}}