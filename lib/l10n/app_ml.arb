{"@@locale": "ml", "appTitle": "റെയിൽഓപ്സ്", "@appTitle": {"description": "The title of the application"}, "cancel": "", "confirm": "", "delete": "", "edit": "", "error": "", "home": "", "loading": "", "login": "ലോഗിൻ", "@login": {"description": "Login button text"}, "logout": "", "next": "", "no": "", "ok": "", "password": "", "previous": "", "refresh": "", "retry": "", "save": "", "search": "", "settings": "", "submit": "", "username": "", "welcome": "റെയിൽഓപ്സിൽേക്ക് സ്വാഗതം", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "", "test_welcome": "സ്വാഗതം", "@test_welcome": {"description": "Welcome message for testing", "context": "test_context"}, "test_login": "ലോഗിൻ", "@test_login": {"description": "Login button text", "context": "button_labels"}, "text_menu": "മെനു", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "text_train_tracker": "ട്രെയിൻ ട്രാക്കർ", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "CA നിയോഗിക്കുക", "@text_assign_ca": {"description": "Text from text_widgets: Assign CA", "context": "text_widgets"}, "text_assign_cs": "CS നിയോഗിക്കുക", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR വിശദാംശങ്ങൾ", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_passenger_chart": "യാത്രക്കാരുടെ ചാർട്ട്", "@text_passenger_chart": {"description": "Text from text_widgets: Passenger Chart", "context": "text_widgets"}, "text_map_screen": "മാപ്പ് സ്ക്രീൻ", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "കോൺഫിഗറേഷൻ", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "റിപ്പോർട്ടുകൾ", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "യാത്രക്കാരുടെ ഫീഡ്ബാക്ക്", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "റേക്ക് കുറവ് റിപ്പോർട്ട്", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS ൽ നിന്ന് MCC കൈമാറ്റം", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC ൽ നിന്ന് OBHS കൈമാറ്റം", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "ഡാറ്റ അപ്‌ലോഡ് ചെയ്യുക", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "ഉപയോക്തൃ മാനേജ്മെന്റ്", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "പ്രശ്ന മാനേജ്മെന്റ്", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "റെയിൽ സാഥി QR", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "കസ്റ്റമർ കെയർ", "@text_customer_care": {"description": "Text from text_widgets: Customer Care", "context": "text_widgets"}, "text_change_mobile": "മൊബൈൽ മാറ്റുക", "@text_change_mobile": {"description": "Profile screen translation for: text_change_mobile", "context": "profile_screen"}, "text_change_whatsapp": "വാട്ട്സാപ്പ് നമ്പർ മാറ്റുക", "@text_change_whatsapp": {"description": "Profile screen translation for: text_change_whatsapp", "context": "profile_screen"}, "text_alert": "മുന്നറിയിപ്പ്", "@text_alert": {"description": "Profile screen translation for: text_alert", "context": "profile_screen"}, "text_close": "അടയ്ക്കുക", "@text_close": {"description": "Profile screen translation for: text_close", "context": "profile_screen"}, "text_change_your_email": "നിങ്ങളുടെ ഇമെയിൽ മാറ്റുക", "@text_change_your_email": {"description": "Profile screen translation for: text_change_your_email", "context": "profile_screen"}, "text_current_email": "നിലവിലെ ഇമെയിൽ", "@text_current_email": {"description": "Profile screen translation for: text_current_email", "context": "profile_screen"}, "text_new_email": "പുതിയ ഇമെയിൽ", "@text_new_email": {"description": "Profile screen translation for: text_new_email", "context": "profile_screen"}, "text_please_enter_new_email": "ദയവായി പുതിയ ഇമെയിൽ നൽകുക", "@text_please_enter_new_email": {"description": "Profile screen translation for: text_please_enter_new_email", "context": "profile_screen"}, "text_otp": "ഒടിപി", "@text_otp": {"description": "Profile screen translation for: text_otp", "context": "profile_screen"}, "text_resend_otp": "ഒടിപി വീണ്ടും അയയ്ക്കുക", "@text_resend_otp": {"description": "Profile screen translation for: text_resend_otp", "context": "profile_screen"}, "text_resend_in_seconds": "{seconds} സെക്കൻഡിൽ വീണ്ടും അയയ്ക്കുക", "@text_resend_in_seconds": {"description": "Profile screen translation for: text_resend_in_seconds", "context": "profile_screen"}, "text_verify_otp": "ഒടിപി പരിശോധിക്കുക", "@text_verify_otp": {"description": "Profile screen translation for: text_verify_otp", "context": "profile_screen"}, "text_generate_otp": "ഒടിപി സൃഷ്ടിക്കുക", "@text_generate_otp": {"description": "Profile screen translation for: text_generate_otp", "context": "profile_screen"}, "text_change_your_password": "നിങ്ങളുടെ പാസ്‌വേഡ് മാറ്റുക", "@text_change_your_password": {"description": "Profile screen translation for: text_change_your_password", "context": "profile_screen"}, "text_old_password": "പഴയ പാസ്‌വേഡ്", "@text_old_password": {"description": "Profile screen translation for: text_old_password", "context": "profile_screen"}, "text_new_password": "പുതിയ പാസ്‌വേഡ്", "@text_new_password": {"description": "Profile screen translation for: text_new_password", "context": "profile_screen"}, "text_confirm_new_password": "പുതിയ പാസ്‌വേഡ് സ്ഥിരീകരിക്കുക", "@text_confirm_new_password": {"description": "Profile screen translation for: text_confirm_new_password", "context": "profile_screen"}, "text_please_enter_otp": "ദയവായി ഒടിപി നൽകുക", "@text_please_enter_otp": {"description": "Profile screen translation for: text_please_enter_otp", "context": "profile_screen"}, "text_send_mobile_otp": "മൊബൈൽ ഒടിപി അയയ്ക്കുക", "@text_send_mobile_otp": {"description": "Profile screen translation for: text_send_mobile_otp", "context": "profile_screen"}, "text_send_email_otp": "ഇമെയിൽ ഒടിപി അയയ്ക്കുക", "@text_send_email_otp": {"description": "Profile screen translation for: text_send_email_otp", "context": "profile_screen"}, "text_please_enter_value": "ദയവായി ഒരു മൂല്യം നൽകുക", "@text_please_enter_value": {"description": "Profile screen translation for: text_please_enter_value", "context": "profile_screen"}, "text_please_enter_valid_mobile": "ദയവായി സാധുവായ മൊബൈൽ നമ്പർ നൽകുക", "@text_please_enter_valid_mobile": {"description": "Profile screen translation for: text_please_enter_valid_mobile", "context": "profile_screen"}, "text_success": "വിജയം", "@text_success": {"description": "Profile screen translation for: text_success", "context": "profile_screen"}, "text_ok": "ശരി", "@text_ok": {"description": "Profile screen translation for: text_ok", "context": "profile_screen"}, "text_change_your_mobile_number": "നിങ്ങളുടെ മൊബൈൽ നമ്പർ മാറ്റുക", "@text_change_your_mobile_number": {"description": "Profile screen translation for: text_change_your_mobile_number", "context": "profile_screen"}, "text_current_mobile_number": "നിലവിലെ മൊബൈൽ നമ്പർ", "@text_current_mobile_number": {"description": "Profile screen translation for: text_current_mobile_number", "context": "profile_screen"}, "text_new_mobile_number": "പുതിയ മൊബൈൽ നമ്പർ", "@text_new_mobile_number": {"description": "Profile screen translation for: text_new_mobile_number", "context": "profile_screen"}, "text_please_enter_new_mobile": "ദയവായി പുതിയ മൊബൈൽ നമ്പർ നൽകുക", "@text_please_enter_new_mobile": {"description": "Profile screen translation for: text_please_enter_new_mobile", "context": "profile_screen"}, "text_change_your_whatsapp_number": "നിങ്ങളുടെ വാട്ട്സാപ്പ് നമ്പർ മാറ്റുക", "@text_change_your_whatsapp_number": {"description": "Profile screen translation for: text_change_your_whatsapp_number", "context": "profile_screen"}, "text_current_whatsapp_number": "നിലവിലെ വാട്ട്സാപ്പ് നമ്പർ", "@text_current_whatsapp_number": {"description": "Profile screen translation for: text_current_whatsapp_number", "context": "profile_screen"}, "text_new_whatsapp_number": "പുതിയ വാട്ട്സാപ്പ് നമ്പർ", "@text_new_whatsapp_number": {"description": "Profile screen translation for: text_new_whatsapp_number", "context": "profile_screen"}, "text_please_enter_new_whatsapp": "ദയവായി പുതിയ വാട്ട്സാപ്പ് മൊബൈൽ നമ്പർ നൽകുക", "@text_please_enter_new_whatsapp": {"description": "Profile screen translation for: text_please_enter_new_whatsapp", "context": "profile_screen"}, "text_train": "റെയിൽ", "@text_train": {"description": "Table column header for train", "context": "add_train_screen"}, "text_coaches": "കോച്ച്", "@text_coaches": {"description": "Table column header for coaches", "context": "add_train_screen"}, "text_origin_date": "മൂല തീയതി", "@text_origin_date": {"description": "Table column header for date", "context": "add_train_screen"}, "text_na": "ലഭ്യമല്ല", "@text_na": {"description": "Table column header for na", "context": "add_train_screen"}, "text_send_otp": "OTP അയയ്ക്കുക", "@text_send_otp": {"description": "Send OTP button text", "context": "change_email_modal"}, "text_failed_to_send_otp": "OTP അയയ്ക്കുന്നതിൽ പരാജയപ്പെട്ടു: {error}", "@text_failed_to_send_otp": {"description": "Error message when OTP sending fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_email_saved_successfully": "ഇമെയിൽ വിജയകരമായി സേവ് ചെയ്തു!", "@text_email_saved_successfully": {"description": "Success message when email is saved", "context": "change_email_modal"}, "text_failed_to_verify_otp": "OTP പരിശോധിക്കുന്നതിൽ പരാജയപ്പെട്ടു: {error}", "@text_failed_to_verify_otp": {"description": "Error message when OTP verification fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_cancel": "റദ്ദാക്കുക", "@text_cancel": {"description": "Cancel button text", "context": "general"}, "rm_feedback_app_bar_title": "റെയിൽമദദ് യാത്രക്കാരുടെ ഫീഡ്ബാക്ക്", "rm_feedback_main_title": "റെയിൽമദദ് ഫീഡ്ബാക്ക്", "form_pnr_number": "PNR നമ്പർ *", "form_crn_number": "CRN നമ്പർ*", "form_train_no": "ട്രെയിൻ നമ്പർ *", "form_train_name": "ട്രെയിനിന്റെ പേര് *", "form_passenger_name": "യാത്രക്കാരന്റെ പേര് *", "form_coach_no": "കോച്ച് നമ്പർ *", "form_berth_no": "ബെർത്ത് നമ്പർ *", "form_mobile_number": "മൊബൈൽ നമ്പർ", "form_email_id": "ഇമെയിൽ ഐഡി", "form_issue_type": "പ്രശ്നത്തിന്റെ തരം", "form_sub_issue_type": "ഉപ പ്രശ്നത്തിന്റെ തരം", "form_resolved_status": "പരിഹരിച്ചു (അതെ/ഇല്ല) *", "form_marks": "മാർക്കുകൾ (1 മുതൽ 10) *", "form_task_status": "ജോലിയുടെ നില *", "form_remarks": "യാത്രക്കാരന്റെ അഭിപ്രായങ്ങൾ", "btn_validate": "പരിശോധിക്കുക", "btn_verified": "പരിശോധിച്ചു", "btn_verify_email": "ഇമെയിൽ പരിശോധിക്കുക", "btn_verify_otp": "OTP പരിശോധിക്കുക", "btn_submit_feedback": "ഫീഡ്ബാക്ക് സമർപ്പിക്കുക", "btn_upload_pnr_image": "PNR ചിത്രം അപ്‌ലോഡ് ചെയ്യുക", "btn_pick_media": "ഫീഡ്ബാക്കിനായി ചിത്രങ്ങൾ/വീഡിയോകൾ തിരഞ്ഞെടുക്കുക", "btn_camera": "ക്യാമറ", "btn_gallery": "ഗാലറി", "btn_image": "ചിത്രം", "btn_video": "വീഡിയോ", "btn_i_understand": "ഞാൻ മനസ്സിലാക്കി", "btn_ok": "ശരി", "status_verified": "പരിശോധിച്ചു", "status_pending": "തീർപ്പുകൽപ്പിക്കാത്ത", "status_completed": "പൂർത്തിയായി", "status_yes": "അതെ", "status_no": "ഇല്ല", "status_select": "തിരഞ്ഞെടുക്കുക", "section_email_verification": "ഇമെയിൽ പരിശോധന (ഓപ്ഷണൽ)", "section_selected_images": "തിരഞ്ഞെടുത്ത ചിത്രങ്ങൾ:", "section_selected_videos": "തിരഞ്ഞെടുത്ത വീഡിയോകൾ:", "dialog_email_verification_info": "ഇമെയിൽ പരിശോധന വിവരങ്ങൾ", "dialog_select_media_type": "മീഡിയ തരം തിരഞ്ഞെടുക്കുക", "validation_fill_all_fields": "ദയവായി എല്ലാ ഫീൽഡുകളും സാധുവായ വിവരങ്ങൾ ഉപയോഗിച്ച് പൂരിപ്പിക്കുക.", "validation_pnr_digits": "PNR നമ്പർ 8 അല്ലെങ്കിൽ 10 അക്കങ്ങൾ ആയിരിക്കണം", "validation_berth_number": "ബെർത്ത് നമ്പർ സാധുവായ സംഖ്യ ആയിരിക്കണം", "validation_feedback_length": "ഫീഡ്ബാക്ക് 100 അക്ഷരങ്ങൾക്ക് മുകളിൽ ആകരുത്", "validation_email_required": "ദയവായി സാധുവായ ഇമെയിൽ ഐഡി നൽകുക.", "validation_otp_required": "ദയവായി OTP നൽകുക.", "validation_train_no_required": "ട്രെയിൻ നമ്പർ ആവശ്യമാണ്", "validation_train_name_required": "ട്രെയിനിന്റെ പേര് ആവശ്യമാണ്", "validation_passenger_name_required": "യാത്രക്കാരന്റെ പേര് ആവശ്യമാണ്", "validation_mobile_required": "മൊബൈൽ നമ്പർ ആവശ്യമാണ്", "validation_mobile_digits": "മൊബൈൽ നമ്പർ 10 അക്കങ്ങൾ ആയിരിക്കണം", "validation_issue_type_required": "ദയവായി പ്രശ്നത്തിന്റെ തരം തിരഞ്ഞെടുക്കുക", "validation_sub_issue_required": "ദയവായി ഉപ-പ്രശ്നത്തിന്റെ തരം തിരഞ്ഞെടുക്കുക", "validation_resolved_required": "ദയവായി പരിഹാര നില തിരഞ്ഞെടുക്കുക", "validation_marks_required": "ദയവായി മാർക്കുകൾ തിരഞ്ഞെടുക്കുക", "msg_pnr_images_limit": "നിങ്ങൾക്ക് 3 PNR ചിത്രങ്ങൾ മാത്രമേ തിരഞ്ഞെടുക്കാൻ കഴിയൂ", "msg_feedback_images_limit": "പരമാവധി 3 ഫീഡ്ബാക്ക് ചിത്രങ്ങൾക്ക് അനുമതിയുണ്ട്", "msg_images_added_limit": "വെറും {count} ചിത്രങ്ങൾ ചേർത്തു. പരമാവധി 3 എന്ന പരിധി എത്തി.", "msg_error_picking_media": "മീഡിയ തിരഞ്ഞെടുക്കുന്നതിൽ പിശക്: {error}", "msg_failed_fetch_train_name": "ട്രെയിനിന്റെ പേര് കൊണ്ടുവരുന്നതിൽ പരാജയപ്പെട്ടു", "msg_invalid_pnr": "അസാധുവായ PNR നമ്പർ.", "msg_pnr_success": "PNR വിശദാംശങ്ങൾ വിജയകരമായി കൊണ്ടുവന്നു.", "msg_pnr_validation_failed": "PNR വിശദാംശങ്ങൾ പരിശോധിക്കുന്നതിൽ പരാജയപ്പെട്ടു. അസാധുവായ PNR നമ്പർ.", "msg_email_verification_sent": "ഇമെയിൽ പരിശോധന ആരംഭിച്ചു. ദയവായി നിങ്ങളുടെ ഇൻബോക്സും സ്പാം ഫോൾഡറും പരിശോധിക്കുക..", "msg_otp_verified": "OTP വിജയകരമായി പരിശോധിച്ചു.", "msg_feedback_submitted": "ഫീഡ്ബാക്ക് വിജയകരമായി സമർപ്പിച്ചു!", "msg_feedback_failed": "ഫീഡ്ബാക്ക് സമർപ്പിക്കുന്നതിൽ പരാജയപ്പെട്ടു", "msg_unexpected_error": "അപ്രതീക്ഷിത പിശക് സംഭവിച്ചു. ദയവായി വീണ്ടും ശ്രമിക്കുക.", "info_spam_folder_note": "ദയവായി ശ്രദ്ധിക്കുക പരിശോധന ഇമെയിലുകൾ ചിലപ്പോൾ നിങ്ങളുടെ സ്പാം/ജങ്ക് ഫോൾഡറിൽ എത്തിയേക്കാം.", "info_after_requesting_otp": "OTP അഭ്യർത്ഥിച്ചതിന് ശേഷം:", "info_check_inbox": "ആദ്യം നിങ്ങളുടെ ഇൻബോക്സ് പരിശോധിക്കുക", "info_check_spam": "കണ്ടെത്താനായില്ലെങ്കിൽ, സ്പാം/ജങ്ക് ഫോൾഡർ പരിശോധിക്കുക", "info_add_safe_sender": "ഞങ്ങളുടെ ഡൊമെയിൻ നിങ്ങളുടെ സുരക്ഷിത അയയ്ക്കുന്നവരുടെ പട്ടികയിൽ ചേർക്കുക", "text_no_feedback_images": "ഫീഡ്ബാക്ക് ചിത്രങ്ങളൊന്നും തിരഞ്ഞെടുത്തിട്ടില്ല", "text_no_pnr_images": "PNR ചിത്രങ്ങളൊന്നും തിരഞ്ഞെടുത്തിട്ടില്ല", "text_character_count": "{count}/100 അക്ഷരങ്ങൾ", "loading_sending_otp": "OTP അയയ്ക്കുന്നു", "loading_verifying_otp": "OTP പരിശോധിക്കുന്നു", "loading_submitting_feedback": "ഫീഡ്ബാക്ക് സമർപ്പിക്കുന്നു", "attendance_api_summary": "API സംഗ്രഹം വിശദാംശങ്ങൾ", "@attendance_api_summary": {"description": "Attendance: API Summary Details", "context": "attendance"}, "attendance_assigned_coaches": "🚆 അസൈൻ ചെയ്തു Coaches:", "@attendance_assigned_coaches": {"description": "Attendance: 🚆 Assigned Coaches:", "context": "attendance"}, "attendance_available_label": "ലഭ്യമാണ്: {count}", "@attendance_available_label": {"description": "Attendance: Available: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_available_section": "ലഭ്യമാണ്", "@attendance_available_section": {"description": "Attendance: Available", "context": "attendance"}, "attendance_berths_count": "{count} berths", "@attendance_berths_count": {"description": "Attendance: {count} berths", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_buffer_time_restriction": "The സമയം between the current സമയം and the ട്രെയിൻ's arrival സമയം is not within the 2-hour buffer.", "@attendance_buffer_time_restriction": {"description": "Attendance: The time between the current time and the train's arrival time is not within the 2-hour buffer.", "context": "attendance"}, "attendance_cancel": "റദ്ദാക്കുക", "@attendance_cancel": {"description": "Attendance: Cancel", "context": "attendance"}, "attendance_cancelled": "Cancelled", "@attendance_cancelled": {"description": "Attendance: Cancelled", "context": "attendance"}, "attendance_chart_not_prepared": "ചാര്‍ട്ട് has not been prepared for this സ്റ്റേഷൻ", "@attendance_chart_not_prepared": {"description": "Attendance: Chart has not been prepared for this station", "context": "attendance"}, "attendance_charting_refreshed": "ചാര്‍ട്ടിംഗ് refreshed at: {time}", "@attendance_charting_refreshed": {"description": "Attendance: Charting refreshed at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_started": "ചാര്‍ട്ടിംഗ് started at: {time}", "@attendance_charting_started": {"description": "Attendance: Charting started at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_time": "ചാര്‍ട്ടിംഗ് സമയം: {time}", "@attendance_charting_time": {"description": "Attendance: Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_click_more": "click for more...", "@attendance_click_more": {"description": "Attendance: click for more...", "context": "attendance"}, "attendance_coach_label": "🚃 കോച്ച്: {coach}", "@attendance_coach_label": {"description": "Attendance: 🚃 Coach: {coach}", "context": "attendance", "placeholders": {"coach": {"type": "String"}}}, "attendance_coach_occupancy": "കോച്ച് Occupancy വിശദാംശങ്ങൾ", "@attendance_coach_occupancy": {"description": "Attendance: Coach Occupancy <PERSON>", "context": "attendance"}, "attendance_coach_type": "കോച്ച് Type:", "@attendance_coach_type": {"description": "Attendance: Coach Type:", "context": "attendance"}, "attendance_coaches": "Coaches: {coaches}", "@attendance_coaches": {"description": "Attendance: Coaches: {coaches}", "context": "attendance", "placeholders": {"coaches": {"type": "String"}}}, "attendance_concise_view": "Concise കാണുക", "@attendance_concise_view": {"description": "Attendance: Concise View", "context": "attendance"}, "attendance_count_label": "A: {count}", "@attendance_count_label": {"description": "Attendance: A: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_daily": "Daily", "@attendance_daily": {"description": "Attendance: Daily", "context": "attendance"}, "attendance_data_refreshed": "ഡാറ്റ refreshed വിജയകരമായി", "@attendance_data_refreshed": {"description": "Attendance: Data refreshed successfully", "context": "attendance"}, "attendance_deboarding": "🔴 ഇറങ്ങുക:", "@attendance_deboarding": {"description": "Attendance: 🔴 Deboarding:", "context": "attendance"}, "attendance_deboarding_none": "🔴 ഇറങ്ങുക: None", "@attendance_deboarding_none": {"description": "Attendance: 🔴 Deboarding: None", "context": "attendance"}, "attendance_detailed_view": "Detailed കാണുക", "@attendance_detailed_view": {"description": "Attendance: Detailed View", "context": "attendance"}, "attendance_ehk_assigned": "EHK അസൈൻ ചെയ്തു for ട്രെയിൻ:{ehkName}", "@attendance_ehk_assigned": {"description": "Attendance: EHK Assigned for train:{ehkName}", "context": "attendance", "placeholders": {"ehkName": {"type": "String"}}}, "attendance_expected_charting": "Expected ചാര്‍ട്ടിംഗ് സമയം: {time}", "@attendance_expected_charting": {"description": "Attendance: Expected Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_failed_load_data": "പരാജയപ്പെട്ടു to load detailed ഡാറ്റ: {error}", "@attendance_failed_load_data": {"description": "Attendance: Failed to load detailed data: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_failed_update_status": "പരാജയപ്പെട്ടു to അപ്ഡേറ്റ് ചെയ്യുക ട്രെയിൻ നില", "@attendance_failed_update_status": {"description": "Attendance: Failed to update train status", "context": "attendance"}, "attendance_go": "പോകുക", "@attendance_go": {"description": "Attendance: Go", "context": "attendance"}, "attendance_in_route": "In-route", "@attendance_in_route": {"description": "Attendance: In-route", "context": "attendance"}, "attendance_inside": "അകത്ത്", "@attendance_inside": {"description": "Attendance: Inside", "context": "attendance"}, "attendance_inside_train": "You are now അടയാളപ്പെടുത്തി as അകത്ത് the ട്രെയിൻ", "@attendance_inside_train": {"description": "Attendance: You are now marked as inside the train", "context": "attendance"}, "attendance_journey_status_updated": "യാത്ര നില updated to {status}", "@attendance_journey_status_updated": {"description": "Attendance: Journey status updated to {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_last_fetched": "Last fetched: {time}", "@attendance_last_fetched": {"description": "Attendance: Last fetched: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_loading": "ലോഡ് ചെയ്യുന്നു...", "@attendance_loading": {"description": "Attendance: Loading...", "context": "attendance"}, "attendance_location_not_fetched": "ട്രെയിൻ സ്ഥലം is not fetched yet, ദയവായി try again later", "@attendance_location_not_fetched": {"description": "Attendance: Train Location is not fetched yet, please try again later", "context": "attendance"}, "attendance_na": "N/A", "@attendance_na": {"description": "Attendance: N/A", "context": "attendance"}, "attendance_near_stations": "You're near the following സ്റ്റേഷൻ(s):", "@attendance_near_stations": {"description": "Attendance: You're near the following station(s):", "context": "attendance"}, "attendance_nearby_station_alert": "🛤️ അടുത്തുള്ള സ്റ്റേഷൻ മുന്നറിയിപ്പ്", "@attendance_nearby_station_alert": {"description": "Attendance: 🛤️ Nearby Station Alert", "context": "attendance"}, "attendance_non_sleeper": "Non-Sleeper", "@attendance_non_sleeper": {"description": "Attendance: Non-Sleeper", "context": "attendance"}, "attendance_not_attendance_station": "ഹാജർ cannot be അടയാളപ്പെടുത്തി for സ്റ്റേഷൻ {stationCode} as it is not an ഹാജർ സ്റ്റേഷൻ.", "@attendance_not_attendance_station": {"description": "Attendance: Attendance cannot be marked for station {stationCode} as it is not an attendance station.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_not_inside_train": "You are not അകത്ത് the ട്രെയിൻ. ദയവായി പോകുക അകത്ത് the ട്രെയിൻ first.", "@attendance_not_inside_train": {"description": "Attendance: You are not inside the train. Please go inside the train first.", "context": "attendance"}, "attendance_off_label": "Off: {count}", "@attendance_off_label": {"description": "Attendance: Off: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_offboarding_section": "Offboarding", "@attendance_offboarding_section": {"description": "Attendance: Offboarding", "context": "attendance"}, "attendance_ok": "ശരി", "@attendance_ok": {"description": "Attendance: OK", "context": "attendance"}, "attendance_on_label": "On: {count}", "@attendance_on_label": {"description": "Attendance: On: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_onboarding": "🟢 കയറുക:", "@attendance_onboarding": {"description": "Attendance: 🟢 Onboarding:", "context": "attendance"}, "attendance_onboarding_none": "🟢 കയറുക: None", "@attendance_onboarding_none": {"description": "Attendance: 🟢 Onboarding: None", "context": "attendance"}, "attendance_onboarding_section": "കയറുക", "@attendance_onboarding_section": {"description": "Attendance: Onboarding", "context": "attendance"}, "attendance_other_ca": "Other CA", "@attendance_other_ca": {"description": "Attendance: Other CA", "context": "attendance"}, "attendance_other_ehk": "Other EHK/OBHS", "@attendance_other_ehk": {"description": "Attendance: Other EHK/OBHS", "context": "attendance"}, "attendance_outside_train": "You are now അടയാളപ്പെടുത്തി as പുറത്ത് the ട്രെയിൻ", "@attendance_outside_train": {"description": "Attendance: You are now marked as outside the train", "context": "attendance"}, "attendance_passenger_chart": "യാത്രക്കാരൻ ചാര്‍ട്ട്   Atten..", "@attendance_passenger_chart": {"description": "Attendance: Passenger Chart   Atten..", "context": "attendance"}, "attendance_refresh_failed": "റിഫ്രഷ് പരാജയപ്പെട്ടു: {error}", "@attendance_refresh_failed": {"description": "Attendance: Refresh failed: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_screen_title": "ഹാജർ", "@attendance_screen_title": {"description": "Attendance: Attendance", "context": "attendance"}, "attendance_select_date": "തിരഞ്ഞെടുക്കുക തീയതി", "@attendance_select_date": {"description": "Attendance: Select date", "context": "attendance"}, "attendance_select_train_date": "ദയവായി തിരഞ്ഞെടുക്കുക a ട്രെയിൻ നമ്പർ and തീയതി.", "@attendance_select_train_date": {"description": "Attendance: Please select a train number and date.", "context": "attendance"}, "attendance_select_train_first": "ദയവായി തിരഞ്ഞെടുക്കുക a ട്രെയിൻ first", "@attendance_select_train_first": {"description": "Attendance: Please select a train first", "context": "attendance"}, "attendance_self": "Self", "@attendance_self": {"description": "Attendance: Self", "context": "attendance"}, "attendance_sleeper": "Sleeper", "@attendance_sleeper": {"description": "Attendance: Sleeper", "context": "attendance"}, "attendance_station_details": "സ്റ്റേഷൻ വിശദാംശങ്ങൾ - {stationCode}", "@attendance_station_details": {"description": "Attendance: Station Details - {stationCode}", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_stoppages": "Stoppages", "@attendance_stoppages": {"description": "Attendance: Stoppages", "context": "attendance"}, "attendance_timings": "Tim<PERSON>", "@attendance_timings": {"description": "Attendance: Timings", "context": "attendance"}, "attendance_today": "Today", "@attendance_today": {"description": "Attendance: Today", "context": "attendance"}, "attendance_too_far_from_station": "You're over 50 KM away from the selected സ്റ്റേഷൻ {stationCode}. ഹാജർ can only be അടയാളപ്പെടുത്തി when you're within the allowed range.", "@attendance_too_far_from_station": {"description": "Attendance: You're over 50 KM away from the selected station {stationCode}. Attendance can only be marked when you're within the allowed range.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_train": "ട്രെയിൻ", "@attendance_train": {"description": "Attendance: Train", "context": "attendance"}, "attendance_train_date": "ട്രെയിൻ {trainNo} - {date}", "@attendance_train_date": {"description": "Attendance: Train {trainNo} - {date}", "context": "attendance", "placeholders": {"trainNo": {"type": "String"}, "date": {"type": "String"}}}, "attendance_train_depot": "ട്രെയിൻ ഡിപ്പോ:{depot}", "@attendance_train_depot": {"description": "Attendance: Train Depot:{depot}", "context": "attendance", "placeholders": {"depot": {"type": "String"}}}, "attendance_train_not_running": "ട്രെയിൻ Not Running", "@attendance_train_not_running": {"description": "Attendance: Train Not Running", "context": "attendance"}, "attendance_train_not_running_message": "ട്രെയിൻ {trainNumber} is NOT running on {dayOfWeek}", "@attendance_train_not_running_message": {"description": "Attendance: Train {trainNumber} is NOT running on {dayOfWeek}", "context": "attendance", "placeholders": {"trainNumber": {"type": "String"}, "dayOfWeek": {"type": "String"}}}, "attendance_update": "അപ്ഡേറ്റ് ചെയ്യുക", "@attendance_update": {"description": "Attendance: Update", "context": "attendance"}, "attendance_update_journey_status": "അപ്ഡേറ്റ് ചെയ്യുക യാത്ര നില", "@attendance_update_journey_status": {"description": "Attendance: Update Journey Status", "context": "attendance"}, "attendance_update_message": "A new പതിപ്പ് of the app is ലഭ്യമാണ്. You must അപ്ഡേറ്റ് ചെയ്യുക to continue using the app.", "@attendance_update_message": {"description": "Attendance: A new version of the app is available. You must update to continue using the app.", "context": "attendance"}, "attendance_update_now": "അപ്ഡേറ്റ് ചെയ്യുക Now", "@attendance_update_now": {"description": "Attendance: Update Now", "context": "attendance"}, "attendance_update_required": "അപ്ഡേറ്റ് ചെയ്യുക ആവശ്യമാണ്", "@attendance_update_required": {"description": "Attendance: Update Required", "context": "attendance"}, "attendance_user_location": "ഉപയോക്താവ് സ്ഥലം:{locationStatus}", "@attendance_user_location": {"description": "Attendance: User Location:{locationStatus}", "context": "attendance", "placeholders": {"locationStatus": {"type": "String"}}}, "attendance_details_distance": "ദൂരം: {distance} km", "@attendance_details_distance": {"description": "Attendance: Distance: {distance} km", "context": "attendance", "placeholders": {"distance": {"type": "String"}}}, "attendance_details_error": "പിശക്: {error}", "@attendance_details_error": {"description": "Attendance: Error: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_details_journey_date": "യാത്ര തീയതി:", "@attendance_details_journey_date": {"description": "Attendance: Journey Date:", "context": "attendance"}, "attendance_details_latitude": "latitude: {latitude}", "@attendance_details_latitude": {"description": "Attendance: latitude: {latitude}", "context": "attendance", "placeholders": {"latitude": {"type": "String"}}}, "attendance_details_longitude": "longitude: {longitude}", "@attendance_details_longitude": {"description": "Attendance: longitude: {longitude}", "context": "attendance", "placeholders": {"longitude": {"type": "String"}}}, "attendance_details_match_percentage": "Match ശതമാനം: {percentage}", "@attendance_details_match_percentage": {"description": "Attendance: Match Percentage: {percentage}", "context": "attendance", "placeholders": {"percentage": {"type": "String"}}}, "attendance_details_nearest_station": "nearest സ്റ്റേഷൻ: {station}", "@attendance_details_nearest_station": {"description": "Attendance: nearest Station: {station}", "context": "attendance", "placeholders": {"station": {"type": "String"}}}, "attendance_details_no_attendance": "No ഹാജർ found.", "@attendance_details_no_attendance": {"description": "Attendance: No attendance found.", "context": "attendance"}, "attendance_details_no_data": "No ഡാറ്റ ലഭ്യമാണ്.", "@attendance_details_no_data": {"description": "Attendance: No data available.", "context": "attendance"}, "attendance_details_no_human_detected": "No human detected", "@attendance_details_no_human_detected": {"description": "Attendance: No human detected", "context": "attendance"}, "attendance_details_station_code": "സ്റ്റേഷൻ Code:", "@attendance_details_station_code": {"description": "Attendance: Station Code:", "context": "attendance"}, "attendance_details_status": "നില: {status}", "@attendance_details_status": {"description": "Attendance: Status: {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_details_status_marked": "അടയാളപ്പെടുത്തി", "@attendance_details_status_marked": {"description": "Attendance: Marked", "context": "attendance"}, "attendance_details_status_pending": "Pending", "@attendance_details_status_pending": {"description": "Attendance: Pending", "context": "attendance"}, "attendance_details_title": "ഹാജർ വിശദാംശങ്ങൾ", "@attendance_details_title": {"description": "Attendance: Attendance Details", "context": "attendance"}, "attendance_details_train_number": "ട്രെയിൻ നമ്പർ:", "@attendance_details_train_number": {"description": "Attendance: Train Number:", "context": "attendance"}, "attendance_details_updated": "All വിശദാംശങ്ങൾ updated വിജയകരമായി.", "@attendance_details_updated": {"description": "Attendance: All details updated successfully.", "context": "attendance"}, "attendance_details_updated_at": "Updated At: {updatedAt}", "@attendance_details_updated_at": {"description": "Attendance: Updated At: {updatedAt}", "context": "attendance", "placeholders": {"updatedAt": {"type": "String"}}}, "attendance_details_updated_by": "Updated By: {updatedBy}", "@attendance_details_updated_by": {"description": "Attendance: Updated By: {updatedBy}", "context": "attendance", "placeholders": {"updatedBy": {"type": "String"}}}, "attendance_details_username": "Username: {username}", "@attendance_details_username": {"description": "Attendance: Username: {username}", "context": "attendance", "placeholders": {"username": {"type": "String"}}}, "btn_no_attendance_found": "No ഹാജർ found.", "@btn_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_attendance_already_submitted": "ഹാജർ Already Submitted", "@text_attendance_already_submitted": {"description": "Attendance: Attendance Already Submitted", "context": "attendance"}, "text_attendance_marked_successfully": "ഹാജർ അടയാളപ്പെടുത്തി വിജയകരമായി!", "@text_attendance_marked_successfully": {"description": "Attendance: Attendance marked successfully!", "context": "attendance"}, "text_no_attendance_found": "No ഹാജർ found.", "@text_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_welcome_to_railops": "റെയിൽഓപ്സിൽേക്ക് സ്വാഗതം", "@text_welcome_to_railops": {"description": "Welcome message on login screen", "context": "login_screen"}, "text_sign_up_to_railops": "റെയിൽഓപ്സിൽ സൈൻ അപ്പ് ചെയ്യുക", "@text_sign_up_to_railops": {"description": "Sign up screen title", "context": "sign_up_screen"}, "text_login_using_fingerprint": "വിരലടയാളം ഉപയോഗിച്ച് ലോഗിൻ ചെയ്യുക", "@text_login_using_fingerprint": {"description": "Biometric authentication prompt", "context": "authentication"}, "text_logging_in_please_wait": "ലോഗിൻ ചെയ്യുന്നു... ദയവായി കാത്തിരിക്കുക।", "@text_logging_in_please_wait": {"description": "Loading message during login", "context": "authentication"}, "text_log_in": "ലോഗ് ഇൻ", "@text_log_in": {"description": "Login button text", "context": "authentication"}, "text_home": "ഹോം", "@text_home": {"description": "Home navigation title", "context": "navigation"}, "text_home_screen": "ഹോം സ്ക്രീൻ", "@text_home_screen": {"description": "Home screen title text", "context": "home_screen"}, "@form_mobile_number": {"description": "Mobile number field label", "context": "login_form"}, "form_password": "പാസ്‌വേഡ് *", "@form_password": {"description": "Password field label", "context": "login_form"}, "error_enter_mobile_number": "ദയവായി നിങ്ങളുടെ മൊബൈൽ നമ്പർ നൽകുക", "@error_enter_mobile_number": {"description": "Mobile number validation error", "context": "login_form"}, "error_mobile_number_digits": "മൊബൈൽ നമ്പർ 10 അക്കങ്ങൾ ആയിരിക്കണം", "@error_mobile_number_digits": {"description": "Mobile number length validation error", "context": "login_form"}, "error_enter_password": "ദയവായി നിങ്ങളുടെ പാസ്‌വേഡ് നൽകുക", "@error_enter_password": {"description": "Password validation error", "context": "login_form"}, "btn_log_in_with_mobile": "മൊബൈൽ നമ്പർ ഉപയോഗിച്ച് ലോഗ് ഇൻ ചെയ്യുക", "@btn_log_in_with_mobile": {"description": "Mobile login button text", "context": "login_form"}, "btn_new_user_sign_up": "പുതിയ ഉപയോക്താവ്? ഇവിടെ സൈൻ അപ്പ് ചെയ്യുക", "@btn_new_user_sign_up": {"description": "Sign up button text", "context": "login_form"}, "text_privacy_policy": "സ്വകാര്യതാ നയം", "@text_privacy_policy": {"description": "Privacy policy link text", "context": "login_form"}, "text_terms_conditions": "നിബന്ധനകളും വ്യവസ്ഥകളും", "@text_terms_conditions": {"description": "Terms and conditions link text", "context": "login_form"}, "text_know_about_app": "ഈ ആപ്പിനെക്കുറിച്ച് അറിയുക", "@text_know_about_app": {"description": "App information link text", "context": "login_form"}, "msg_login_successful": "ലോഗിൻ വിജയകരം", "@msg_login_successful": {"description": "Login success message", "context": "authentication"}, "msg_invalid_pin": "അസാധുവായ പിൻ", "@msg_invalid_pin": {"description": "Invalid PIN error message", "context": "authentication"}, "form_first_name_required": "ആദ്യ പേര് *", "@form_first_name_required": {"description": "Signup form field: form_first_name_required", "context": "signup_form"}, "form_first_name_hint": "നിങ്ങളുടെ ആദ്യ പേര് നൽകുക", "@form_first_name_hint": {"description": "Signup form field: form_first_name_hint", "context": "signup_form"}, "form_middle_name_optional": "മധ്യ പേര് (ഓപ്ഷണൽ)", "@form_middle_name_optional": {"description": "Signup form field: form_middle_name_optional", "context": "signup_form"}, "form_middle_name_hint": "നിങ്ങളുടെ മധ്യ പേര് നൽകുക", "@form_middle_name_hint": {"description": "Signup form field: form_middle_name_hint", "context": "signup_form"}, "form_last_name_required": "അവസാന പേര് *", "@form_last_name_required": {"description": "Signup form field: form_last_name_required", "context": "signup_form"}, "form_last_name_hint": "നിങ്ങളുടെ അവസാന പേര് നൽകുക", "@form_last_name_hint": {"description": "Signup form field: form_last_name_hint", "context": "signup_form"}, "form_whatsapp_number": "വാട്സാപ്പ് നമ്പർ", "@form_whatsapp_number": {"description": "Signup form field: form_whatsapp_number", "context": "signup_form"}, "form_whatsapp_hint": "10 അക്ക വാട്സാപ്പ് നമ്പർ നൽകുക", "@form_whatsapp_hint": {"description": "Signup form field: form_whatsapp_hint", "context": "signup_form"}, "form_secondary_phone_optional": "രണ്ടാമത്തെ ഫോൺ നമ്പർ (ഓപ്ഷണൽ)", "@form_secondary_phone_optional": {"description": "Signup form field: form_secondary_phone_optional", "context": "signup_form"}, "form_secondary_phone_hint": "10 അക്ക രണ്ടാമത്തെ ഫോൺ നമ്പർ നൽകുക", "@form_secondary_phone_hint": {"description": "Signup form field: form_secondary_phone_hint", "context": "signup_form"}, "btn_no_email": "എനിക്ക് ഇമെയിൽ ഇല്ല", "@btn_no_email": {"description": "Signup button: btn_no_email", "context": "signup_form"}, "btn_request_signup": "സൈൻ അപ്പിനുള്ള അഭ്യർത്ഥന", "@btn_request_signup": {"description": "Signup button: btn_request_signup", "context": "signup_form"}, "btn_already_have_account": "ഇതിനകം അക്കൗണ്ട് ഉണ്ടോ? ലോഗിൻ ചെയ്യുക", "@btn_already_have_account": {"description": "Signup button: btn_already_have_account", "context": "signup_form"}, "text_whatsapp_same_as_phone": "വാട്സാപ്പ് നമ്പർ ഫോൺ നമ്പറിന് സമാനമാണ്", "@text_whatsapp_same_as_phone": {"description": "Signup text: text_whatsapp_same_as_phone", "context": "signup_form"}, "text_use_same_whatsapp": "വാട്സാപ്പിനായി അതേ നമ്പർ ഉപയോഗിക്കണോ?", "@text_use_same_whatsapp": {"description": "Signup text: text_use_same_whatsapp", "context": "signup_form"}, "text_verified": "പരിശോധിച്ചു", "@text_verified": {"description": "Signup text: text_verified", "context": "signup_form"}, "msg_submitting_data": "ഡാറ്റ സമർപ്പിക്കുന്നു. ദയവായി കാത്തിരിക്കുക!", "@msg_submitting_data": {"description": "Signup message: msg_submitting_data", "context": "signup_form"}, "msg_success": "വിജയം", "@msg_success": {"description": "Signup message: msg_success", "context": "signup_form"}, "msg_error": "പിശക്", "@msg_error": {"description": "Signup message: msg_error", "context": "signup_form"}, "msg_form_incomplete": "ഫോം അപൂർണ്ണം", "@msg_form_incomplete": {"description": "Signup form field: msg_form_incomplete", "context": "signup_form"}, "msg_complete_required_fields": "ദയവായി എല്ലാ ആവശ്യമായ ഫീൽഡുകളും ശരിയായി പൂരിപ്പിക്കുക", "@msg_complete_required_fields": {"description": "Signup message: msg_complete_required_fields", "context": "signup_form"}, "msg_information": "വിവരങ്ങൾ", "@msg_information": {"description": "Signup message: msg_information", "context": "signup_form"}, "msg_complete_fields_order": "ദയവായി എല്ലാ ഫീൽഡുകളും ക്രമത്തിൽ പൂരിപ്പിക്കുക. ഓരോ ഫീൽഡും മുമ്പത്തെ ഫീൽഡ് ശരിയായി പൂരിപ്പിച്ച് പരിശോധിച്ചതിന് ശേഷം മാത്രമേ പ്രവർത്തനക്ഷമമാകൂ.", "@msg_complete_fields_order": {"description": "Signup message: msg_complete_fields_order", "context": "signup_form"}, "error_enter_first_name": "ദയവായി നിങ്ങളുടെ ആദ്യ പേര് നൽകുക", "@error_enter_first_name": {"description": "Signup error: error_enter_first_name", "context": "signup_error"}, "error_enter_last_name": "ദയവായി നിങ്ങളുടെ അവസാന പേര് നൽകുക", "@error_enter_last_name": {"description": "Signup error: error_enter_last_name", "context": "signup_error"}, "error_enter_whatsapp": "ദയവായി വാട്സാപ്പ് നമ്പർ നൽകുക", "@error_enter_whatsapp": {"description": "Signup error: error_enter_whatsapp", "context": "signup_error"}, "error_whatsapp_10_digits": "വാട്സാപ്പ് നമ്പർ കൃത്യമായി 10 അക്കങ്ങൾ ആയിരിക്കണം", "@error_whatsapp_10_digits": {"description": "Signup error: error_whatsapp_10_digits", "context": "signup_error"}, "error_enter_only_numbers": "ദയവായി സംഖ്യകൾ മാത്രം നൽകുക", "@error_enter_only_numbers": {"description": "Signup error: error_enter_only_numbers", "context": "signup_error"}, "error_secondary_phone_10_digits": "രണ്ടാമത്തെ ഫോൺ നമ്പർ കൃത്യമായി 10 അക്കങ്ങൾ ആയിരിക്കണം", "@error_secondary_phone_10_digits": {"description": "Signup error: error_secondary_phone_10_digits", "context": "signup_error"}, "error_phone_numbers_different": "ഫോൺ നമ്പറും രണ്ടാമത്തെ ഫോൺ നമ്പറും വ്യത്യസ്തമായിരിക്കണം", "@error_phone_numbers_different": {"description": "Signup error: error_phone_numbers_different", "context": "signup_error"}, "error_request_error": "അഭ്യർത്ഥന പിശക്", "@error_request_error": {"description": "Signup error: error_request_error", "context": "signup_error"}, "error_phone_number": "ഫോൺ നമ്പർ", "@error_phone_number": {"description": "Signup error: error_phone_number", "context": "signup_error"}, "error_whatsapp_number": "വാട്സാപ്പ് നമ്പർ", "@error_whatsapp_number": {"description": "Signup error: error_whatsapp_number", "context": "signup_error"}, "error_email_id": "ഇമെയിൽ ഐഡി", "@error_email_id": {"description": "Signup error: error_email_id", "context": "signup_error"}, "error_emp_number": "ജീവനക്കാരൻ നമ്പർ", "@error_emp_number": {"description": "Signup error: error_emp_number", "context": "signup_error"}, "error_update_marked_info": "ദയവായി ചുവന്ന ക്രോസ് കൊണ്ട് അടയാളപ്പെടുത്തിയ വിവരങ്ങൾ അപ്ഡേറ്റ് ചെയ്ത് വീണ്ടും ശ്രമിക്കുക.", "@error_update_marked_info": {"description": "Signup error: error_update_marked_info", "context": "signup_error"}, "status_new": "പുതിയ", "@status_new": {"description": "Signup status: status_new", "context": "signup_error"}, "status_already_taken": "ഇതിനകം എടുത്തു", "@status_already_taken": {"description": "Signup status: status_already_taken", "context": "signup_error"}, "status_already_requested": "ഇതിനകം അഭ്യർത്ഥിച്ചു", "@status_already_requested": {"description": "Signup status: status_already_requested", "context": "signup_error"}, "status_unknown": "അജ്ഞാതം", "@status_unknown": {"description": "Signup status: status_unknown", "context": "signup_error"}}