import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/services/trip_report_services/trip_report_services.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';

class IssueScreen extends StatefulWidget {
  @override
  _IssueScreenState createState() => _IssueScreenState();
}

class _IssueScreenState extends State<IssueScreen>
    with SingleTickerProviderStateMixin {
  List<dynamic> issues = [];
  dynamic selectedIssue;
  dynamic selectedSubIssue;
  TextEditingController issueController = TextEditingController();
  TextEditingController subIssueController = TextEditingController();
  bool isLoading = true;
  bool isEditing = false;
  int? editingIssueId;
  int? editingSubIssueId;
  TabController? _tabController;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    fetchIssues();
  }

  @override
  void dispose() {
    _tabController?.dispose();
    issueController.dispose();
    subIssueController.dispose();
    super.dispose();
  }

  // ✅ Fetch all Issues
  Future<void> fetchIssues() async {
    setState(() {
      isLoading = true;
    });

    final response = await TripReportServices.getAllIssues();

    if (response != null) {
      setState(() {
        issues = response;
        isLoading = false;
      });
    } else {
      setState(() {
        isLoading = false;
      });
      _showErrorSnackBar('Failed to load issues');
    }
  }

  Future<void> addIssue() async {
    if (!_formKey.currentState!.validate()) return;

    final token = Provider.of<UserModel>(context, listen: false).token;
    if (token == null) {
      _showErrorSnackBar('Authentication required. Please log in.');
      return;
    }
    print(token);

    setState(() {
      isLoading = true;
    });

    try {
      final response = await TripReportServices.addIssues(
        issueController.text,
        token,
      );
      if (response != null) {
        issueController.clear();
        fetchIssues();
        _showSuccessSnackBar(response['message']);
      } else {
        setState(() {
          isLoading = false;
        });
        _showErrorSnackBar('Failed to add issue');
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      _showErrorSnackBar('Error: $e');
    }
  }

  Future<void> updateIssue() async {
    if (!_formKey.currentState!.validate() || editingIssueId == null) return;

    final token = Provider.of<UserModel>(context, listen: false).token;
    if (token == null) {
      _showErrorSnackBar('Authentication required. Please log in.');
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final response = await TripReportServices.updateIssue(
        issueController.text,
        editingIssueId!,
        token,
      );

      if (response != null) {
        setState(() {
          isEditing = false;
          editingIssueId = null;
          issueController.clear();
        });
        fetchIssues();
        _showSuccessSnackBar(response['message']);
      } else {
        setState(() {
          isLoading = false;
        });
        _showErrorSnackBar('Failed to update issue');
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      _showErrorSnackBar('Error: $e');
    }
  }

  void _confirmDeleteIssue(dynamic issue) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Confirm Delete'),
            content: Text(
              'Are you sure you want to delete "${issue['name']}" and all its subissues?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text('Delete', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      _deleteIssue(issue['id']);
    }
  }

  Future<void> _deleteIssue(int id) async {
    final token = Provider.of<UserModel>(context, listen: false).token;
    if (token == null) return;

    setState(() => isLoading = true);

    try {
      final response = await TripReportServices.deleteIssue(id, token);
      if (response != null) {
        fetchIssues();
        _showSuccessSnackBar(response['message']);
      } else {
        setState(() => isLoading = false);
        _showErrorSnackBar('Failed to delete issue');
      }
    } catch (e) {
      setState(() => isLoading = false);
      _showErrorSnackBar('Error deleting issue: $e');
    }
  }

  Future<void> addSubIssue() async {
    if (!_formKey.currentState!.validate() || selectedIssue == null) return;

    final token = Provider.of<UserModel>(context, listen: false).token;
    if (token == null) {
      _showErrorSnackBar('Authentication required. Please log in.');
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final response = await TripReportServices.addSubIssue(
        selectedIssue['id'],
        subIssueController.text,
        token,
      );

      if (response != null) {
        subIssueController.clear();
        subIssueController.clear();
        await fetchIssues(); // Ensure await here

        // Update selectedIssue with the latest data
        var updatedIssue = issues.firstWhere(
          (issue) => issue['id'] == selectedIssue['id'],
        );

        setState(() {
          selectedIssue = updatedIssue;
        });
        _showSuccessSnackBar(response['message']);
      } else {
        setState(() {
          isLoading = false;
        });
        _showErrorSnackBar('Failed to add subissue');
      }
    } catch (e) {
      fetchIssues();
      setState(() {
        isLoading = false;
      });

      // _showErrorSnackBar('Error: $e');
    }
  }

  Future<void> updateSubIssue() async {
    if (!_formKey.currentState!.validate() || editingSubIssueId == null) return;

    final token = Provider.of<UserModel>(context, listen: false).token;
    if (token == null) {
      _showErrorSnackBar('Authentication required. Please log in.');
      return;
    }

    setState(() {
      isLoading = true;
    });
    try {
      final response = await TripReportServices.updateSubIssue(
        editingSubIssueId!,
        subIssueController.text,
        token,
      );

      if (response != null) {
        setState(() {
          isEditing = false;
          editingSubIssueId = null;
          subIssueController.clear();
        });
        await fetchIssues(); // Ensure await here

        // Update selectedIssue with the latest data
        var updatedIssue = issues.firstWhere(
          (issue) => issue['id'] == selectedIssue['id'],
        );

        setState(() {
          selectedIssue = updatedIssue;
        });

        _showSuccessSnackBar(response['message']);
      } else {
        setState(() {
          isLoading = false;
        });
        // _showErrorSnackBar('Failed to update subissue');
      }
    } catch (e) {
      fetchIssues();
      setState(() {
        isLoading = false;
      });
      // _showErrorSnackBar('Error: $e');
    }
  }

  void _confirmDeleteSubIssue(dynamic subIssue) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Confirm Delete'),
            content: Text(
              'Are you sure you want to delete "${subIssue['name']}"?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text('Delete', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      _deleteSubIssue(subIssue['id']);
    }
  }

  Future<void> _deleteSubIssue(int id) async {
    final token = Provider.of<UserModel>(context, listen: false).token;
    if (token == null) return;

    setState(() => isLoading = true);

    try {
      final response = await TripReportServices.deleteSubIssue(id, token);
      if (response != null) {
        // Refresh the subissues list
        fetchIssues().then((_) {
          var updatedIssue = issues.firstWhere(
            (issue) => issue['id'] == selectedIssue['id'],
            orElse: () => null,
          );
          if (updatedIssue != null) {
            setState(() => selectedIssue = updatedIssue);
          }
        });
        _showSuccessSnackBar(response['message']);
      } else {
        setState(() => isLoading = false);
        _showErrorSnackBar('Failed to delete subissue');
      }
    } catch (e) {
      setState(() => isLoading = false);
      _showErrorSnackBar('Error deleting subissue: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _handleRefresh() async {
    await fetchIssues();
    return Future.value();
  }

  void _startEditingIssue(dynamic issue) {
    setState(() {
      isEditing = true;
      editingIssueId = issue['id'];
      issueController.text = issue['name'];
      _tabController!.animateTo(0);
    });
  }

  void _startEditingSubIssue(dynamic subIssue) {
    setState(() {
      isEditing = true;
      editingSubIssueId = subIssue['id'];
      subIssueController.text = subIssue['name'];
      _tabController!.animateTo(1);
    });
  }

  void _cancelEditing() {
    setState(() {
      isEditing = false;
      editingIssueId = null;
      editingSubIssueId = null;
      issueController.clear();
      subIssueController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: AppLocalizations.of(context)!.text_manage_issues_subissues,
      ), // Custom Navbar
      drawer: const CustomDrawer(),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child:
            isLoading
                ? Center(child: CircularProgressIndicator())
                : Form(
                  key: _formKey,
                  child: TabBarView(
                    controller: _tabController,
                    children: [_buildIssuesTab(), _buildSubissuesTab()],
                  ),
                ),
      ),
      bottomNavigationBar: Material(
        color: Theme.of(context).cardColor,
        child: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              text: AppLocalizations.of(context)!.text_issues,
              icon: Icon(Icons.list_alt),
            ),
            Tab(
              text: AppLocalizations.of(context)!.text_subissues,
              icon: Icon(Icons.subdirectory_arrow_right),
            ),
          ],
          labelPadding: EdgeInsets.symmetric(vertical: 0, horizontal: 0),
        ),
      ),
    );
  }

  Widget _buildIssuesTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 4,
            margin: EdgeInsets.only(bottom: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isEditing
                        ? AppLocalizations.of(context)!.text_edit_issue
                        : AppLocalizations.of(context)!.text_add_new_issue,
                    style: TextStyle(fontSize: 18),
                  ),
                  SizedBox(height: 16),
                  TextFormField(
                    controller: issueController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.form_issue_name,
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.label),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter an issue name';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (isEditing) ...[
                        TextButton.icon(
                          onPressed: _cancelEditing,
                          icon: Icon(Icons.cancel),
                          label: Text(AppLocalizations.of(context)!.btn_cancel),
                        ),
                        SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed: updateIssue,
                          icon: Icon(Icons.save),
                          label: Text(AppLocalizations.of(context)!.btn_update),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ] else
                        ElevatedButton.icon(
                          onPressed: addIssue,
                          icon: Icon(Icons.add, color: Colors.blue),
                          label: Text(
                            AppLocalizations.of(context)!.btn_add_issue,
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: Colors.black,
                            side: BorderSide(color: Colors.black, width: 0.5),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _handleRefresh,
              child:
                  issues.isEmpty
                      ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.info_outline,
                              size: 48,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'No issues found',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      )
                      : ListView.builder(
                        itemCount: issues.length,
                        itemBuilder: (context, index) {
                          final issue = issues[index];
                          return Card(
                            margin: EdgeInsets.only(bottom: 8),
                            child: ListTile(
                              title: Text(
                                issue['name'],
                                // style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              subtitle:
                                  issue['subissues'] != null &&
                                          issue['subissues'].length > 0
                                      ? Text(
                                        '${issue['subissues'].length} subissues',
                                        style: TextStyle(color: Colors.blue),
                                      )
                                      : Text('No subissues'),
                              leading: CircleAvatar(
                                backgroundColor: Colors.blue[100],
                                child: Text(
                                  issue['name'].substring(0, 1).toUpperCase(),
                                  style: TextStyle(
                                    color: Colors.blue[900],
                                    // fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: Icon(Icons.edit, color: Colors.blue),
                                    onPressed: () => _startEditingIssue(issue),
                                    tooltip: 'Edit issue',
                                  ),
                                  IconButton(
                                    icon: Icon(Icons.delete, color: Colors.red),
                                    onPressed: () => _confirmDeleteIssue(issue),
                                    tooltip: 'Delete issue',
                                  ),
                                  IconButton(
                                    icon: Icon(
                                      Icons.visibility,
                                      color: Colors.green,
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        selectedIssue = issue;
                                        _tabController!.animateTo(1);
                                      });
                                    },
                                    tooltip: 'View subissues',
                                  ),
                                ],
                              ),
                              onTap: () {
                                setState(() {
                                  selectedIssue = issue;
                                });
                              },
                              selected: selectedIssue == issue,
                            ),
                          );
                        },
                      ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubissuesTab() {
    List subIssues = [];

    if (selectedIssue != null) {
      if (selectedIssue.containsKey('subissues') &&
          selectedIssue['subissues'] is List) {
        subIssues = selectedIssue['subissues'];
      }
      print(
        "Selected issue ID: ${selectedIssue['id']}, Name: ${selectedIssue['name']}",
      );
      print("Subissues: $subIssues");
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 4,
            margin: EdgeInsets.only(bottom: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Parent Issue',
                    style: TextStyle(
                      fontSize: 16,
                      // fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<int>(
                        value:
                            selectedIssue != null &&
                                    issues.any(
                                      (issue) =>
                                          issue['id'] == selectedIssue['id'],
                                    )
                                ? selectedIssue['id']
                                : null,
                        hint: Text('Select Issue'),
                        isExpanded: true,
                        items:
                            issues.map((issue) {
                              return DropdownMenuItem<int>(
                                value: issue['id'], // Only pass ID
                                child: Text(issue['name']),
                              );
                            }).toList(),
                        onChanged: (int? value) {
                          setState(() {
                            selectedIssue = issues.firstWhere(
                              (issue) => issue['id'] == value,
                            );
                            selectedSubIssue =
                                null; // Reset selected subissue when issue changes
                          });
                        },
                      ),
                    ),
                  ),
                  SizedBox(height: 16),
                  Text(
                    isEditing ? 'Edit Subissue' : 'Add New Subissue',
                    style: TextStyle(fontSize: 16),
                  ),
                  SizedBox(height: 8),
                  TextFormField(
                    controller: subIssueController,
                    decoration: InputDecoration(
                      labelText: 'Subissue Name',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.subdirectory_arrow_right),
                      enabled: selectedIssue != null,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a subissue name';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (isEditing) ...[
                        TextButton.icon(
                          onPressed: _cancelEditing,
                          icon: Icon(Icons.cancel),
                          label: Text('Cancel'),
                        ),
                        SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed: updateSubIssue,
                          icon: Icon(Icons.save, color: Colors.blue),
                          label: Text('Update'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: Colors.black,
                            side: BorderSide(color: Colors.black, width: 0.5),
                          ),
                        ),
                      ] else
                        ElevatedButton.icon(
                          onPressed: selectedIssue != null ? addSubIssue : null,
                          icon: Icon(Icons.add, color: Colors.blue),
                          label: Text('Add Subissue'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: Colors.black,
                            disabledBackgroundColor: Colors.grey[300],
                            disabledForegroundColor: Colors.grey[600],
                            side: BorderSide(color: Colors.black, width: 0.5),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (selectedIssue != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Text(
                'Subissues for "${selectedIssue['name']}"',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _handleRefresh,
              child:
                  selectedIssue == null
                      ? Center(
                        child: Text(
                          'Please select an issue first',
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                      : subIssues.isEmpty
                      ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.info_outline,
                              size: 48,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'No subissues found for this issue',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      )
                      : ListView.builder(
                        itemCount: subIssues.length,
                        itemBuilder: (context, index) {
                          final subIssue = subIssues[index];
                          return Card(
                            margin: EdgeInsets.only(bottom: 8),
                            child: ListTile(
                              title: Text(subIssue['name']),
                              leading: CircleAvatar(
                                backgroundColor: Colors.green[100],
                                child: Text(
                                  subIssue['name']
                                      .substring(0, 1)
                                      .toUpperCase(),
                                  style: TextStyle(
                                    color: Colors.green[900],
                                    // fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: Icon(Icons.edit, color: Colors.green),
                                    onPressed:
                                        () => _startEditingSubIssue(subIssue),
                                    tooltip: 'Edit subissue',
                                  ),
                                  IconButton(
                                    icon: Icon(Icons.delete, color: Colors.red),
                                    onPressed:
                                        () => _confirmDeleteSubIssue(subIssue),
                                    tooltip: 'Delete subissue',
                                  ),
                                ],
                              ),
                              onTap: () {
                                setState(() {
                                  selectedSubIssue = subIssue;
                                });
                              },
                              selected: selectedSubIssue == subIssue,
                            ),
                          );
                        },
                      ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddDialog() {
    return AlertDialog(
      title: Text('Add New Item'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: Icon(Icons.list_alt, color: Colors.blue),
            title: Text('Add Issue'),
            onTap: () {
              Navigator.pop(context);
              setState(() {
                _tabController!.animateTo(0);
                isEditing = false;
                issueController.clear();
              });
            },
          ),
          Divider(),
          ListTile(
            leading: Icon(Icons.subdirectory_arrow_right, color: Colors.green),
            title: Text('Add Subissue'),
            onTap: () {
              Navigator.pop(context);
              setState(() {
                _tabController!.animateTo(1);
                isEditing = false;
                subIssueController.clear();
              });
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Cancel'),
        ),
      ],
    );
  }
}
