import 'dart:io' as io;
import 'package:flutter/foundation.dart' show Uint8List, kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:file_picker/file_picker.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/rail_sathi_services/complain_services.dart';
import 'package:railops/services/rail_sathi_services/train_services.dart';
import 'package:railops/services/train_services/train_details_services.dart';
import 'package:railops/types/rail_sathi_types/train_type.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:railops/services/profile_services/pnr_service.dart';
import 'package:railops/types/profile_types/pnr_response.dart';
import 'package:image_picker/image_picker.dart';

class WriteComplaintTab extends StatefulWidget {
  const WriteComplaintTab({super.key});

  @override
  State<WriteComplaintTab> createState() => _WriteComplaintTabState();
}

class _WriteComplaintTabState extends State<WriteComplaintTab>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  List<Train> trains = [];
  Train? selectedTrain;
  bool isOtherTrain = false;
  List<PlatformFile> selectedMedia = [];
  bool isValidatingPNR = false;
  String? ehkUsername;
  String pnrValidationStatus = 'not-attempted'; // Default validation status

  // Controllers
  final TextEditingController pnrController = TextEditingController();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController coachController = TextEditingController();
  final TextEditingController berthController = TextEditingController();
  final TextEditingController journeyDateController = TextEditingController();
  final TextEditingController issueController = TextEditingController();
  final TextEditingController customTrainNumberController =
      TextEditingController();
  final TextEditingController customTrainNameController =
      TextEditingController();

  String? selectedStatus;
  bool isPnrValidated = false;
  bool showIssueForm = false;
  bool isSubmitting = false;

  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    fetchTrains();
  }

  Future<void> fetchTrains() async {
    try {
      final trainsData = await RailSathiTrainService.getAllTrainsWithId();
      setState(() {
        trains = trainsData;
      });
    } catch (e) {
      debugPrint("Error fetching trains: $e");
    }
  }

  Future<void> _pickFromCamera() async {
    try {
      if (kIsWeb) {
        await _pickFromGallery(); // Web only supports gallery
        return;
      }

      final pickedFile = await _picker.pickImage(source: ImageSource.camera);
      if (pickedFile != null) {
        final file = io.File(pickedFile.path);
        final bytes = await pickedFile.readAsBytes();
        setState(() {
          selectedMedia.add(PlatformFile(
            name: pickedFile.name,
            path: pickedFile.path,
            bytes: bytes,
            size: bytes.length,
          ));
        });
      }
    } catch (e) {
      debugPrint("Camera pick error: $e");
    }
  }

  Future<void> _pickFromGallery() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.custom,
        allowedExtensions: ['jpg', 'png', 'mp4'],
      );
      if (result != null) {
        setState(() {
          selectedMedia.addAll(result.files);
        });
      }
    } catch (e) {
      debugPrint("Gallery pick error: $e");
    }
  }

  Widget buildMediaPreview() {
    return selectedMedia.isNotEmpty
        ? SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: selectedMedia.length,
              itemBuilder: (context, index) {
                final media = selectedMedia[index];
                final isVideo = media.extension?.toLowerCase() == 'mp4';

                Widget content;
                if (kIsWeb) {
                  content = isVideo
                      ? const Icon(Icons.videocam, size: 40)
                      : Image.memory(media.bytes!, fit: BoxFit.cover);
                } else {
                  final file = io.File(media.path!);
                  content = isVideo
                      ? const Icon(Icons.videocam, size: 40)
                      : Image.file(file, fit: BoxFit.cover);
                }

                return Stack(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          width: 100,
                          height: 100,
                          color: Colors.black12,
                          child: content,
                        ),
                      ),
                    ),
                    Positioned(
                      top: 2,
                      right: 10,
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            selectedMedia.removeAt(index);
                          });
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            shape: BoxShape.circle,
                          ),
                          padding: const EdgeInsets.all(4),
                          child: const Icon(Icons.close,
                              size: 16, color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          )
        : const SizedBox.shrink();
  }

  Future<void> _selectJourneyDate(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2023),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      journeyDateController.text =
          "${picked.day}-${picked.month}-${picked.year}";
    }
  }

  Future<void> _submitComplain() async {
    setState(() => isSubmitting = true);

    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final userToken = userModel.token;

      List<io.File> mediaFiles = [];
      List<Uint8List> mediaBytes = [];

      if (kIsWeb) {
        mediaBytes = selectedMedia
            .where((file) => file.bytes != null)
            .map((file) => file.bytes!)
            .toList();
      } else {
        mediaFiles = selectedMedia
            .where((file) => file.path != null)
            .map((file) => io.File(file.path!))
            .toList();
      }

      // Determine train data to send
      int? trainId;
      String? trainNumber;
      String? trainNameValue;

      if (isOtherTrain) {
        trainNumber = customTrainNumberController.text;
        trainNameValue = customTrainNameController.text;
      } else if (selectedTrain != null) {
        trainId = selectedTrain!.id;
        trainNumber = selectedTrain!.trainNo;
        trainNameValue = selectedTrain!.trainName;
      }

      final result = await ComplainServices.addComplain(
        token: userToken,
        pnrNumber: pnrController.text,
        isPnrValidated: pnrValidationStatus, // Using the updated status format
        name: nameController.text,
        mobileNumber: phoneController.text,
        complainType: _tabController.index == 0 ? 'cleaning' : 'linen',
        complainDescription: issueController.text,
        complainStatus: selectedStatus ?? 'pending',
        train: trainId,
        trainNumber: trainNumber,
        trainName: trainNameValue,
        coach: coachController.text,
        berthNo: int.tryParse(berthController.text) ?? 0,
        mediaFiles: mediaFiles,
        mediaBytes: mediaBytes,
        complainDate: journeyDateController.text,
      );

      if (result != null) {
        _resetForm();
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(AppLocalizations.of(context).text_success),
            content: Text(AppLocalizations.of(context)
                .text_complaint_submitted_successfully),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(AppLocalizations.of(context).btn_ok),
              ),
            ],
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content:
                  Text(AppLocalizations.of(context).text_failed_to_submit)),
        );
      }
    } catch (e) {
      print("Error: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.of(context).snackbar_error_e)),
      );
    } finally {
      setState(() => isSubmitting = false);
    }
  }

  void _resetForm() {
    setState(() {
      pnrController.clear();
      nameController.clear();
      phoneController.clear();
      coachController.clear();
      berthController.clear();
      journeyDateController.clear();
      issueController.clear();
      customTrainNumberController.clear();
      customTrainNameController.clear();
      selectedTrain = null;
      isOtherTrain = false;
      selectedMedia = [];
      isPnrValidated = false;
      showIssueForm = false;
      selectedStatus = null;
      pnrValidationStatus = 'not-attempted';
      ehkUsername = null;
      _tabController.index = 0;
    });
  }

  Future<void> _showMediaSourceSelector() async {
    if (kIsWeb) {
      await _pickFromGallery(); // Web supports only file picker
      return;
    }

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: Text(AppLocalizations.of(context).text_camera),
              onTap: () {
                Navigator.of(context).pop();
                _pickFromCamera();
              },
            ),
            ListTile(
              leading: const Icon(Icons.image),
              title: Text(AppLocalizations.of(context).text_gallery),
              onTap: () {
                Navigator.of(context).pop();
                _pickFromGallery();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget buildIssueTab(String label) {
    final theme = Theme.of(context);
    final lightPrimary = theme.primaryColor.withOpacity(0.15);

    final ehkDisplay =
        ehkUsername ?? AppLocalizations.of(context).text_ehk_not_assigned;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                  AppLocalizations.of(context)
                      .text_ehk_ehkdisplay
                      .replaceAll('{ehkDisplay}', ehkDisplay),
                  style: const TextStyle(fontWeight: FontWeight.bold)),
              const Spacer(),
              if (ehkUsername != null)
                IconButton(
                  onPressed: () => launchUrl(
                      Uri.parse("tel:${ehkUsername!.split('_').last}")),
                  icon: const Icon(Icons.call, color: Colors.green),
                ),
            ],
          ),
          TextField(
            controller: issueController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context).form_write_your_issue,
              border: const OutlineInputBorder(),
              prefixIcon: const Icon(Icons.message_outlined),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 10),
          DropdownButtonFormField<String>(
            value: selectedStatus,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context).form_issue_status,
              border: const OutlineInputBorder(),
            ),
            items: [
              DropdownMenuItem(
                  value: 'pending',
                  child: Text(AppLocalizations.of(context).btn_pending)),
              DropdownMenuItem(
                  value: 'completed',
                  child: Text(AppLocalizations.of(context).btn_completed)),
            ],
            onChanged: (value) {
              setState(() {
                selectedStatus = value;
              });
            },
          ),
          const SizedBox(height: 10),
          buildMediaPreview(),
          const SizedBox(height: 10),
          ElevatedButton.icon(
            onPressed: _showMediaSourceSelector,
            icon: const Icon(Icons.upload),
            label: Text(AppLocalizations.of(context).btn_upload_image_video),
            style: ElevatedButton.styleFrom(
              backgroundColor: lightPrimary,
              foregroundColor: theme.primaryColor,
              elevation: 0,
              minimumSize: const Size.fromHeight(45),
            ),
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: _submitComplain,
            style: ElevatedButton.styleFrom(
              backgroundColor: lightPrimary,
              foregroundColor: theme.primaryColor,
              elevation: 0,
              minimumSize: const Size.fromHeight(45),
            ),
            child: Text(AppLocalizations.of(context).btn_submit_issue),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey[100],
        body: Stack(
          children: [
            SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  /// PNR and Train Details
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.white,
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: pnrController,
                                readOnly: isPnrValidated,
                                decoration: InputDecoration(
                                  labelText:
                                      AppLocalizations.of(context).form_pnr,
                                  border: const OutlineInputBorder(),
                                  suffixIcon: isPnrValidated
                                      ? const Icon(Icons.check_circle,
                                          color: Colors.green)
                                      : null,
                                ),
                              ),
                            ),
                            const SizedBox(width: 10),
                            ElevatedButton(
                              onPressed: isPnrValidated
                                  ? null
                                  : () async {
                                      final userModel = Provider.of<UserModel>(
                                          context,
                                          listen: false);
                                      final pnr = pnrController.text.trim();

                                      if (pnr.isEmpty) return;

                                      setState(() => isValidatingPNR = true);

                                      try {
                                        //on your local use this
                                        // final response =
                                        //     await PnrService.checkPNR(
                                        //         pnr, userModel.token);
                                        final response =
                                            await PnrService.checkPNR(pnr);
                                        if (response != null) {
                                          setState(() {
                                            isPnrValidated = true;
                                            pnrValidationStatus =
                                                'attempted-success';

                                            // Find train in our list or set as custom
                                            final trainNumber =
                                                response.trainNumber;
                                            final matchingTrain =
                                                trains.firstWhere(
                                              (train) =>
                                                  train.trainNo == trainNumber,
                                              orElse: () => Train(
                                                  id: 0,
                                                  trainNo: trainNumber,
                                                  trainName:
                                                      response.trainName),
                                            );

                                            if (matchingTrain.id == 0) {
                                              // Train not in dropdown, use "Other" option
                                              isOtherTrain = true;
                                              customTrainNumberController.text =
                                                  trainNumber;
                                              customTrainNameController.text =
                                                  response.trainName;
                                            } else {
                                              // Train exists in dropdown
                                              selectedTrain = matchingTrain;
                                            }

                                            coachController.text = response
                                                    .passengerCoaches.isNotEmpty
                                                ? response.passengerCoaches[0]
                                                : '';
                                            berthController.text = response
                                                    .passengerBerths.isNotEmpty
                                                ? response.passengerBerths[0]
                                                    .toString()
                                                : '';
                                            nameController.text = response
                                                    .passengerNames.isNotEmpty
                                                ? response.passengerNames[0]
                                                : '';
                                            journeyDateController.text =
                                                response.dateOfJourney;
                                            ehkUsername =
                                                response.ehkUsernames.isNotEmpty
                                                    ? response.ehkUsernames[0]
                                                    : null;
                                          });
                                        } else {
                                          setState(() {
                                            pnrValidationStatus =
                                                'attempted-failure';
                                          });
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                                content: Text(AppLocalizations
                                                        .of(context)
                                                    .msg_pnr_validation_failed)),
                                          );
                                        }
                                      } catch (e) {
                                        setState(() {
                                          pnrValidationStatus =
                                              'attempted-failure';
                                        });
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                              content: Text(AppLocalizations.of(
                                                      context)
                                                  .msg_pnr_validation_error_e)),
                                        );
                                      } finally {
                                        setState(() => isValidatingPNR = false);
                                      }
                                    },
                              style: ElevatedButton.styleFrom(
                                  minimumSize: const Size(100, 55)),
                              child: isValidatingPNR
                                  ? const SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                          strokeWidth: 2))
                                  : Text(AppLocalizations.of(context)
                                      .btn_validate),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        TextField(
                          controller: nameController,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context).form_name,
                            border: const OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 12),
                        TextField(
                          controller: phoneController,
                          decoration: InputDecoration(
                            labelText:
                                AppLocalizations.of(context).form_phone_number,
                            border: const OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.phone,
                        ),
                        const SizedBox(height: 12),
                        DropdownSearch<String>(
                          enabled: !isPnrValidated,
                          popupProps: PopupProps.menu(
                            showSearchBox: true,
                            searchFieldProps: TextFieldProps(
                              decoration: InputDecoration(
                                hintText: AppLocalizations.of(context)
                                    .form_search_by_train_number_or_name,
                                prefixIcon: const Icon(Icons.search),
                              ),
                            ),
                            showSelectedItems: true,
                          ),
                          selectedItem: isOtherTrain
                              ? AppLocalizations.of(context).text_other
                              : selectedTrain?.trainNo,
                          items: [
                            ...trains.map((train) =>
                                "${train.trainNo} - ${train.trainName}"),
                            AppLocalizations.of(context).text_other,
                          ],
                          dropdownDecoratorProps: DropDownDecoratorProps(
                            dropdownSearchDecoration: InputDecoration(
                              labelText: AppLocalizations.of(context)
                                  .form_train_selection,
                              border: const OutlineInputBorder(),
                            ),
                          ),
                          onChanged: isPnrValidated
                              ? null
                              : (value) {
                                  if (value == null) return;

                                  setState(() {
                                    if (value ==
                                        AppLocalizations.of(context)
                                            .text_other) {
                                      isOtherTrain = true;
                                      selectedTrain = null;
                                    } else {
                                      isOtherTrain = false;
                                      // Extract train number from the combined string
                                      final trainNumber =
                                          value.split(' - ').first;
                                      selectedTrain = trains.firstWhere(
                                        (train) => train.trainNo == trainNumber,
                                        orElse: () => Train(
                                            id: 0, trainNo: '', trainName: ''),
                                      );
                                    }
                                  });
                                },
                          filterFn: (item, filter) {
                            return item
                                .toLowerCase()
                                .contains(filter.toLowerCase());
                          },
                        ),
                        if (isOtherTrain) ...[
                          const SizedBox(height: 12),
                          TextField(
                            controller: customTrainNumberController,
                            readOnly: isPnrValidated,
                            decoration: InputDecoration(
                              labelText: AppLocalizations.of(context)
                                  .form_train_number,
                              border: const OutlineInputBorder(),
                            ),
                          ),
                          const SizedBox(height: 12),
                          TextField(
                            controller: customTrainNameController,
                            readOnly: isPnrValidated,
                            decoration: InputDecoration(
                              labelText:
                                  AppLocalizations.of(context).form_train_name,
                              border: const OutlineInputBorder(),
                            ),
                          ),
                        ] else if (selectedTrain != null) ...[
                          const SizedBox(height: 12),
                          SizedBox(
                            width: double.infinity,
                            child: Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(8),
                                color: Colors.grey.shade50,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Name: ${selectedTrain!.trainName}",
                                    style:
                                        TextStyle(color: Colors.grey.shade700),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                        const SizedBox(height: 12),
                        TextField(
                          controller: journeyDateController,
                          readOnly: true,
                          enabled: !isPnrValidated,
                          onTap: isPnrValidated
                              ? null
                              : () => _selectJourneyDate(context),
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context)
                                .form_journey_start_date,
                            hintText:
                                AppLocalizations.of(context).form_dd_mm_yyyy,
                            border: const OutlineInputBorder(),
                            suffixIcon: const Icon(Icons.calendar_today),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                readOnly: isPnrValidated,
                                enabled: !isPnrValidated,
                                controller: coachController,
                                decoration: InputDecoration(
                                  labelText:
                                      AppLocalizations.of(context).form_coach,
                                  border: const OutlineInputBorder(),
                                ),
                              ),
                            ),
                            const SizedBox(width: 10),
                            Expanded(
                              child: TextField(
                                readOnly: isPnrValidated,
                                enabled: !isPnrValidated,
                                controller: berthController,
                                decoration: InputDecoration(
                                  labelText:
                                      AppLocalizations.of(context).form_berth,
                                  border: const OutlineInputBorder(),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        ElevatedButton(
                          onPressed: () {
                            bool hasValidTrainSelection = (isOtherTrain &&
                                    customTrainNumberController
                                        .text.isNotEmpty &&
                                    customTrainNameController
                                        .text.isNotEmpty) ||
                                (!isOtherTrain && selectedTrain != null);

                            if (pnrController.text.isNotEmpty &&
                                nameController.text.isNotEmpty &&
                                phoneController.text.isNotEmpty &&
                                hasValidTrainSelection &&
                                journeyDateController.text.isNotEmpty) {
                              setState(() {
                                showIssueForm = true;
                              });
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                    content: Text(AppLocalizations.of(context)
                                        .msg_please_fill_all_required_details)),
                              );
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            minimumSize: const Size.fromHeight(50),
                            backgroundColor: Colors.blueAccent,
                            foregroundColor: Colors.white,
                          ),
                          child: Text(AppLocalizations.of(context).btn_next),
                        ),
                      ],
                    ),
                  ),

                  /// Complaint Form Section
                  Visibility(
                    visible: showIssueForm,
                    child: Column(
                      children: [
                        const SizedBox(height: 30),
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(12),
                            color: Colors.white,
                          ),
                          child: Column(
                            children: [
                              TabBar(
                                controller: _tabController,
                                labelColor: Colors.black,
                                unselectedLabelColor: Colors.grey,
                                indicatorColor: Theme.of(context).primaryColor,
                                tabs: [
                                  Tab(
                                      text: AppLocalizations.of(context)
                                          .tab_cleaning_issues),
                                  Tab(
                                      text: AppLocalizations.of(context)
                                          .tab_linen_related_issues),
                                ],
                              ),
                              SizedBox(
                                height: 500,
                                child: TabBarView(
                                  controller: _tabController,
                                  children: [
                                    buildIssueTab("Cleaning"),
                                    buildIssueTab("Linen"),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            if (isSubmitting)
              Container(
                color: Colors.black.withOpacity(0.5),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        ));
  }
}
