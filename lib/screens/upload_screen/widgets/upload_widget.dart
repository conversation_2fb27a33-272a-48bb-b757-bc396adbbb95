import 'dart:convert';
import 'dart:io'; // For File class
import 'package:device_info_plus/device_info_plus.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/upload_services/upload_services.dart';
import 'package:railops/types/upload_types/upload_request.dart';
import 'package:permission_handler/permission_handler.dart'; // For permissions
import 'package:flutter/foundation.dart' show kIsWeb; // For platform check
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class UploadWidget extends StatefulWidget {
  final UserModel userModel;

  const UploadWidget({super.key, required this.userModel});

  @override
  _UploadWidgetState createState() => _UploadWidgetState();
}

class _UploadWidgetState extends State<UploadWidget> {
  FilePickerResult? result;
  PlatformFile? selectedFile;
  bool isLoading = false; // Loader state

  final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();

  Future<void> _pickFile() async {
    if (!kIsWeb) {
      // Handle permissions for mobile platforms
      await _checkPermissions();
    }

    result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['json'], // Allow only JSON files
      allowMultiple: false,
      withData: true,
    );

    if (result == null || result!.files.isEmpty) {
      // No file selected
      print("No file selected");
      setState(() {
        selectedFile = null;
      });
    } else {
      setState(() {
        selectedFile = result!.files.first; // Get the first file selected
      });
      print('Selected file: ${selectedFile!.name}');
    }
  }

  Future<void> _checkPermissions() async {
    var androidInfo = await deviceInfoPlugin.androidInfo;
    int androidVersion = int.parse(androidInfo.version.release);
    final status = await Permission.storage.request();

    if (!status.isGranted && !(androidVersion >= 13)) {
      final result = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(AppLocalizations.of(context).upload_permission_title),
          content: Text(AppLocalizations.of(context).upload_permission_content),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(AppLocalizations.of(context).btn_cancel),
            ),
            TextButton(
              onPressed: () {
                openAppSettings();
                Navigator.of(context).pop(true);
              },
              child: Text(AppLocalizations.of(context).btn_open_settings),
            ),
          ],
        ),
      );

      if (result != true) {
        return; // Permission not granted or dialog dismissed
      }
    }
  }

  Future<void> _submitFile() async {
    if (selectedFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).msg_select_json_file),
        ),
      );
      return;
    }

    setState(() {
      isLoading = true; // Show loader
    });

    try {
      if (selectedFile!.bytes == null) {
        throw Exception('File bytes are null');
      }

      // Read the file content
      final fileBytes = selectedFile!.bytes!;
      final fileContent = utf8.decode(fileBytes);

      // Parse the JSON file content
      final jsonData = jsonDecode(fileContent) as List<dynamic>;

      // Convert the data to the required format
      final convertedData = convertToJsonData(jsonData);

      // Create the UploadRequest
      final uploadRequest = UploadRequest(
        jsonData: convertedData,
        phone: widget.userModel.mobileNumber,
        token: widget.userModel.token,
      );

      // Upload the data using UploadService
      final responseMessage =
          await UploadService.uploadTrainSchedule(uploadRequest);

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(responseMessage),
        ),
      );

      // Clear the selection after successful upload
      _clearSelection();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).error_json_format),
        ),
      );
    } finally {
      setState(() {
        isLoading = false; // Hide loader
      });
    }
  }

  Map<String, dynamic> convertToJsonData(List<dynamic> data) {
    final result = <String, Map<String, dynamic>>{};

    for (int i = 0; i < data.length; i++) {
      final item = data[i] as Map<String, dynamic>;
      result[i.toString()] = {
        "train_no": item["train_no"],
        "train_name": item["train_name"],
        "date": item["date"],
        "coach_no": item["coach_no"],
        "berth_no": item["berth_no"],
        "from_station": item["from_station"],
        "to_station": item["to_station"],
        "coach_class": item["coach_class"],
        "occupancy_page_no": item["occupancy_page_no"],
        "source": item["source"],
      };
    }

    return result;
  }

  void _clearSelection() {
    setState(() {
      selectedFile = null;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizations.of(context).msg_selection_cleared),
      ),
    );
  }

  Future<void> _refreshPage() async {
    setState(() {
      selectedFile = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _refreshPage, // Swipe down to refresh
        child: SingleChildScrollView(
          physics:
              const AlwaysScrollableScrollPhysics(), // Ensures scroll even if content is smaller than screen
          child: Center(
            child: Card(
              elevation: 5,
              margin: const EdgeInsets.all(20.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: Container(
                height: screenHeight * 0.5, // 50% of screen height
                width: screenWidth *
                    0.8, // Use percentage of screen width, avoid fixed extra width
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 5),
                      child: Text(
                        AppLocalizations.of(context).upload_title,
                        style: const TextStyle(
                            fontSize: 18, fontWeight: FontWeight.normal),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 10.0),

                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppLocalizations.of(context).text_selected_file,
                          style: const TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 10),
                        Text(
                          selectedFile != null
                              ? selectedFile!.name
                              : AppLocalizations.of(context)
                                  .text_no_file_selected,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.blueAccent,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (selectedFile != null) ...[
                          const SizedBox(height: 5),
                          Text(
                            '${AppLocalizations.of(context).text_size} ${(selectedFile!.size / 1024).toStringAsFixed(2)} KB',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ],
                    ),
                    const Spacer(), // Pushes buttons to the bottom
                    Column(
                      children: [
                        if (isLoading) ...[
                          const CircularProgressIndicator(),
                          const SizedBox(height: 20),
                        ],
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Expanded(
                              // Prevent overflow by wrapping in Expanded
                              child: ElevatedButton.icon(
                                onPressed: _pickFile,
                                icon: const Icon(Icons.file_upload,
                                    color: Colors.blue),
                                label: Text(
                                  AppLocalizations.of(context).btn_pick_file,
                                  style: const TextStyle(color: Colors.black),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 2),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  side: const BorderSide(
                                      color: Colors.black87, width: 0.5),
                                ),
                              ),
                            ),
                            const SizedBox(
                                width: 10), // Add spacing between buttons
                            Expanded(
                              // Prevent overflow by wrapping in Expanded
                              child: ElevatedButton.icon(
                                onPressed: _clearSelection,
                                icon: const Icon(Icons.clear,
                                    color: Colors.redAccent),
                                label: Text(
                                  AppLocalizations.of(context).btn_clear,
                                  style: const TextStyle(color: Colors.black),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  side: const BorderSide(
                                      color: Colors.black87, width: 0.5),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        ElevatedButton.icon(
                          onPressed: _submitFile,
                          icon: const Icon(Icons.send, color: Colors.green),
                          label: Text(
                            AppLocalizations.of(context).btn_submit_file,
                            style: const TextStyle(color: Colors.black),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 50, vertical: 15),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            side: const BorderSide(
                                color: Colors.black87, width: 0.5),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
