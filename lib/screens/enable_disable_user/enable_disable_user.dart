import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/routes.dart';
import 'package:railops/services/authentication_services/Request_update_service.dart';
import 'package:railops/services/authentication_services/auth_service.dart';
import 'package:railops/services/user_info_services/user_info_services.dart';
import 'package:railops/types/user_types/user_info.dart';
import 'package:railops/utils/permissions.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';
import 'package:railops/widgets/error_modal.dart';
import 'package:railops/widgets/success_modal.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:railops/screens/update_user/update_user_screen.dart' as update;
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:io' show Platform;
import 'package:url_launcher/url_launcher_string.dart';

class EnableDisableUser extends StatefulWidget {
  const EnableDisableUser({super.key});

  @override
  _EnableDisableUserState createState() => _EnableDisableUserState();
}

class _EnableDisableUserState extends State<EnableDisableUser> {
  List<dynamic> users = [];
  List<dynamic> filteredUsers = [];
  bool isLoading = true;
  bool hasMoreUsers = true;
  String searchQuery = "";
  final ScrollController _scrollController = ScrollController();
  int page = 1;
  bool searchLoading = false;
  int maxPage = 2;
  String? token;
  String? userType;
  bool hasAdminPermissions = false;
  bool hasAddUserPermissions = false;
  bool hasUpdateUserPermissions = false;
  bool hasUserStatusPermissions = false;
  bool _isMounted = false;
  TextEditingController controller = TextEditingController();
  bool _isInitialLoad = true;

  @override
  void initState() {
    super.initState();
    _isMounted = true;
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent &&
        !isLoading &&
        hasMoreUsers) {
      fetchMoreUsers();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_isMounted && _isInitialLoad) {
      final userModel = Provider.of<UserModel>(context, listen: false);
      setState(() {
        token = userModel.token;
        userType = userModel.userType;

        // Check if the user has admin permissions
        List<String> adminUsers = [
          's2 admin',
          'railway admin',
          'contractor admin'
        ];
        hasAdminPermissions = adminUsers.contains(userType);

        List<String> addUsers = [
          'write read',
          'EHK',
          'coach attendent',
          'OBHS',
          'railway officer',
          'contractor',
          'war room user',
          'passenger'
        ];
        hasAddUserPermissions = addUsers.contains(userType);

        List<String> readUsers = [
          'write read',
          'read only',
          'EHK',
          'coach attendent',
          'OBHS',
          'railway officer',
          'contractor',
          'war room user',
          'passenger'
        ];
        hasUpdateUserPermissions = readUsers.contains(userType);

        List<String> UserStatus = [
          'write read',
          'read only',
          'EHK',
          'coach attendent',
          'OBHS',
          'railway officer',
          'contractor',
          'war room user',
          'passenger'
        ];
        hasUserStatusPermissions = UserStatus.contains(userType);
      });
      fetchUsers();
      _isInitialLoad = false;
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _isMounted = false;
    super.dispose();
  }

  Future<void> fetchUsers() async {
    if (!_isMounted) return;
    try {
      setState(() => isLoading = true);
      final response =
          await UserService.enableDisableUser(token!, page.toString());

      if (_isMounted) {
        setState(() {
          List<dynamic> usersList = response['users'] ?? [];
          maxPage = response['pagination']["total_pages"];
          users.addAll(usersList);
          filteredUsers = users.take((25 * page)).toList();
          isLoading = false;
          if (usersList.isEmpty) {
            hasMoreUsers = false;
          }
        });
        initializeUserIndex();
      }
    } catch (error) {
      if (_isMounted) {
        setState(() => isLoading = false);
        Navigator.of(context).pop();
      }
    }
  }

  // Direct navigation function to update user screen with permission check
  void navigateToUpdateUserScreen(BuildContext context, String mobileNumber) {
    if (hasAdminPermissions || hasUpdateUserPermissions) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) =>
              update.UpdateUserScreen(mobileNumber: mobileNumber),
        ),
      );
    } else {
      _showPermissionDeniedDialog(context, "update user details");
    }
  }

  // Show permission denied dialog
  void _showPermissionDeniedDialog(BuildContext context, String action) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).text_permission_denied,
              style: const TextStyle(color: Colors.red)),
          content: Text(
              'Your user type needs to be admin to perform this action: $action'),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(AppLocalizations.of(context).btn_ok,
                  style: const TextStyle(fontWeight: FontWeight.bold)),
            ),
          ],
        );
      },
    );
  }

  Map<String, Map<String, dynamic>> userIndex = {};

  void initializeUserIndex() {
    for (var user in users) {
      userIndex[user['username'].toString().toLowerCase()] = user;
      userIndex[user['phone'].toString().toLowerCase()] = user;
    }
  }

  Future<void> fetchMoreUsers() async {
    if (hasMoreUsers) {
      page++;
      fetchUsers();
    }
  }

  void filterUsers(String query) {
    query = query.trim();
    if (query.isEmpty) {
      setState(() {
        filteredUsers = users.take((25 * page)).toList();
        searchLoading = false;
      });
      return;
    }
    if (!searchLoading) {
      _performUserSearch(query);
    }
  }

  Future<void> _performUserSearch(String query) async {
    // Ensure we're not already loading
    if (searchLoading) return;
    try {
      setState(() {
        searchLoading = true;
      });
      final response = await UserService.getUserByPartialUsername(token!,
          username: query.trim());

      if (!mounted) return;

      setState(() {
        if (response['data'] != null) {
          filteredUsers = response['data'].map<dynamic>((user) {
            return {
              'username': user['first_name'] + ' ' + (user['last_name'] ?? ''),
              'phone': user['phone'],
              'email': user['email'],
              'role': user['user_type'],
              'depot': user['depot'],
              'username_unique': user['username'],
              'user_status': user['user_status'] ?? 'N/A',
            };
          }).toList();
        } else {
          filteredUsers = [];
        }
        searchLoading = false;
      });
    } catch (error) {
      if (mounted) {
        setState(() {
          searchLoading = false;
          filteredUsers = [];
        });
      }
    }
  }

  void _showUserDetailsDialog(BuildContext context, dynamic user) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return UserDetailsDialog(
          user: user,
          navigateToUpdateUserScreen: navigateToUpdateUserScreen,
          hasAdminPermissions: hasAdminPermissions,
          hasUpdateUserPermissions: hasUpdateUserPermissions,
          hasUserStatusPermissions: hasUserStatusPermissions,
        );
      },
    );
  }

  Future<void> _reloadPage() async {
    await Future.delayed(const Duration(seconds: 3));
    reloadCurrentScreen(context, const EnableDisableUser());
  }

  void reloadCurrentScreen(BuildContext context, Widget currentScreen) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => currentScreen,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
          title: AppLocalizations.of(context).screen_title_user_management),
      drawer: const CustomDrawer(),
      backgroundColor: const Color(0xFFF5F7FA),
      body: RefreshIndicator(
        onRefresh: () async {
          await Future.delayed(const Duration(seconds: 1));
          reloadCurrentScreen(context, const EnableDisableUser());
        },
        child: Container(
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height,
          ),
          child: isLoading && page == 1
              ? const Center(child: CircularProgressIndicator())
              : Column(
                  children: [
                    Container(
                      color: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16.0, vertical: 12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Text(
                            'Users Directory',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                          const SizedBox(height: 16),
                          // Move buttons here
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Container(
                                margin: const EdgeInsets.only(right: 30.0),
                                decoration: BoxDecoration(
                                  boxShadow: [
                                    BoxShadow(
                                      color:
                                          Colors.blue.shade200.withOpacity(0.5),
                                      blurRadius: 8,
                                      offset: const Offset(0, 3),
                                    ),
                                  ],
                                ),
                                child: hasAdminPermissions
                                    ? ElevatedButton.icon(
                                        onPressed: hasAdminPermissions
                                            ? () {
                                                Navigator.pushNamed(context,
                                                    Routes.requestUser);
                                              }
                                            : () {
                                                _showPermissionDeniedDialog(
                                                    context, "Requested User");
                                              },
                                        icon: const Icon(Icons.person_search),
                                        label: Text(AppLocalizations.of(context)
                                            .btn_requested_users),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.blue,
                                          foregroundColor: Colors.white,
                                          elevation: 0,
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 16.0, vertical: 10.0),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8.0),
                                          ),
                                        ),
                                      )
                                    : null,
                              ),
                              Container(
                                margin: const EdgeInsets.only(left: 10.0),
                                decoration: BoxDecoration(
                                  boxShadow: [
                                    BoxShadow(
                                      color:
                                          Colors.blue.shade200.withOpacity(0.5),
                                      blurRadius: 8,
                                      offset: const Offset(0, 3),
                                    ),
                                  ],
                                ),
                                child: ElevatedButton.icon(
                                  onPressed: (hasAddUserPermissions ||
                                          hasAdminPermissions)
                                      ? () {
                                          Navigator.pushNamed(
                                              context, Routes.addUser);
                                        }
                                      : () {
                                          _showPermissionDeniedDialog(
                                              context, "Add new user");
                                        },
                                  icon: const Icon(Icons.person_add),
                                  label: Text(hasAdminPermissions
                                      ? AppLocalizations.of(context)
                                          .btn_new_user
                                      : AppLocalizations.of(context)
                                          .btn_request_for_new_user),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                    elevation: 0,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16.0, vertical: 10.0),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Container(
                            constraints: BoxConstraints(
                                maxWidth:
                                    MediaQuery.of(context).size.width * 0.95),
                            child: Row(
                              children: [
                                Expanded(
                                  // Wrap TextField with Expanded
                                  child: TextField(
                                    onSubmitted: (value) {
                                      filterUsers(value);
                                    },
                                    controller: controller,
                                    decoration: InputDecoration(
                                      filled: true,
                                      fillColor: Colors.grey.shade100,
                                      prefixIcon: const Icon(Icons.search,
                                          color: Colors.blue),
                                      hintText: AppLocalizations.of(context)
                                          .form_search_users,
                                      suffixIcon: searchLoading
                                          ? const Padding(
                                              padding: EdgeInsets.all(12.0),
                                              child: SizedBox(
                                                width: 20,
                                                height: 20,
                                                child:
                                                    CircularProgressIndicator(
                                                  strokeWidth: 2,
                                                  color: Colors.blue,
                                                ),
                                              ),
                                            )
                                          : null,
                                      border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(12.0),
                                        borderSide: BorderSide.none,
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 16.0, horizontal: 16.0),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),
                                ElevatedButton(
                                  onPressed: () {
                                    filterUsers(controller.text);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16, horizontal: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  child: const Icon(Icons.search),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                          // New User button and Requested Users button side by side
                        ],
                      ),
                    ),
                    Expanded(
                      child: filteredUsers.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.person_search,
                                      size: 64, color: Colors.grey.shade400),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No users found',
                                    style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.grey.shade600),
                                  ),
                                ],
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Card(
                                elevation: 2,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: SingleChildScrollView(
                                  controller: _scrollController,
                                  scrollDirection: Axis.vertical,
                                  child: SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: DataTable(
                                      showCheckboxColumn: false,
                                      columnSpacing:
                                          9.0, // Decreased column spacing
                                      headingRowColor: WidgetStateProperty
                                          .resolveWith<Color>(
                                        (Set<WidgetState> states) =>
                                            Colors.blue.shade50,
                                      ),
                                      headingTextStyle: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blue,
                                        fontSize: 14, // Reduced font size
                                      ),
                                      border: TableBorder(
                                        horizontalInside: BorderSide(
                                          color: Colors.grey.shade200,
                                          width: 1,
                                        ),
                                      ),
                                      dataRowHeight: 65,
                                      columns: const [
                                        DataColumn(
                                          label: Padding(
                                            padding: EdgeInsets.only(
                                                left:
                                                    18.0), // Added left margin
                                            child: Text(
                                              'NAME',
                                              style: TextStyle(
                                                fontSize: 10,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        ),
                                        DataColumn(
                                          label: Text(
                                            'PHONE NUMBER',
                                            style: TextStyle(
                                              fontSize: 10,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                        DataColumn(
                                          label: Padding(
                                            padding: EdgeInsets.only(
                                                left:
                                                    16.0), // Added left margin
                                            child: Text(
                                              'STATUS',
                                              style: TextStyle(
                                                fontSize: 10,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        ),
                                      ],
                                      rows: List.generate(
                                        filteredUsers.length,
                                        (index) {
                                          final user = filteredUsers[index];
                                          String name = '';

                                          final nameField =
                                              user['username'] ?? 'N/A';
                                          final regex = RegExp(r'\d+');
                                          if (regex.hasMatch(nameField)) {
                                            name = nameField
                                                .replaceAll(RegExp(r'\d|_'), '')
                                                .trim();
                                          } else {
                                            name = nameField
                                                .replaceAll('_', ' ')
                                                .trim();
                                            if (name.isEmpty) {
                                              name =
                                                  nameField; // Fallback to original if empty after replacemen
                                            }
                                          }

                                          String userStatus =
                                              user['user_status'] ?? 'N/A';

                                          return DataRow(
                                            onSelectChanged: (_) {
                                              _showUserDetailsDialog(
                                                  context, user);
                                            },
                                            cells: [
                                              DataCell(
                                                Row(
                                                  children: [
                                                    CircleAvatar(
                                                      backgroundColor:
                                                          Colors.blue.shade100,
                                                      child: Text(
                                                        name.isNotEmpty
                                                            ? name[0]
                                                                .toUpperCase()
                                                            : '?',
                                                        style: const TextStyle(
                                                            color: Colors.blue),
                                                      ),
                                                    ),
                                                    const SizedBox(width: 12),
                                                    Text(
                                                      name,
                                                      style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              DataCell(
                                                Row(
                                                  children: [
                                                    // Removed the calling icon
                                                    Text(
                                                        user['phone'] ?? 'N/A'),
                                                  ],
                                                ),
                                              ),
                                              DataCell(
                                                Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 12,
                                                      vertical: 6),
                                                  decoration: BoxDecoration(
                                                    color:
                                                        _getStatusBackgroundColor(
                                                            userStatus),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            16),
                                                    border: Border.all(
                                                      color:
                                                          _getStatusBorderColor(
                                                              userStatus),
                                                    ),
                                                  ),
                                                  child: Text(
                                                    userStatus.toUpperCase(),
                                                    style: TextStyle(
                                                      color:
                                                          _getStatusTextColor(
                                                              userStatus),
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      fontSize: 12,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                    ),
                    if (isLoading && page > 1)
                      const Padding(
                        padding: EdgeInsets.symmetric(vertical: 16.0),
                        child: Center(
                          child: CircularProgressIndicator(
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.blue),
                          ),
                        ),
                      ),
                  ],
                ),
        ),
      ),
    );
  }
}

class UserDetailsDialog extends StatefulWidget {
  final dynamic user;
  final Function(BuildContext, String) navigateToUpdateUserScreen;
  final bool hasAdminPermissions;
  final bool hasUpdateUserPermissions;
  final bool hasUserStatusPermissions;

  const UserDetailsDialog({
    super.key,
    required this.user,
    required this.navigateToUpdateUserScreen,
    required this.hasAdminPermissions,
    required this.hasUpdateUserPermissions,
    required this.hasUserStatusPermissions,
  });

  @override
  _UserDetailsDialogState createState() => _UserDetailsDialogState();
}

class _UserDetailsDialogState extends State<UserDetailsDialog> {
  String? selectedStatus;
  bool isUpdating = false;
  String? token;
  String? userName;
  String? userId;
  String? userPhone;
  String? UserWhatsapp;
  String? userType;
  Map<String, dynamic>? _userData;
  final _formKey = GlobalKey<FormState>();

  // Date controllers for suspension dates
  DateTime? suspensionFromDate;
  DateTime? suspensionToDate;

  @override
  void initState() {
    super.initState();
    final userModel = Provider.of<UserModel>(context, listen: false);
    setState(() {
      token = userModel.token;
      userPhone = widget.user['phone'];
      UserWhatsapp = widget.user['whatsapp_number'];
      selectedStatus = widget.user['user_status'];
      userType = userModel.userType;
    });
    _UserDetails();
  }

  void _UserDetails() async {
    setState(() => isUpdating = true);
    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;

      final Map<String, dynamic> userData =
          await AuthService.getUserByMobile(userPhone!, token);
      String? accessToken = userData['access_token'];

      if (accessToken == null) {
        showErrorModal(context, 'Access token not found', 'Error', () {});
        return;
      }

      final List<UserInfo> userIdInfo =
          await UserService.getUserInfo(accessToken);
      if (userIdInfo.isNotEmpty) {
        userData['user_id'] = userIdInfo[0].id.toString();
      }

      setState(() {
        _userData = userData;
      });
      // print("USER_DATA: " + await UserService.getUserInfo(accessToken).toString());
    } catch (error) {
      showErrorModal(context, '$error'.toString(), 'Error', () {});
    } finally {
      setState(() => isUpdating = false);
    }
  }

  Future<void> _selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isFromDate
          ? (suspensionFromDate ?? DateTime.now())
          : (suspensionToDate ?? DateTime.now().add(const Duration(days: 30))),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          suspensionFromDate = picked;
          // If "To" date is before "From" date, reset it
          if (suspensionToDate != null && suspensionToDate!.isBefore(picked)) {
            suspensionToDate = null;
          }
        } else {
          // Ensure "To" date is not before "From" date
          if (suspensionFromDate != null &&
              picked.isBefore(suspensionFromDate!)) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('End date cannot be before start date'),
                backgroundColor: Colors.red,
              ),
            );
            return;
          }
          suspensionToDate = picked;
        }
      });
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Select Date';
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Widget _buildDateSelector(
      String label, DateTime? selectedDate, bool isFromDate) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _selectDate(context, isFromDate),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatDate(selectedDate),
                  style: TextStyle(
                    fontSize: 16,
                    color: selectedDate == null
                        ? Colors.grey.shade500
                        : Colors.black,
                  ),
                ),
                Icon(
                  Icons.calendar_today,
                  size: 20,
                  color: Colors.grey.shade600,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _updateUserStatus(BuildContext context) async {
    if (!_formKey.currentState!.validate()) return;

    // Validate suspension dates if status is suspended
    if (selectedStatus == 'suspended') {
      if (suspensionFromDate == null || suspensionToDate == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select both suspension dates'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      if (suspensionToDate!.isBefore(suspensionFromDate!)) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('End date cannot be before start date'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }

    if (isUpdating) return;
    setState(() {
      isUpdating = true;
    });

    try {
      final String userId = _userData!['user_id']?.toString() ?? '';
      final dynamic userType = _userData!['user_type'] ?? '';
      final bool isEnabled = selectedStatus == 'Enabled';

      final Map<String, dynamic> updateData = {
        'user_id': userId,
        'user_type': userType,
        'username':
            widget.user['username_unique'] ?? widget.user['username'] ?? '',
        'status': selectedStatus?.toLowerCase(),
        'phone': _userData!['phone'] ?? '',
        'whatsapp_number': UserWhatsapp,
        'emp_number': _userData!['emp_number'] ?? '',
        'division': _userData!['division'] ?? '',
        'zone': _userData!['zone'] ?? '',
        'depo': _userData!['depot'] ?? '',
      };

      // Add suspension dates if status is suspended
      if (selectedStatus == 'suspended') {
        updateData['suspension_from'] =
            suspensionFromDate!.toIso8601String().split('T')[0];
        updateData['suspension_to'] =
            suspensionToDate!.toIso8601String().split('T')[0];
      }

      final response = await UpdateUserService.submitUpdateRequest(
        token!,
        updateData,
      );
      if (response['message']?.toLowerCase().contains('success') ?? false) {
        showSuccessModal(
          context,
          '${response['message']}',
          "Success",
          () => Navigator.pushNamed(context, Routes.userInfo),
        );
      }
    } catch (error) {
      if (context.mounted) {
        showErrorModal(context, error.toString(), "Error", () {});
      }
    } finally {
      if (mounted) {
        setState(() {
          isUpdating = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final dialogWidth = MediaQuery.of(context).size.width * 0.85;
    final maxContentWidth = dialogWidth - 40;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 5,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: dialogWidth,
          minWidth: dialogWidth,
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User Details Section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircleAvatar(
                            backgroundColor: Colors.blue.shade100,
                            radius: 24,
                            child: Text(
                              (widget.user['username']?[0] ?? '?')
                                  .toUpperCase(),
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'User Details',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  widget.user['username'] ?? 'N/A',
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, size: 24),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      constraints: const BoxConstraints(),
                      padding: EdgeInsets.zero,
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                // Divider
                Divider(color: Colors.grey.shade300, thickness: 1),
                const SizedBox(height: 16),
                // Information section
                _buildDetailRowWithIcon(
                    Icons.person,
                    AppLocalizations.of(context).text_username,
                    widget.user['username'] ?? 'N/A',
                    maxContentWidth),
                _buildDetailEmailRowWithIcon(
                    context,
                    Icons.email,
                    AppLocalizations.of(context).text_email,
                    widget.user['email'] ?? 'N/A',
                    maxContentWidth),
                _buildDetailPhoneRowWithIcon(
                    Icons.phone,
                    AppLocalizations.of(context).text_phone,
                    widget.user['phone'] ?? 'N/A',
                    maxContentWidth),
                _buildDetailRowWithIcon(
                    Icons.security,
                    AppLocalizations.of(context).text_role,
                    widget.user['role'] ?? 'N/A',
                    maxContentWidth),
                _buildDetailRowWithIcon(
                    Icons.location_city,
                    AppLocalizations.of(context).text_depot,
                    widget.user['depot'] ?? 'N/A',
                    maxContentWidth),
                // Status Update Section
                const SizedBox(height: 10),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'User Status',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            DropdownButtonFormField<String>(
                              value: selectedStatus,
                              decoration: InputDecoration(
                                filled: true,
                                fillColor: Colors.white,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.blue.shade400),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                              ),
                              items: [
                                AppLocalizations.of(context)
                                    .text_status_enabled,
                                AppLocalizations.of(context)
                                    .text_status_disabled,
                                AppLocalizations.of(context)
                                    .text_status_suspended,
                                AppLocalizations.of(context).text_status_blocked
                              ]
                                  .map((status) => DropdownMenuItem(
                                        value: status.toLowerCase(),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              status == 'Enabled'
                                                  ? Icons.check_circle
                                                  : status == 'Disabled'
                                                      ? Icons.cancel
                                                      : status == 'Suspended'
                                                          ? Icons.pause_circle
                                                          : Icons.block,
                                              color: status == 'Enabled'
                                                  ? Colors.green
                                                  : status == 'Disabled'
                                                      ? Colors.red
                                                      : status == 'Suspended'
                                                          ? Colors.orange
                                                          : Colors.black,
                                              size: 18,
                                            ),
                                            const SizedBox(width: 8),
                                            Text(status),
                                          ],
                                        ),
                                      ))
                                  .toList(),
                              onChanged: (value) {
                                setState(() {
                                  selectedStatus = value;
                                  // Reset suspension dates when status changes
                                  if (value != 'suspended') {
                                    suspensionFromDate = null;
                                    suspensionToDate = null;
                                  }
                                });
                              },
                              isExpanded: true,
                            ),
                            // Show date selectors only when Suspended is selected
                            // Show date selectors only when Suspended is selected
                            if (selectedStatus == 'suspended') ...[
                              const SizedBox(height: 16),
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.orange.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border:
                                      Border.all(color: Colors.orange.shade200),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.schedule,
                                          size: 18,
                                          color: Colors.orange.shade700,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Suspension Period',
                                          style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.orange.shade700,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    // From Date - First Row
                                    SizedBox(
                                      width: double.infinity,
                                      child: _buildDateSelector(
                                        'From Date',
                                        suspensionFromDate,
                                        true,
                                      ),
                                    ),
                                    const SizedBox(height: 12),
                                    // To Date - Second Row
                                    SizedBox(
                                      width: double.infinity,
                                      child: _buildDateSelector(
                                        'To Date',
                                        suspensionToDate,
                                        false,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            if (isUpdating) return;
                            if (!_formKey.currentState!.validate()) return;
                            _updateUserStatus(context);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: isUpdating
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                              : Text(
                                  userType == "railway admin"
                                      ? AppLocalizations.of(context)
                                          .btn_update_status
                                      : AppLocalizations.of(context)
                                          .btn_request_for_update_status,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 18),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.only(bottom: 16),
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).pop();
                      widget.navigateToUpdateUserScreen(
                          context, widget.user['phone']);
                    },
                    icon: const Icon(Icons.edit),
                    label: userType == "railway admin"
                        ? Text(AppLocalizations.of(context)
                            .btn_update_user_details)
                        : Text(AppLocalizations.of(context)
                            .btn_request_for_update_user_details),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 2,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRowWithIcon(
      IconData icon, String label, String value, double maxWidth) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Colors.blue, size: 20),
          ),
          const SizedBox(width: 16),
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailPhoneRowWithIcon(
      IconData icon, String label, String value, double maxWidth) {
    return Padding(
      padding: const EdgeInsets.only(top: 8, bottom: 8, right: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            padding: EdgeInsets.zero,
            style: IconButton.styleFrom(
              backgroundColor: Colors.blue.shade50,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              minimumSize: const Size(36, 36),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            icon: const Icon(
              Icons.call,
              color: Colors.blueAccent,
              size: 20,
            ),
            onPressed: () async {
              const countryCode = '+91';
              final formattedNumber = '$countryCode$value';
              final url = 'tel:$formattedNumber';
              if (await canLaunch(url)) {
                await launch(url);
              } else {
                throw 'Could not open the dialer';
              }
            },
          ),
          const SizedBox(width: 16),
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

Widget _buildDetailEmailRowWithIcon(BuildContext context, IconData icon,
    String label, String value, double maxWidth) {
  return Padding(
    padding: const EdgeInsets.only(top: 8, bottom: 8, right: 8),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          padding: EdgeInsets.zero,
          style: IconButton.styleFrom(
            backgroundColor: Colors.blue.shade50,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            minimumSize: const Size(36, 36),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          icon: const Icon(
            Icons.email,
            color: Colors.blueAccent,
            size: 20,
          ),
          // onPressed: () async {
          //   // Validate email address
          //   if (!value.contains('@')) {
          //     showErrorModal(
          //       context,
          //       'Invalid email address',
          //       'Error',
          //       () => Navigator.of(context).pop(),
          //     );
          //     return;
          //   }

          //   // Try Gmail first
          //   try {
          //     // First try Gmail app
          //     final gmailUri = Uri(
          //       scheme: 'https',
          //       host: 'mail.google.com',
          //       path: '',
          //       queryParameters: {
          //         'view': 'cm',
          //         'fs': '1',
          //         'tf': '1',
          //         'to': value,
          //         'su': 'Sarva Suvidhaen Pvt Ltd - Personal Note',
          //         'body':
          //             'Hello,\n\nI am contacting you regarding your account.',
          //       },
          //     );

          //     final bool canLaunchGmail = await canLaunchUrl(gmailUri);
          //     if (canLaunchGmail) {
          //       await launchUrl(gmailUri);
          //       return;
          //     }

          //     // If Gmail fails, try default email client
          //     final emailUri = Uri(
          //       scheme: 'mailto',
          //       path: value,
          //       queryParameters: {
          //         'subject': 'Sarva Suvidhaen Pvt Ltd - Personal Note',
          //         'body':
          //             'Hello,\n\nI am contacting you regarding your account.',
          //       },
          //     );

          //     final bool canLaunchEmail = await canLaunchUrl(emailUri);
          //     if (!canLaunchEmail) {
          //       showErrorModal(
          //         context,
          //         'No email client found on your device. Please install Gmail or an email app.',
          //         'Error',
          //         () => Navigator.of(context).pop(),
          //       );
          //       return;
          //     }

          //     final bool launchSuccess = await launchUrl(emailUri);
          //     if (!launchSuccess) {
          //       showErrorModal(
          //         context,
          //         'Failed to open email client. Please try again.',
          //         'Error',
          //         () => Navigator.of(context).pop(),
          //       );
          //     }
          onPressed: () => _launchEmailClient(context, value),
        ),
        const SizedBox(width: 16),
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

Future<void> _launchEmailClient(
    BuildContext context, String emailAddress) async {
  // Validate email address
  if (!emailAddress.contains('@')) {
    showErrorModal(
      context,
      'Invalid email address',
      'Error',
      () => Navigator.of(context).pop(),
    );
    return;
  }

  const String subject = 'Sarva Suvidhaen Pvt Ltd - Personal Note';
  const String body = 'Hello,\n\nI am contacting you regarding your account.';

  // Define the Gmail web URL (for web platform and fallbacks)
  final webMailtoUrl = Uri.parse(
    'https://mail.google.com/mail/?view=cm&fs=1&tf=1&to=$emailAddress&su=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}',
  );

  try {
    if (kIsWeb) {
      // Web platform - directly open Gmail web compose
      await launchUrl(webMailtoUrl);
    } else if (Platform.isAndroid) {
      // Android platform - try native app first
      bool launched = false;

      // Try Gmail app Intent
      final Uri intentUri = Uri.parse(
        'intent:#Intent;action=android.intent.action.SENDTO;type=text/plain;package=com.google.android.gm;S.android.intent.extra.EMAIL=${Uri.encodeComponent(emailAddress)};S.android.intent.extra.SUBJECT=${Uri.encodeComponent(subject)};S.android.intent.extra.TEXT=${Uri.encodeComponent(body)};end',
      );

      try {
        launched = await launchUrl(intentUri,
            mode: LaunchMode.externalNonBrowserApplication);
      } catch (e) {
        print("Intent failed: $e");
      }

      // If Gmail Intent failed, try standard mailto
      if (!launched) {
        final mailtoUri = Uri.parse(
            'mailto:$emailAddress?subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}');
        try {
          launched =
              await launchUrl(mailtoUri, mode: LaunchMode.externalApplication);
        } catch (e) {
          print("Mailto failed: $e");
        }
      }

      // Last resort: open Gmail in browser
      if (!launched) {
        await launchUrl(webMailtoUrl, mode: LaunchMode.externalApplication);
      }
    } else {
      // Other platforms - try mailto first, then web
      final mailtoUri = Uri(
        scheme: 'mailto',
        path: emailAddress,
        queryParameters: {
          'subject': subject,
          'body': body,
        },
      );

      bool launched = false;
      try {
        launched =
            await launchUrl(mailtoUri, mode: LaunchMode.externalApplication);
      } catch (e) {
        print("Mailto failed: $e");
      }

      if (!launched) {
        await launchUrl(webMailtoUrl, mode: LaunchMode.externalApplication);
      }
    }
  } catch (e) {
    showErrorModal(
      context,
      'Unable to open email client: ${e.toString()}',
      'Error',
      () => Navigator.of(context).pop(),
    );
  }
}

// Helper function to show error messages
void showErrorModal(
    BuildContext context, String message, String title, VoidCallback onClose) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: onClose,
            child: const Text('OK'),
          ),
        ],
      );
    },
  );
}

Color _getStatusBackgroundColor(String status) {
  switch (status.toLowerCase()) {
    case 'enabled':
      return Colors.green.shade50;
    case 'disabled':
      return Colors.red.shade50;
    case 'suspended':
      return Colors.orange.shade50;
    case 'blocked':
      return Colors.grey.shade100;
    default:
      return Colors.grey.shade50;
  }
}

Color _getStatusBorderColor(String status) {
  switch (status.toLowerCase()) {
    case 'enabled':
      return Colors.green.shade300;
    case 'disabled':
      return Colors.red.shade300;
    case 'suspended':
      return Colors.orange.shade300;
    case 'blocked':
      return Colors.grey.shade400;
    default:
      return Colors.grey.shade300;
  }
}

Color _getStatusTextColor(String status) {
  switch (status.toLowerCase()) {
    case 'enabled':
      return Colors.green.shade700;
    case 'disabled':
      return Colors.red.shade700;
    case 'suspended':
      return Colors.orange.shade700;
    case 'blocked':
      return Colors.grey.shade700;
    default:
      return Colors.grey.shade600;
  }
}
