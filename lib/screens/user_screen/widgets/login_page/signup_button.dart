import 'package:flutter/material.dart';
import 'package:railops/routes.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

Widget renderSignUpButton(BuildContext context) {
  double screenWidth = MediaQuery.of(context).size.width;
  double buttonWidth = screenWidth * 0.9;
  void signUp() {
    Navigator.pushNamed(context, Routes.signUp);
  }

  return OutlinedButton(
    style: OutlinedButton.styleFrom(
      backgroundColor: Colors.white,
      foregroundColor: Colors.blue,
      fixedSize: Size(buttonWidth, 50),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
    ),
    onPressed: signUp,
    child: Text(
      AppLocalizations.of(context).btn_new_user_sign_up,
      style: const TextStyle(),
    ),
  );
}
