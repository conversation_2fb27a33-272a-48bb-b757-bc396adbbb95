import 'package:flutter/material.dart';
import 'package:railops/routes.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

Widget renderMobileLogInButton(BuildContext context) {
  double screenWidth = MediaQuery.of(context).size.width;
  double buttonWidth = screenWidth * 0.9;
  void otpLogIn() async {
    await Navigator.pushReplacementNamed(context, Routes.mobileLogin);
  }

  return OutlinedButton(
    style: ElevatedButton.styleFrom(
      // backgroundColor: Color.fromARGB(255, 255, 255, 255),
      foregroundColor: const Color(0xFF313256),
      fixedSize: Size(buttonWidth, 50),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
    ),
    onPressed: otpLogIn,
    child: Text(
      AppLocalizations.of(context).btn_log_in_with_mobile,
      style: const TextStyle(
          // fontWeight: FontWeight.bold
          ),
    ),
  );
}
