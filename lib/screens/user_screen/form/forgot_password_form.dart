import 'package:flutter/material.dart';
import 'package:railops/routes.dart';
import 'package:railops/services/otp_services/index.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ForgotPasswordForm extends StatefulWidget {
  const ForgotPasswordForm({super.key});
  @override
  _ForgotPasswordForm createState() => _ForgotPasswordForm();
}

class _ForgotPasswordForm extends State<ForgotPasswordForm> {
  String? _value;
  bool _isLoading = false;
  final String _type = 'forgot_password_otp';
  bool _showModal = false;

  void sendMail() async {
    setState(() {
      _isLoading = true;
    });
    try {
      final sendOtpResponse = await SignUpOtp.sendOtp(_value!, _type);
      setState(() {
        _isLoading = false;
      });
      _showErrorModal(context, sendOtpResponse);
      Future.delayed(const Duration(seconds: 4), () {
        Navigator.pushReplacementNamed(context, Routes.login);
      });
    } catch (e) {
      _showErrorModal(context, '$e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorModal(BuildContext context, String errorMessage) {
    String dialogTitle;
    String dialogMessage;

    if (errorMessage ==
        AppLocalizations.of(context).msg_password_reset_email_sent) {
      dialogTitle = AppLocalizations.of(context).text_success;
      dialogMessage = errorMessage;
    } else if (errorMessage == 'Unexpected null value.') {
      dialogTitle = AppLocalizations.of(context).text_error;
      dialogMessage =
          AppLocalizations.of(context).error_enter_valid_email_address;
    } else {
      dialogTitle = AppLocalizations.of(context).text_error;
      dialogMessage = errorMessage;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(dialogTitle),
          content: Text(dialogMessage),
          actions: <Widget>[
            TextButton(
              child: Text(AppLocalizations.of(context).text_close),
              onPressed: () {
                setState(() {
                  _showModal = false;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double buttonWidth = screenWidth * 0.8;

    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(
                    height: 50.0), // Adjusted space for better layout
                Text(
                  AppLocalizations.of(context).text_forgot_password_instruction,
                  style: const TextStyle(
                      fontSize: 16.0, color: Colors.black87), // Blue text color
                  textAlign: TextAlign.center, // Center the text
                ),
                const SizedBox(height: 32.0),
                TextFormField(
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).form_email_label,
                    labelStyle: const TextStyle(
                        color: Colors.black87), // Blue label color
                    border: const OutlineInputBorder(),
                    focusedBorder: const OutlineInputBorder(
                      borderSide: BorderSide(
                          color: Color(0xFF1E88E5)), // Blue border when focused
                    ),
                  ),
                  keyboardType: TextInputType.emailAddress,
                  onChanged: (String? value) {
                    _value = value!;
                  },
                ),
                const SizedBox(height: 32.0),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        Colors.blue.shade100, // Blue background color
                    foregroundColor: Colors.black87,
                    fixedSize: Size(buttonWidth, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(12), // Rounded corners
                    ),
                    side: const BorderSide(
                        color: Colors.black87, width: 0.5), // Border
                  ),
                  onPressed: _isLoading ? null : sendMail,
                  child: _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : Text(
                          AppLocalizations.of(context)
                              .btn_send_verification_mail,
                          style: const TextStyle(
                            fontSize: 16, // Consistent font size
                          ),
                        ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
