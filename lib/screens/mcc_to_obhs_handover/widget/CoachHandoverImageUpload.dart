import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/trip_report_services/trip_report_services.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';
import 'package:flutter/foundation.dart';
import 'package:railops/screens/trip_report/widget/StatusSelectionModal.dart';
import 'package:railops/services/assign_ehk_ca_services/assign_ehk_ca_services.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:geolocator/geolocator.dart';
import 'package:railops/widgets/error_modal.dart';
import 'package:railops/types/trip_report_type/trip_report_type.dart';
import 'package:railops/screens/trip_report/widget/TripReportFilePreview.dart';

// NOTE: Localization for this file is temporarily blocked due to Flutter SDK compatibility issues.
// The flutter gen-l10n command is currently failing with "Null check operator used on a null value" error.
// ARB entries for this file (31 strings) have been prepared and are ready for implementation
// once the Flutter SDK issue is resolved. This affects the systematic i18n implementation plan.
// TODO: Complete localization implementation once Flutter gen-l10n is working properly.

class CoachHandoverImageUploadPage extends StatefulWidget {
  final String trainNumber;
  final String journeyDate;
  final String coach;
  final List<Map<String, String>> handoverItems;

  const CoachHandoverImageUploadPage({
    super.key,
    required this.trainNumber,
    required this.journeyDate,
    required this.coach,
    required this.handoverItems,
  });

  @override
  _CoachHandoverImageUploadPageState createState() =>
      _CoachHandoverImageUploadPageState();
}

class _CoachHandoverImageUploadPageState
    extends State<CoachHandoverImageUploadPage> {
  final ImagePicker _picker = ImagePicker();
  List<XFile> _selectedFiles = [];
  bool _isFileLoading = false;
  String? token;
  bool isUploading = false;
  final TextEditingController _commentController = TextEditingController();
  List<int> selectedMainProblems = [];
  List<int> selectedSubProblems = [];
  Map<int, String> problemOptions = {};
  bool showLoader = false;
  List<dynamic> allIssues = [];
  List<dynamic> issues = [];
  List<dynamic> subissues = [];
  Map<String, int> selectedStatuses = {};
  final Map<int, String> statusChoices = {
    1: "Reported",
    2: "Fixed",
    3: "Resolved"
  };
  late List<String> caUsers = [];
  late List<String> obhsUsers = [];
  late List<String> ehkUsers = [];
  late List<String> users = [];
  Map<String, dynamic> issueStatusData = {};
  List<TripReportType> uploadedFiles = [];

  @override
  void initState() {
    super.initState();
    final userModel = Provider.of<UserModel>(context, listen: false);
    token = userModel.token;
    _loadAllIssues();
    fetchIssuesStatus();
    fetchSpecificUsers("OBHS");
    fetchSpecificUsers("coach attendent");
    fetchSpecificUsers("EHK");
    _fetchUploadedFiles();
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _loadAllIssues() async {
    setState(() {
      showLoader = true;
    });

    try {
      List<dynamic> response = await TripReportServices.getAllIssues();
      setState(() {
        allIssues = response;
        problemOptions = {
          for (var issue in response) issue["id"]: issue["name"]
        };
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Failed to load issues: ${e.toString()}")),
      );
    } finally {
      setState(() {
        showLoader = false;
      });
    }
  }

  Future<void> fetchIssuesStatus() async {
    try {
      final response = await TripReportServices.getIssueStatus(
          widget.trainNumber,
          widget.journeyDate,
          widget.coach,
          token!,
          "mcc_to_obhs");

      final data = response["data"];
      setState(() {
        issues = data['issues'] ?? [];
        subissues = data['subissues'] ?? [];
        selectedStatuses.clear();

        // Update selectedMainProblems and selectedSubProblems from fetched data
        selectedMainProblems =
            issues.map((issue) => issue['issue_id'] as int).toList();
        selectedSubProblems = subissues
            .map((subissue) => subissue['subissue_id'] as int)
            .toList();

        // Update statuses
        for (var issue in issues) {
          final status = issue['status']?.toString() ?? "1";
          selectedStatuses["issue_${issue['issue_id']}"] =
              int.tryParse(status) ?? 1;
        }
        for (var subissue in subissues) {
          final status = subissue['status']?.toString() ?? "1";
          selectedStatuses["subissue_${subissue['subissue_id']}"] =
              int.tryParse(status) ?? 1;
        }
      });
    } catch (e) {
      print("Error fetching issues: $e");
    }
  }

  Future<void> fetchSpecificUsers(String userType) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;

    if (token.isNotEmpty) {
      final response =
          await AdminAssignService.fetchSpecificUsers(userType, token);

      setState(() {
        if (userType == "EHK") {
          ehkUsers = response.map<String>((ehk) => ehk["username"]).toList();
        } else if (userType == "coach attendent") {
          caUsers = response.map<String>((ca) => ca["username"]).toList();
        } else if (userType == "OBHS") {
          obhsUsers = response.map<String>((ca) => ca["username"]).toList();
        }
        users = <dynamic>{...obhsUsers, ...caUsers, ...ehkUsers}.toList()
          ..sort((a, b) => a.toLowerCase().compareTo(b.toLowerCase()));
      });
    }
  }

  void _showProblemSelectionDialog() {
    List<int> tempSelectedMainProblems = List.from(selectedMainProblems);
    List<int> tempSelectedSubProblems = List.from(selectedSubProblems);
    int? expandedIssueId;

    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setStateModal) {
            return Container(
              padding: const EdgeInsets.all(16),
              height: MediaQuery.of(context).size.height * 0.8,
              child: Column(
                children: [
                  Text(
                    "Select Issues for Coach ${widget.coach}",
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: ListView.builder(
                      itemCount: problemOptions.length,
                      itemBuilder: (context, index) {
                        final issue = problemOptions.entries.elementAt(index);
                        final subissues =
                            (allIssues[index]['subissues'] as List)
                                .cast<Map<String, dynamic>>();

                        bool isAnySubissueSelected = subissues.any((subissue) =>
                            tempSelectedSubProblems.contains(subissue['id']));

                        return Column(
                          children: [
                            ExpansionTile(
                              key: Key(
                                  'expansion_${issue.key}_${expandedIssueId == issue.key}'),
                              title: CheckboxListTile(
                                controlAffinity:
                                    ListTileControlAffinity.leading,
                                value: tempSelectedMainProblems
                                    .contains(issue.key),
                                onChanged: (isChecked) {
                                  setStateModal(() {
                                    if (isChecked == true) {
                                      tempSelectedMainProblems.add(issue.key);
                                      expandedIssueId = issue.key;
                                    } else {
                                      tempSelectedMainProblems
                                          .remove(issue.key);
                                    }
                                  });
                                },
                                title: Row(
                                  children: [
                                    Icon(getIconForProblem(issue.key),
                                        color: Colors.blue, size: 20),
                                    const SizedBox(width: 12),
                                    Text(issue.value),
                                  ],
                                ),
                              ),
                              initiallyExpanded: expandedIssueId == issue.key ||
                                  isAnySubissueSelected,
                              onExpansionChanged: (expanded) {
                                setStateModal(() {
                                  expandedIssueId = expanded ? issue.key : null;
                                });
                              },
                              children: [
                                ...subissues.map((subissue) {
                                  return Padding(
                                    padding: const EdgeInsets.only(left: 45.0),
                                    child: CheckboxListTile(
                                      controlAffinity:
                                          ListTileControlAffinity.leading,
                                      value: tempSelectedSubProblems
                                          .contains(subissue['id']),
                                      onChanged: (isChecked) {
                                        setStateModal(() {
                                          if (isChecked == true) {
                                            tempSelectedSubProblems
                                                .add(subissue['id']);
                                          } else {
                                            tempSelectedSubProblems
                                                .remove(subissue['id']);
                                          }
                                        });
                                      },
                                      title: Text(subissue['name']),
                                    ),
                                  );
                                }),
                              ],
                            ),
                            const Divider(height: 1),
                          ],
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        selectedMainProblems = tempSelectedMainProblems;
                        selectedSubProblems = tempSelectedSubProblems;
                      });
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 24),
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.black,
                      side: const BorderSide(color: Colors.black, width: 0.5),
                    ),
                    child: const Text("Save Selection"),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  IconData getIconForProblem(int problemId) {
    switch (problemId) {
      case 1:
        return Icons.door_front_door;
      case 2:
        return Icons.wash;
      case 3:
        return Icons.lock_outline;
      case 4:
        return Icons.plumbing_sharp;
      default:
        return Icons.help_outline;
    }
  }

  Widget _buildButtonLabel() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final isAdmin = userModel.userType == 's2 admin';

    if (isAdmin) {
      return const Text(
        'No Issues',
        style: TextStyle(fontSize: 15),
      );
    }

    final uniqueIssueCount = selectedMainProblems.toSet().length;
    final uniqueSubIssueCount = selectedSubProblems.toSet().length;
    return Text(
      uniqueIssueCount > 0
          ? '$uniqueIssueCount Issue & $uniqueSubIssueCount SubIssue Selected'
          : 'Select Issues',
      style: const TextStyle(fontSize: 15),
    );
  }

  Future<void> _pickImage() async {
    final ImageSource? source = await showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            ListTile(
              leading: const Icon(Icons.photo),
              title: const Text('Gallery'),
              onTap: () => Navigator.pop(context, ImageSource.gallery),
            ),
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );

    if (source == null) return;

    final ImagePicker picker = ImagePicker();

    if (source == ImageSource.camera) {
      final bool? isVideo = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Select Media Type'),
          contentTextStyle: const TextStyle(fontSize: 16),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera),
                title: const Text('Image'),
                onTap: () => Navigator.pop(context, false),
              ),
              ListTile(
                leading: const Icon(Icons.videocam),
                title: const Text('Video'),
                onTap: () => Navigator.pop(context, true),
              ),
            ],
          ),
        ),
      );

      if (isVideo == true) {
        final XFile? file = await picker.pickVideo(source: ImageSource.camera);
        if (file != null) {
          setState(() {
            _selectedFiles.add(file);
          });
        }
      } else {
        final XFile? file = await picker.pickImage(source: ImageSource.camera);
        if (file != null) {
          setState(() {
            _selectedFiles.add(file);
          });
        }
      }
    } else if (source == ImageSource.gallery) {
      final List<XFile> pickedFiles = await picker.pickMultipleMedia();
      if (pickedFiles.isNotEmpty) {
        setState(() {
          _selectedFiles.addAll(pickedFiles);
        });
      }
    }
  }

  bool _isVideo(XFile file) {
    final extension = file.name.split('.').last.toLowerCase();
    return ['mp4', 'mov', 'avi', 'mkv'].contains(extension);
  }

  Future<Position?> _getCurrentPosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        await Geolocator.requestPermission();
      }
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        permission = await Geolocator.requestPermission();
      }
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } catch (e) {
      showErrorModal(
          context, 'Please Enable the location service!', "Error", () {});
      await Geolocator.requestPermission();
      return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);
    }
  }

  Future<void> _uploadImages() async {
    if (_selectedFiles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select images to upload')),
      );
      return;
    }

    if (selectedMainProblems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select at least one issue')),
      );
      return;
    }

    setState(() {
      isUploading = true;
    });

    try {
      Position? position = await _getCurrentPosition();
      final response = await TripReportServices.createTripImageReport(
          files: _selectedFiles,
          trainNumber: widget.trainNumber,
          date: widget.journeyDate,
          token: token!,
          latitude: position!.latitude.toString(),
          longitude: position.longitude.toString(),
          coach: widget.coach,
          issues: selectedMainProblems.join(","),
          subissues: selectedSubProblems.join(","),
          comment: _commentController.text,
          issueFor: "mcc_to_obhs");

      if (response != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content:
                  Text(response['message'] ?? 'Images uploaded successfully')),
        );
        Navigator.pop(context);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to upload images')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error uploading images: ${e.toString()}')),
      );
    } finally {
      setState(() {
        isUploading = false;
        _selectedFiles = [];
        _commentController.clear();
      });
    }
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          Text(
            value,
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  Future<void> _revertCoachStatus(String? selectedPerson, String? date,
      bool isSubIssue, int? id, int? statusId, String? statusType) async {
    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;
      final String updatedBy = userModel.userName;
      final int subIssueId = isSubIssue ? id! : 0;
      final int issueId = isSubIssue ? 0 : id!;
      const bool revert = true;
      await TripReportServices.updateCoachStatus(
          issueId,
          subIssueId,
          widget.journeyDate,
          widget.coach,
          widget.trainNumber,
          statusId!,
          statusType!.toLowerCase(),
          selectedPerson!,
          date!,
          updatedBy,
          token,
          revert);
      await fetchIssuesStatus();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating issue: $e')),
      );
    }
  }

  DataRow _buildDataRow(Map<String, dynamic> item, bool isSubissue, int index) {
    final id = item[isSubissue ? 'subissue_id' : 'issue_id'];
    final name = item[isSubissue ? 'subissue_name' : 'issue_name'];
    String reportedBy = item['reported_by'] ?? "N/A";
    String fixedBy = item['fixed_by'] ?? "N/A";
    String resolvedBy = item['resolved_by'] ?? "N/A";
    final compositeKey = isSubissue ? "subissue_$id" : "issue_$id";
    final currentStatus = selectedStatuses[compositeKey] ?? 1;
    final isResolved = resolvedBy != "N/A";

    return DataRow(
      cells: [
        DataCell(SizedBox(
          width: 180,
          child: Text(name ?? "Unknown"),
        )),
        DataCell(
          DropdownButton<int>(
            value: selectedStatuses[compositeKey],
            items: statusChoices.entries
                .map((entry) => DropdownMenuItem<int>(
                      value: entry.key,
                      child: Text(
                        entry.value,
                        style: TextStyle(
                          fontSize: 14,
                          color: isResolved ? Colors.grey : null,
                        ),
                      ),
                    ))
                .toList(),
            onChanged: isResolved
                ? null
                : (newStatus) {
                    setState(() {
                      selectedStatuses[compositeKey] = newStatus!;
                    });
                  },
          ),
        ),
        DataCell(_buildStatusButton(
          context,
          statusType: statusChoices[selectedStatuses[compositeKey]]!,
          statusId: selectedStatuses[compositeKey]!,
          isDisabled: isResolved,
          isSubIssue: isSubissue,
          id: id,
          name: name,
        )),
        DataCell(Text(reportedBy, style: const TextStyle(fontSize: 14))),
        DataCell(Text(fixedBy, style: const TextStyle(fontSize: 14))),
        DataCell(Text(resolvedBy, style: const TextStyle(fontSize: 14))),
      ],
    );
  }

  Widget _buildStatusButton(
    BuildContext context, {
    required String statusType,
    required int statusId,
    required bool isDisabled,
    required bool isSubIssue,
    required int id,
    required String name,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          height: 30,
          child: ElevatedButton(
            onPressed: isDisabled
                ? null
                : () async {
                    final clicked = await showDialog(
                      context: context,
                      builder: (context) => SelectionModalWidget(
                        statusType: statusType,
                        statusId: statusId,
                        isSubIssue: isSubIssue,
                        id: id,
                        name: name,
                        journeyDate: widget.journeyDate,
                        trainNumber: widget.trainNumber,
                        coach: widget.coach,
                        users: users,
                      ),
                    );
                    if (clicked == true) {
                      await fetchIssuesStatus();
                    }
                  },
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              textStyle: const TextStyle(fontSize: 12),
              backgroundColor: isDisabled ? null : Colors.blueAccent,
              foregroundColor: Colors.white,
            ),
            child: Text('$statusType By'),
          ),
        ),
        const SizedBox(width: 5),
        if (statusId == 3 &&
            Provider.of<UserModel>(context, listen: false)
                .userType
                .contains("admin"))
          SizedBox(
            height: 30,
            child: IconButton(
              onPressed: () async {
                _revertCoachStatus(widget.journeyDate, name, isSubIssue, id,
                    statusId, statusType);
                await fetchIssuesStatus();
              },
              icon: const Icon(Icons.replay, color: Colors.redAccent),
              tooltip: "Revert Status",
            ),
          ),
      ],
    );
  }

  Future<void> _fetchUploadedFiles() async {
    try {
      setState(() {
        _isFileLoading = true;
      });

      final response = await TripReportServices.fetchTripReportImages(
          trainNumber: widget.trainNumber,
          date: widget.journeyDate,
          token: token!,
          coach: widget.coach,
          reportFor: 'mcc_to_obhs');

      if (response != null) {
        setState(() {
          uploadedFiles = response;
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No images available')),
        );
      }

      setState(() {
        _isFileLoading = false;
      });
    } catch (e) {
      setState(() {
        _isFileLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error fetching images: $e')),
      );
    }
  }

  Future<void> _handleRefresh() async {
    await _fetchUploadedFiles();
  }

  @override
  Widget build(BuildContext context) {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final isAdmin = userModel.userType == 'admin';

    return Scaffold(
      appBar: const CustomAppBar(title: "Upload Handover Images"),
      drawer: const CustomDrawer(),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Train Details Card
              Card(
                elevation: 5,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: _buildDetailRow(
                                'Train Number: ', widget.trainNumber),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: _buildDetailRow('Coach: ', widget.coach),
                          ),
                        ],
                      ),
                      _buildDetailRow('Journey Date: ', widget.journeyDate),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Issue Selection Button
              ElevatedButton.icon(
                onPressed: _showProblemSelectionDialog,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black,
                  side: const BorderSide(color: Colors.black87, width: 0.5),
                ),
                icon: const Icon(Icons.checklist, color: Colors.blue),
                label: _buildButtonLabel(),
              ),

              const SizedBox(height: 20),

              // Issues Status Table
              if (issues.isNotEmpty || subissues.isNotEmpty)
                SizedBox(
                  height: 150,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.vertical,
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Container(
                        constraints: BoxConstraints(
                          minWidth: MediaQuery.of(context).size.width,
                        ),
                        child: DataTable(
                          border: TableBorder.all(color: Colors.grey),
                          columnSpacing: 10,
                          headingRowColor: WidgetStateColor.resolveWith(
                              (states) => Colors.blueGrey.shade100),
                          columns: const [
                            DataColumn(
                                label: Text("Issue/Subissue",
                                    style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14))),
                            DataColumn(
                                label: Text("Status",
                                    style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14))),
                            DataColumn(
                                label: Text("Submit/Update",
                                    style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14))),
                            DataColumn(
                                label: Text("Reported_by",
                                    style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14))),
                            DataColumn(
                                label: Text("Fixed_by",
                                    style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14))),
                            DataColumn(
                                label: Text("Resolved_by",
                                    style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14)))
                          ],
                          rows: [
                            ...issues.asMap().entries.map((entry) =>
                                _buildDataRow(entry.value, false, entry.key)),
                            ...subissues.asMap().entries.map((entry) =>
                                _buildDataRow(entry.value, true, entry.key)),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

              const SizedBox(height: 20),

              // Uploaded Images Section
              Card(
                elevation: 5,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      const Text(
                        'Uploaded Images/Videos',
                        style: TextStyle(
                          decoration: TextDecoration.underline,
                          fontSize: 18,
                        ),
                      ),
                      Column(
                        children: [
                          if (_isFileLoading)
                            const CircularProgressIndicator(
                                color: Colors.blueAccent),
                          if (!_isFileLoading && uploadedFiles.isNotEmpty)
                            ...uploadedFiles
                                .where((imageData) =>
                                    (imageData.imageUrls.isNotEmpty ?? false) ||
                                    (imageData.videoUrls?.isNotEmpty ?? false))
                                .map((imageData) {
                              return TripReportFilePreview(
                                  imageResponse: imageData,
                                  showStatus: false,
                                  journeyDate: widget.journeyDate,
                                  trainNumber: widget.trainNumber,
                                  coach: widget.coach,
                                  users: users,
                                  reportFor: "mcc_to_obhs");
                            }),
                        ],
                      )
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Selected Images Preview
              if (_selectedFiles.isNotEmpty)
                SizedBox(
                  height: 200,
                  child: GridView.builder(
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 4,
                      mainAxisSpacing: 4,
                    ),
                    itemCount: _selectedFiles.length,
                    itemBuilder: (context, index) {
                      final file = _selectedFiles[index];
                      final isVideo = _isVideo(file);
                      return Stack(
                        alignment: Alignment.topRight,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.grey[200],
                            ),
                            child: Center(
                              child: isVideo
                                  ? const Icon(Icons.videocam,
                                      size: 40, color: Colors.blue)
                                  : kIsWeb
                                      ? Image.network(file.path,
                                          fit: BoxFit.cover)
                                      : Image.file(File(file.path),
                                          fit: BoxFit.cover),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.cancel,
                                color: Colors.red, size: 20),
                            onPressed: () {
                              setState(() {
                                _selectedFiles.removeAt(index);
                              });
                            },
                          ),
                        ],
                      );
                    },
                  ),
                )
              else
                const Center(
                  child: Text(
                    'No Images selected',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                  ),
                ),

              const SizedBox(height: 20),

              // Image Picker Button
              ElevatedButton.icon(
                onPressed: _pickImage,
                icon: const Icon(Icons.photo_camera, color: Colors.blue),
                label: const Text('Pick Images'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black,
                  side: const BorderSide(color: Colors.black87, width: 0.5),
                ),
              ),

              const SizedBox(height: 10),

              // Comment Input Box
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 5.0, vertical: 5.0),
                  child: TextField(
                    controller: _commentController,
                    maxLines: 2,
                    style: const TextStyle(fontSize: 14),
                    decoration: InputDecoration(
                      hintText:
                          'Add your comments here (max 250 characters)...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Colors.grey[400]!),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide:
                            const BorderSide(color: Colors.blue, width: 2.0),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Colors.grey[400]!),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Upload Button
              ElevatedButton.icon(
                icon: isUploading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.blue,
                        ),
                      )
                    : const Icon(Icons.cloud_upload, color: Colors.blue),
                onPressed: isUploading ? null : _uploadImages,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: const BorderSide(color: Colors.black87, width: 0.5),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                label: isUploading
                    ? const Text('Uploading...',
                        style: TextStyle(fontSize: 15, color: Colors.black))
                    : const Text('Submit & Upload',
                        style: TextStyle(fontSize: 15, color: Colors.black)),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
