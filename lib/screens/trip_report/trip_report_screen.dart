import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_filters.dart';
import 'package:railops/screens/trip_report/widget/CoachButton.dart';
import 'package:railops/screens/trip_report/widget/CoachIssueImageUpload.dart';
import 'package:railops/screens/trip_report/widget/CoachIssueStatus.dart';
import 'package:railops/services/trip_report_services/trip_report_services.dart';
import 'package:railops/widgets/index.dart';

class TripReportScreen extends StatefulWidget {
  const TripReportScreen({super.key});

  @override
  _TripReportScreenState createState() => _TripReportScreenState();
}

class _TripReportScreenState extends State<TripReportScreen> {
  String? train;
  String? date;
  bool showLoader = false;
  List<String> coaches = [];
  Map<String, List<int>> selectedProblems = {};
  Map<int, String> problemOptions = {};
  bool isInitialLoad = true;
  List<dynamic> allIssues = [];

  @override
  void initState() {
    super.initState();
    _loadAllIssues();
  }

  Future<void> _loadAllIssues() async {
    setState(() {
      showLoader = true;
    });

    try {
      List<dynamic> response = await TripReportServices.getAllIssues();
      setState(() {
        allIssues = response;
        problemOptions = {
          for (var issue in response) issue["id"]: issue["name"]
        };
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                "${AppLocalizations.of(context).error_failed_to_load_issues}: ${e.toString()}")),
      );
    } finally {
      setState(() {
        showLoader = false;
      });
    }
  }

  void onSubmit(String trainNumber, String selectedDate) async {
    setState(() {
      train = trainNumber;
      date = selectedDate;
      showLoader = true;
    });

    try {
      final response =
          await TripReportServices.getTripReport(trainNumber, selectedDate);
      if (response.containsKey('data') &&
          response['data'].containsKey('coach_issues')) {
        setState(() {
          coaches = List<String>.from(response['data']['coach_issues'].keys);

          // Only replace selectedProblems on initial load
          if (isInitialLoad) {
            selectedProblems = {};
            response['data']['coach_issues'].forEach((coach, issues) {
              selectedProblems[coach] =
                  List<int>.from((issues as List).map((e) => e['id'] as int));
            });
            isInitialLoad = false;
          } else {
            // Update coaches list but preserve selected problems
            for (var coach in coaches) {
              if (!selectedProblems.containsKey(coach)) {
                selectedProblems[coach] = List<int>.from(
                    (response['data']['coach_issues'][coach] as List)
                        .map((e) => e['id'] as int));
              }
            }
          }
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  AppLocalizations.of(context).error_invalid_response_format)),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                "${AppLocalizations.of(context).error_failed_to_load_trip_report}: ${e.toString()}")),
      );
    } finally {
      setState(() {
        showLoader = false;
      });
    }
  }

  Future<void> _reloadPage() async {
    if (train != null && date != null) {
      setState(() {
        showLoader = true;
      });

      try {
        onSubmit(train!, date!);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(AppLocalizations.of(context)
                  .msg_data_refreshed_successfully)),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  "${AppLocalizations.of(context).error_refresh_failed}: ${e.toString()}")),
        );
      } finally {
        setState(() {
          showLoader = false;
        });
      }
    }
  }

  IconData getIconForProblem(int problemId) {
    // Get icon based on problem ID
    switch (problemId) {
      case 1:
        return Icons.door_front_door;
      case 2:
        return Icons.wash;
      case 3:
        return Icons.lock_outline;
      case 4:
        return Icons.plumbing_sharp;
      default:
        return Icons.help_outline;
    }
  }

  Widget _buildCoachList() {
    if (coaches.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              AppLocalizations.of(context).text_no_coaches_found,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              AppLocalizations.of(context).text_please_select_train_date,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: coaches.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        String coach = coaches[index];
        List<int> issueIds = selectedProblems[coach] ?? [];

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppLocalizations.of(context).text_coach_label(coach),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      issueIds.isEmpty
                          ? Text(
                              AppLocalizations.of(context)
                                  .text_no_issues_reported,
                              style: const TextStyle(
                                color: Colors.green,
                                fontStyle: FontStyle.italic,
                              ),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppLocalizations.of(context)
                                      .text_reported_issues,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                ...issueIds.map((id) => Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 4),
                                      child: Row(
                                        children: [
                                          const Icon(Icons.error_outline,
                                              color: Colors.orange, size: 16),
                                          const SizedBox(width: 8),
                                          Text(problemOptions[id] ??
                                              AppLocalizations.of(context)
                                                  .text_unknown_issue),
                                        ],
                                      ),
                                    )),
                              ],
                            ),
                      IconButton(
                        iconSize: 40,
                        icon: const Icon(Icons.upload, color: Colors.blue),
                        onPressed: () => _handleImageUpload(coach),
                        tooltip: AppLocalizations.of(context)
                            .tooltip_upload_coach_image,
                      ),
                    ]),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => _handleImageUpload(coach),
                  icon: const Icon(Icons.edit),
                  label: Text(AppLocalizations.of(context).btn_manage_issues),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _handleImageUpload(String coach) async {
    if (train == null || date == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                AppLocalizations.of(context).error_select_train_date_first)),
      );
      return;
    }

    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CoachIssueImageUploadPage(
          trainNumber: train!,
          journeyDate: date!,
          coach: coach,
          problems: problemOptions.entries
              .where((entry) =>
                  selectedProblems[coach]?.contains(entry.key) ?? false)
              .map((entry) =>
                  {'id': entry.key.toString(), 'problemName': entry.value})
              .toList(),
        ),
      ),
    );

    // Mark as not initial load to preserve selected problems
    isInitialLoad = false;

    // Refresh data after returning from image upload
    if (train != null && date != null) {
      onSubmit(train!, date!);
    }
  }

  Widget _buildCoachGrid() {
    if (coaches.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              AppLocalizations.of(context).text_no_coaches_found,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              AppLocalizations.of(context).text_please_select_train_date,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 2.0,
      ),
      itemCount: coaches.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        String coach = coaches[index];
        List<int> issueIds = selectedProblems[coach] ?? [];

        return CoachButton(
            coach: coach,
            issues: issueIds,
            // hasImages: coachHasImages[coach] ?? false,
            onTap: () => _handleImageUpload(coach),
            // onIssueEdit: () => _showProblemSelectionDialog(coach),
            problemOptions: problemOptions,
            isLast: index == coaches.length - 1);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
          title:
              AppLocalizations.of(context).text_train_rake_deficiency_report),
      drawer: const CustomDrawer(),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 5),
            child: Text(
                AppLocalizations.of(context).text_rake_deficiency_report_issues,
                style: const TextStyle(fontSize: 18)),
          ),
          Padding(
            padding: const EdgeInsets.all(5),
            child: AssignEhkCaFilters(
              onSubmit: (trainNumber, selectedDate) {
                if (trainNumber != null && selectedDate != null) {
                  setState(() {
                    // Reset for new train/date selection
                    isInitialLoad = true;
                  });
                  onSubmit(trainNumber, selectedDate);
                }
              },
            ),
          ),
          Expanded(
            child: showLoader
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                    onRefresh: _reloadPage,
                    child: _buildCoachGrid(),
                  ),
          ),
        ],
      ),
    );
  }
}
