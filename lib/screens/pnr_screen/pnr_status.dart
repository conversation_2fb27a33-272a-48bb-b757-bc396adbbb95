import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:railops/core/comman_widgets/app_button_with_icon.dart';
import 'package:railops/core/comman_widgets/input_fields/app_textfield.dart';
import 'package:railops/core/utilities/color_constants.dart';
import 'package:railops/core/utilities/comman_functions.dart';
import 'package:railops/services/profile_services/pnr_service.dart';
import 'package:railops/types/profile_types/pnr_response.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';

class PnrStatus extends StatefulWidget {
  const PnrStatus({super.key});

  @override
  State<PnrStatus> createState() => _PnrStatusState();
}

class _PnrStatusState extends State<PnrStatus> {
  final TextEditingController _pnrController = TextEditingController();
  bool _isLoading = false;
  PnrResponse? _pnrResponse;

  void checkPnr() async {
    if (_pnrController.text.isEmpty || _pnrController.text.length != 10) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content:
              Text(AppLocalizations.of(context).error_invalid_pnr_number)));
      return;
    }

    try {
      setState(() => _isLoading = true);
      //on your local use this
      // final token = await CommanFunctions().getToken();
      // final response = await PnrService.checkPNR(_pnrController.text, token);
      final response = await PnrService.checkPNR(_pnrController.text);
      if (response == null) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content:
                Text(AppLocalizations.of(context).error_failed_fetch_pnr)));
        return;
      }
      setState(() => _pnrResponse = response);
    } catch (e) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text(e.toString())));
    } finally {
      setState(() => _isLoading = false);
    }
  }

  String _formatUserList(List<dynamic>? userList) {
    if (userList == null || userList.isEmpty) return "-";
    return userList.join(", ");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          CustomAppBar(title: AppLocalizations.of(context).pnr_status_title),
      drawer: const CustomDrawer(),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(AppLocalizations.of(context).text_check_pnr_status,
                style: const TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: kTextColor)),
            const SizedBox(height: 30),
            AppTextField(
              controller: _pnrController,
              textInputType: TextInputType.phone,
              hint: AppLocalizations.of(context).hint_enter_pnr_number,
              maxLength: 10,
              label: Text(AppLocalizations.of(context).label_pnr_number),
              prefixIcon:
                  const Icon(Icons.confirmation_number, color: kPrimaryColor),
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: AppButtonWithIcon(
                  title: AppLocalizations.of(context).btn_check_pnr,
                  onTap: checkPnr,
                  fontColor: Colors.black,
                  buttonColor: Colors.white,
                  borderColor: Colors.black87),
            ),
            const SizedBox(height: 40),
            if (_isLoading) const CircularProgressIndicator(),
            if (!_isLoading && _pnrResponse == null)
              Text(AppLocalizations.of(context).text_no_pnr_data_found),
            if (_pnrResponse != null) buildPnrTable(),
          ],
        ),
      ),
    );
  }

  Widget buildPnrTable() {
    return Column(
      children: [
        Table(
          border: TableBorder.all(color: Colors.black),
          columnWidths: const {0: FractionColumnWidth(0.3)},
          children: [
            buildTableRow(AppLocalizations.of(context).label_train_number,
                _pnrResponse!.trainNumber),
            buildTableRow(AppLocalizations.of(context).label_train_name,
                _pnrResponse!.trainName),
            buildTableRow(AppLocalizations.of(context).label_boarding_date,
                _pnrResponse!.dateOfJourney),
            buildTableRow(AppLocalizations.of(context).label_from,
                _pnrResponse!.journeyFrom),
            buildTableRow(
                AppLocalizations.of(context).label_to, _pnrResponse!.journeyTo),
            buildTableRow(AppLocalizations.of(context).label_class,
                _pnrResponse!.journeyClass),
            buildTableRow(AppLocalizations.of(context).label_departure,
                _pnrResponse!.departureTime),
            buildTableRow(AppLocalizations.of(context).label_arrival,
                _pnrResponse!.arrivalTime),
            buildTableRow(AppLocalizations.of(context).label_overall_status,
                _pnrResponse!.overallStatus),
            buildTableRow(AppLocalizations.of(context).label_booking_date,
                _pnrResponse!.bookingDate),
          ],
        ),

        const SizedBox(height: 20),
        Table(
          border: TableBorder.all(color: Colors.black),
          columnWidths: const {0: FractionColumnWidth(0.5)},
          children: [
            buildTableRow(AppLocalizations.of(context).label_passenger,
                AppLocalizations.of(context).label_coach_berth),
            ..._buildPassengerRows(),
          ],
        ),

        const SizedBox(height: 20),

        // Service Staff Table
        Table(
          border: TableBorder.all(color: Colors.black),
          children: [
            buildTableRow(AppLocalizations.of(context).label_ehk_users,
                _formatUserList(_pnrResponse!.ehkUsernames)),
            buildTableRow(AppLocalizations.of(context).label_ca_users,
                _formatUserList(_pnrResponse!.caUsernames)),
            buildTableRow(AppLocalizations.of(context).label_obhs_users,
                _formatUserList(_pnrResponse!.obhsUsernames)),
          ],
        ),

        const SizedBox(height: 20),
        Table(
          border: TableBorder.all(color: Colors.black),
          children: [
            buildTableRow(AppLocalizations.of(context).label_ehk_users,
                _formatUserList(_pnrResponse!.ehkUsernames)),
            buildTableRow(AppLocalizations.of(context).label_ca_users,
                _formatUserList(_pnrResponse!.caUsernames)),
            buildTableRow(AppLocalizations.of(context).label_obhs_users,
                _formatUserList(_pnrResponse!.obhsUsernames)),
          ],
        ),
      ],
    );
  }

  List<TableRow> _buildPassengerRows() {
    return List.generate(_pnrResponse!.passengerNames.length, (index) {
      String passengerName = _pnrResponse!.passengerNames[index];

      // Special case handling for specific trains (as in your original code)
      if (passengerName == "1" &&
          (_pnrResponse!.trainNumber == "12334" ||
              _pnrResponse!.trainNumber == "22213")) {
        passengerName = "Atul Anand";
      }

      return buildTableRow(passengerName,
          "${_pnrResponse!.passengerCoaches[index]} / ${_pnrResponse!.passengerBerths[index]}");
    });
  }

  TableRow buildTableRow(String label, String value) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child:
              Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(value),
        ),
      ],
    );
  }
}
