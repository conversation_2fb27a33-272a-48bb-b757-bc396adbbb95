#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix duplicate keys in ARB file that are causing Flutter localization crash.
This script will:
1. Read the ARB file as text (not JSON to preserve duplicates)
2. Identify duplicate keys
3. Rename duplicates to make them unique
4. Fix placeholder syntax from $variable to {variable}
5. Generate a clean ARB file
"""

import json
import re
import sys
from collections import defaultdict

def fix_arb_file(input_file, output_file):
    """Fix duplicate keys and placeholder syntax in ARB file."""

    print(f"Reading ARB file: {input_file}")

    # Read the file content as text to preserve duplicates
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    print("Processing lines to fix duplicates and placeholders...")

    # Track key occurrences
    key_counts = defaultdict(int)
    processed_lines = []
    i = 0

    while i < len(lines):
        line = lines[i]

        # Check if this line defines a key (not metadata)
        key_match = re.match(r'^  "([^@][^"]*)":', line)
        if key_match:
            key = key_match.group(1)
            key_counts[key] += 1

            # If this is a duplicate, rename it
            if key_counts[key] > 1:
                new_key = f"{key}_{key_counts[key]}"
                print(f"Renaming duplicate key: {key} -> {new_key}")
                line = line.replace(f'"{key}":', f'"{new_key}":')

                # Also update the corresponding metadata line
                if i + 1 < len(lines) and lines[i + 1].strip().startswith(f'"@{key}":'):
                    lines[i + 1] = lines[i + 1].replace(f'"@{key}":', f'"@{new_key}":')

            # Fix placeholder syntax in the value
            if '$' in line:
                original_line = line
                line = re.sub(r'\$([a-zA-Z_][a-zA-Z0-9_]*)', r'{\1}', line)
                if line != original_line:
                    print(f"Fixed placeholder syntax in line {i+1}")

        processed_lines.append(line)
        i += 1

    # Write the processed content
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(processed_lines)

    print(f"Writing cleaned ARB file: {output_file}")

    # Validate the result by parsing as JSON
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            json.loads(f.read())
        print("✅ Generated file is valid JSON!")
    except json.JSONDecodeError as e:
        print(f"❌ Generated file has JSON errors: {e}")
        return False

    duplicates_fixed = sum(1 for count in key_counts.values() if count > 1)
    print(f"Successfully fixed ARB file!")
    print(f"Total lines processed: {len(processed_lines)}")
    print(f"Duplicates fixed: {duplicates_fixed}")

    return True

if __name__ == "__main__":
    input_file = "lib/l10n/app_en.arb"
    output_file = "lib/l10n/app_en_fixed.arb"
    
    if fix_arb_file(input_file, output_file):
        print("\nARB file has been fixed!")
        print(f"Backup original: mv {input_file} {input_file}.backup")
        print(f"Use fixed file: mv {output_file} {input_file}")
    else:
        print("Failed to fix ARB file!")
        sys.exit(1)
